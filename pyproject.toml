[project]
name = "paic-python-starter"
version = "0.1.1"
description = "PAIC Python Starter"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aider-chat>=0.65.0",
    "anthropic>=0.39.0",
    "google-generativeai>=0.8.3",
    "llm>=0.18",
    "llm-claude-3>=0.9",
    "llm-gemini>=0.4.2",
    "ollama>=0.4.1",
    "openai>=1.55.1",
    "pydantic>=2.10.2",
    "pytest>=8.3.3",
    "python-dotenv>=1.0.1",
    "pyyaml>=6.0.2",
    "typer>=0.13.1",
    "rich>=13.9.4",
]
