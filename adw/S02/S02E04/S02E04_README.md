# S02E04: Factory Reports Classification

**Author:** Augment Agent
**Date:** May 24, 2025
**Version:** 1.0.0

## 🚀 Overview

This Python-based Single File Agent (SFA) is designed to classify factory reports. It processes various file types (TXT, PNG, MP3) found in a specified directory, extracts their content, and then uses a Large Language Model (LLM) to categorize each report as either "PEOPLE" (related to human presence or captures) or "HARDWARE" (related to equipment repairs). The final classified list of files is then submitted to a central API.

The agent leverages external services: Kluster.ai for image OCR and LLM-based classification, and AssemblyAI for audio transcription. It includes features for caching intermediate processing steps to JSON files, allowing for resumption and modular execution.

## ✨ Features

* **Multimodal File Processing**:
  * **TXT Files**: Reads text content, attempting multiple encodings (UTF-8, CP1252, Latin1).
  * **PNG Files**: Performs Optical Character Recognition (OCR) using Kluster.ai's vision model (`google/gemma-3-27b-it`). Includes image optimization for large files.
  * **MP3 Files**: Transcribes audio content using AssemblyAI.
* **File Discovery**: Scans a configurable base directory (`BASE_PATH`) for supported files, with options to exclude specific files and directories.
* **Intermediate Caching**: Saves extracted content from TXT, PNG, and MP3 files into `txt.json`, `png.json`, and `mp3.json` respectively. This prevents reprocessing if the script is run multiple times.
* **LLM-based Classification**:
  * Utilizes a system prompt (from `S02E04_sys_prompt.txt`) to guide the classification task.
  * Classifies each file's content individually using an LLM (e.g., `deepseek-ai/DeepSeek-V3-0324` via Kluster.ai).
  * Parses LLM responses to identify "PEOPLE" or "HARDWARE" categories.
* **Result Aggregation**: Consolidates all classified files (PEOPLE or HARDWARE) into a `final_answer.json` file.
* **API Interaction**:
  * Submits the `final_answer.json` to a central reporting API (AIDEVS).
  * Checks the API response for a "flag".
* **Configuration**: Uses environment variables (`.env` file) for API keys and other settings.
* **Robustness**: Implements retry logic for API calls and detailed logging.
* **Modular Execution**: Supports command-line arguments for running specific parts of the workflow:
  * `--sound`: Process only MP3 files.
  * `--image`: Process only PNG files.
  * `--central`: Submit already processed results from `final_answer.json`.

## ⚙️ Workflow

The agent operates through the following main steps:

1. **Initialization**:
    * Loads environment variables from `.env` (API keys, base URLs).
    * Validates essential configurations (API keys).
    * Initializes the AssemblyAI client.
    * Loads the system prompt from `S02E04_sys_prompt.txt`.

2. **File Discovery**:
    * Scans the `BASE_PATH` (default: `/Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S02/pliki_z_fabryki`) for `.txt`, `.png`, and `.mp3` files.
    * Applies exclusion rules for specified files and directories.

3. **Data Extraction & Preprocessing (with Caching)**:
    * **For TXT files**: Reads text content. Results are incrementally saved to `txt.json`. This step is skipped if `txt.json` is already complete for the discovered files.
    * **For PNG files**:
        * Opens, optimizes (if large), and converts images to base64.
        * Calls the Kluster.ai vision API for OCR.
        * Results are incrementally saved to `png.json`. Skipped if `png.json` is complete.
    * **For MP3 files**:
        * Calls AssemblyAI for audio transcription.
        * Results are incrementally saved to `mp3.json`. Skipped if `mp3.json` is complete.

4. **Content Classification**:
    * Loads all extracted content from `txt.json`, `png.json`, and `mp3.json`.
    * For each file's content:
        * Constructs a prompt using the system prompt and the file's content.
        * Sends the prompt to the Kluster.ai chat completions API (using `CLASSIFICATION_MODEL`).
        * Parses the LLM's response to determine the category ("PEOPLE" or "HARDWARE"). Files not fitting these categories (or explicitly marked "OTHER" by the LLM based on the system prompt) are effectively ignored for the final submission.
    * Aggregates filenames into `{"people": [...], "hardware": [...]}`.
    * Saves this structure to `final_answer.json`.

5. **Submission & Flag Check**:
    * Constructs a payload with the task name ("kategorie"), API key, and the classification result from `final_answer.json`.
    * Sends this payload to the AIDEVS `REPORT_ENDPOINT`.
    * Logs the server's response and checks for a "flag" (e.g., `FLG...` or `{{FLG:...}}`).

### Modular Workflows

* **`python S02E04_sfa.py --sound`**: Executes steps 1, 2 (for MP3s), and 3 (MP3 processing only).
* **`python S02E04_sfa.py --image`**: Executes steps 1, 2 (for PNGs), and 3 (PNG processing only).
* **`python S02E04_sfa.py --central`**: Loads an existing `final_answer.json` and proceeds directly to step 5 (Submission & Flag Check).
* **`python S02E04_sfa.py`** (no arguments): Runs the full workflow (steps 1-5).

## 📄 System Prompt

The classification logic heavily relies on the system prompt defined in `S02E04_sys_prompt.txt`. This file instructs the LLM on:

* The categories: PEOPLE, HARDWARE, OTHER.
* Specific criteria for each category (e.g., "PEOPLE" for captured individuals or traces, "HARDWARE" for equipment repairs, excluding software).
* The analysis process, including scoring and providing justifications (though the script primarily extracts the final category).
* The expected output format for classification (e.g., `<filename>...</filename>:<category>...</category>`).

## 📋 Prerequisites

* Python 3.7+
* Access to the internet for API calls.
* API Keys for:
  * AIDEVS (Central API for submission)
  * Kluster.ai (for LLM and Vision Models)
  * AssemblyAI (for Audio Transcription)
* Input files (TXT, PNG, MP3) located in the directory specified by `BASE_PATH` in the script (default: `/Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S02/pliki_z_fabryki`).

## 🛠️ Setup

1. **Clone the repository or download the files**:
    Ensure you have `S02E04_sfa.py` and `S02E04_sys_prompt.txt` in the same directory.

    ```bash
    # Example:
    # cd /path/to/your/project/S02E04
    ```

2. **Install dependencies**:
    Create a `requirements.txt` file in the same directory with the following content:

    ```txt
    python-dotenv
    requests
    assemblyai
    Pillow
    ```

    Then install them:

    ```bash
    pip install -r requirements.txt
    ```

3. **Set up Environment Variables**:
    Create a `.env` file in the same directory as `S02E04_sfa.py` with your API keys and other configurations:

    ```env
    AIDEVS_API_KEY="your_aidevs_api_key"
    KLUSTER_API_KEY="your_kluster_api_key"
    ASSEMBLY_API_KEY="your_assemblyai_api_key"

    # Optional: Override default API base URLs or settings
    # API_BASE_URL="https://c3ntrala.ag3nts.org"
    # KLUSTER_BASE_URL="https://api.kluster.ai/v1"
    # REQUEST_TIMEOUT="30"
    # RETRY_ATTEMPTS="1" # Note: Class uses MAX_RETRIES = 1
    # RETRY_DELAY="2"
    ```

    Replace `"your_..._api_key"` with your actual API keys.

## 🚀 Usage

Navigate to the directory containing `S02E04_sfa.py` and run the script from your terminal.

### Full Workflow

To discover files, process them, classify content, and submit to the central API:

```bash
python S02E04_sfa.py
```

### Process Audio Files Only

To discover and process only MP3 files (transcription and saving to `mp3.json`):

```bash
python S02E04_sfa.py --sound
```

### Process Image Files Only

To discover and process only PNG files (OCR and saving to `png.json`):

```bash
python S02E04_sfa.py --image
```

### Submit Cached Results Only

If `final_answer.json` already exists (e.g., from a previous run), you can submit its content directly:

```bash
python S02E04_sfa.py --central
```

## 💾 Output Files

The script generates the following files in the directory where it is run:

* `txt.json`: Contains extracted text content from `.txt` files.

    ```json
    [
      {"name": "report1.txt", "content": "Text content of report1..."},
      // ... more entries
    ]
    ```

* `png.json`: Contains OCRed text from `.png` files.

    ```json
    [
      {"name": "image_report.png", "content": "Recognized text from image..."},
      // ... more entries
    ]
    ```

* `mp3.json`: Contains transcribed text from `.mp3` files.

    ```json
    [
      {"name": "audio_log.mp3", "content": "Transcribed audio text..."},
      // ... more entries
    ]
    ```

* `final_answer.json`: The final aggregated classification ready for submission.

    ```json
    {
      "people": ["file1.txt", "image_report.png"],
      "hardware": ["maintenance_log.mp3"]
    }
    ```

These JSON files (except `final_answer.json`) serve as a cache. If they exist and contain all necessary entries for the discovered files, the corresponding processing step (TXT reading, PNG OCR, MP3 transcription) will be skipped.
