# S02E04 - <PERSON><PERSON>za <PERSON>ada<PERSON>: Klasyfikacja Raportów z Fabryki

## Opis Zadania

### Cel Główny
Przeanalizowanie danych z fabryki w różnych formatach (TXT, PNG, MP3) i wyodrębnienie dwóch kategorii informacji:
1. **People** - notatki zawierające informacje o schwytanych ludziach lub śladach ich obecności
2. **Hardware** - informacje o naprawionych usterkach hardwarowych (wykluczając software)

### Dane Źródłowe
- **Źródło**: https://c3ntrala.ag3nts.org/dane/pliki_z_fabryki.zip
- **Formaty plików**: TXT, PNG, MP3
- **Ograniczenia**: Pominąć folder "facts", pliki bez rozszerzenia i `weapons_tests.zip`

### Wymagania Wyjściowe
- Format JSON z alfabetycznie posortowanymi nazwami plików
- Endpoint: POST na https://c3ntrala.ag3nts.org/report
- Struktura odpowiedzi:
```json
{
  "task": "kategorie",
  "apikey": "YOUR_API_KEY",
  "answer": {
    "people": ["plik1.txt", "plik2.mp3", "plikN.png"],
    "hardware": ["plik4.txt", "plik5.png", "plik6.mp3"]
  }
}
```

## Analiza Wyzwań Technicznych

### 1. Przetwarzanie Multimodalne
- **TXT**: Bezpośredni odczyt tekstu
- **PNG**: Wymagane OCR lub modele multimodalne (GPT-4V, Claude 3.5 Sonnet)
- **MP3**: Transkrypcja mowy na tekst (Whisper API)

### 2. Klasyfikacja Treści
- Konieczność analizy semantycznej wyekstrahowanego tekstu
- Rozróżnienie między hardware a software
- Identyfikacja informacji o ludziach vs. innych tematów

### 3. Ograniczenia i Wyzwania
- **Case-sensitive nazwy plików** - krytyczne dla poprawności
- **Koszty API** - szczególnie dla analizy obrazów
- **Limity TPM** - potencjalne ograniczenia OpenAI
- **Jakość ekstrakcji** - różna dla różnych formatów

## Proponowany Stack Technologiczny

### Język Programowania
**Python 3.12+** - optymalny wybór ze względu na:
- Bogate biblioteki do przetwarzania mediów
- Wsparcie dla AI/ML APIs
- Łatwość obsługi HTTP requests

### Biblioteki Główne
```python
# Przetwarzanie danych
requests          # HTTP requests, pobieranie ZIP
zipfile           # Rozpakowywanie archiwów
os, pathlib       # Operacje na plikach

# Przetwarzanie obrazów
Pillow (PIL)      # Podstawowa obróbka obrazów
pytesseract       # OCR (alternatywa dla API)

# APIs
openai            # GPT-4V, Whisper
anthropic         # Claude 3.5 Sonnet (alternatywa)

# Utilities
json              # Formatowanie odpowiedzi
typing            # Type hints
dataclasses       # Struktury danych
```

### Alternatywne Rozwiązania API
1. **OpenAI**: GPT-4V (obrazy) + Whisper (audio)
2. **Anthropic**: Claude 3.5 Sonnet (multimodal)
3. **Google**: Gemini 2.0 Flash (multimodal)
4. **OpenRouter**: Dostęp do różnych modeli

### OCR Alternatywy (oszczędność kosztów)
- **Tesseract** - darmowy, lokalny OCR
- **EasyOCR** - Python library, dobra jakość
- **Azure Computer Vision** - konkurencyjne ceny

## Plan Implementacji

### Faza 1: Przygotowanie Środowiska
1. **Setup projektu**
   - Utworzenie struktury katalogów
   - Konfiguracja dependencies (pyproject.toml)
   - Setup environment variables (API keys)

2. **Pobieranie i ekstrakcja danych**
   - Download ZIP archive
   - Rozpakowanie i filtrowanie plików
   - Utworzenie listy plików do przetworzenia

### Faza 2: Ekstrakcja Treści
1. **Moduł przetwarzania TXT**
   - Bezpośredni odczyt z encoding detection
   - Error handling dla corrupted files

2. **Moduł przetwarzania PNG**
   - Pipeline: OCR lokalny → fallback do API multimodal
   - Caching wyników aby uniknąć re-processing
   - Optymalizacja rozmiaru obrazów przed wysłaniem

3. **Moduł przetwarzania MP3**
   - Whisper API integration
   - Chunking długich plików audio
   - Retry logic dla network errors

### Faza 3: Klasyfikacja i Kategoryzacja
1. **Prompt Engineering**
   - Opracowanie skutecznych promptów dla klasyfikacji
   - Few-shot examples dla lepszej precyzji
   - Chain-of-thought reasoning

2. **Logika klasyfikacji**
   - Unified classifier dla wszystkich typów treści
   - Confidence scoring
   - Edge cases handling

### Faza 4: Finalizacja i Wysyłka
1. **Formatowanie odpowiedzi**
   - Alfabetyczne sortowanie
   - Walidacja struktury JSON
   - Final review przed wysyłką

2. **Wysyłka do API**
   - POST request z proper headers
   - Error handling i retry logic
   - Response validation

## Strategia Optymalizacji Kosztów

### 1. Caching i Persistence
```python
# Struktura cache
cache/
├── extracted_texts/        # Teksty z OCR/Whisper
├── classifications/        # Wyniki klasyfikacji
└── metadata.json         # Tracking processed files
```

### 2. Batch Processing
- Grupowanie podobnych plików
- Parallel processing gdzie możliwe
- Rate limiting dla API calls

### 3. Fallback Strategy
- Tesseract OCR jako backup dla obrazów
- Degraded mode przy brakach API
- Local models jako ostateczność

## Struktura Kodu

```
S02E04/
├── S02E04_analiza.md           # Ten dokument
├── S02E04_sfa.py              # Main implementation
├── S02E04_sys_prompt.txt      # System prompts
├── S02E04_IM_plan.md          # Implementation plan
├── modules/
│   ├── __init__.py
│   ├── data_extractor.py      # TXT/PNG/MP3 processing
│   ├── classifier.py          # Content classification
│   ├── api_client.py          # API communications
│   └── utils.py               # Helpers
├── cache/                     # Cached results
├── data/                      # Downloaded ZIP content
└── config/
    ├── prompts.yaml          # Classification prompts
    └── settings.yaml         # API configs
```

## Risk Assessment

### Wysokie Ryzyko
- **API costs** - szczególnie dla dużej liczby obrazów
- **Rate limits** - OpenAI TPM restrictions
- **Classification accuracy** - false positives/negatives

### Średnie Ryzyko
- **File format corruption** - niektóre pliki mogą być uszkodzone
- **Encoding issues** - problemy z non-ASCII characters
- **Network instability** - timeouts podczas API calls

### Niskie Ryzyko
- **ZIP extraction** - standardowa operacja
- **JSON formatting** - well-defined structure
- **File sorting** - straightforward algorithm

## Metryki Sukcesu

1. **Accuracy**: >95% poprawnej klasyfikacji
2. **Performance**: Przetwarzanie w <30 minut
3. **Cost efficiency**: <$10 total API costs
4. **Reliability**: 100% successful submission

## Następne Kroki

1. **Setup environment** - dependencies i API keys
2. **Prototype data extraction** - test z sample files
3. **Develop classification logic** - prompt engineering
4. **Integration testing** - end-to-end workflow
5. **Production run** - final execution

---

**Status**: Analiza kompletna ✅  
**Next**: Implementation Plan (S02E04_IM_plan.md)
