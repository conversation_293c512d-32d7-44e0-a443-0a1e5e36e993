"""
Single File Agent - S02E04: Factory Reports Classification
Klasyfikacja raportów z fabryki w formatach TXT, PNG, MP3

Author: Augment Agent
Date: May 24, 2025
Version: 1.0.0
"""

# --- IMPORTS ---
import os
import json
import argparse
import base64
import re
import time
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union
from dotenv import load_dotenv

import requests
import assemblyai as aai
from PIL import Image
import io
from openai import OpenAI, APIError, APIConnectionError, RateLimitError

# --- CONFIGURATION ---
# Load environment variables
load_dotenv()

# --- APPLICATION SETTINGS ---
TASK_NAME = "kategorie"
API_BASE_URL = os.getenv("API_BASE_URL", "https://c3ntrala.ag3nts.org")
REPORT_ENDPOINT = f"{API_BASE_URL}/report"
API_KEY = os.getenv("AIDEVS_API_KEY")

# Request settings
REQUEST_TIMEOUT = 30  # seconds
RETRY_ATTEMPTS = 1
RETRY_DELAY = 2  # seconds

# --- LLM SETTINGS ---
# Kluster.ai settings
KLUSTER_API_KEY = os.getenv("KLUSTER_API_KEY")
KLUSTER_BASE_URL = os.getenv("KLUSTER_BASE_URL", "https://api.kluster.ai/v1")

# AssemblyAI settings
ASSEMBLY_API_KEY = os.getenv("ASSEMBLY_API_KEY")

# Models
IMAGE_MODEL = "google/gemma-3-27b-it"
CLASSIFICATION_MODEL = "deepseek-ai/DeepSeek-V3-0324"

# System prompt path
SYSTEM_PROMPT_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "S02E04_sys_prompt.txt")

# --- REGEX PATTERNS ---
FLAG_REGEX = r"FLG[a-zA-Z0-9_-]+"
FLAG_REGEX_CURLY = r"\{\{FLG:[a-zA-Z0-9_-]+\}\}"

# --- LOGGING CONFIGURATION ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

class FactoryReportsProcessor:
    """
    Single File Agent for factory reports classification
    """
    
    # Configuration constants
    MAX_RETRIES = 1
    SUPPORTED_EXTENSIONS = {'.txt', '.png', '.mp3'}
    EXCLUDED_FILES = {'weapons_tests.zip', '2024-11-12_report-99'}
    EXCLUDED_DIRS = {'facts'}
    
    # File paths
    BASE_PATH = "/Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S02/pliki_z_fabryki"
    
    # Output files
    JSON_FILES = {
        '.txt': 'txt.json',
        '.png': 'png.json',
        '.mp3': 'mp3.json'
    }
    
    # Final answer file
    FINAL_ANSWER_FILE = 'final_answer.json'
    
    def __init__(self):
        """Initialize the processor"""
        self.validate_configuration()
        
        # Initialize AssemblyAI
        aai.settings.api_key = ASSEMBLY_API_KEY
        
        # Load system prompt
        self.system_prompt = self.load_system_prompt()
        
    def validate_configuration(self) -> bool:
        """Validates required configuration"""
        if not API_KEY:
            logger.critical("AIDEVS_API_KEY is not set")
            return False
            
        if not KLUSTER_API_KEY:
            logger.critical("KLUSTER_API_KEY is not set")
            return False
            
        logger.info("Configuration validation successful")
        return True
        
    def load_system_prompt(self) -> str:
        """Load system prompt from file"""
        try:
            with open(SYSTEM_PROMPT_PATH, 'r', encoding='utf-8') as file:
                system_prompt = file.read().strip()
            logger.info(f"System prompt loaded from: {SYSTEM_PROMPT_PATH}")
            return system_prompt
        except Exception as e:
            logger.error(f"Error loading system prompt: {e}")
            return "You are a factory reports classifier."
            
    def discover_files(self, base_path: str = None) -> Dict[str, List[str]]:
        """
        Discover and categorize files by extension
        """
        if base_path is None:
            base_path = self.BASE_PATH
            
        logger.info(f"Discovering files in: {base_path}")
        
        files_by_ext = {'.txt': [], '.png': [], '.mp3': []}
        
        try:
            for item in Path(base_path).iterdir():
                # Skip excluded directories
                if item.is_dir() and item.name in self.EXCLUDED_DIRS:
                    logger.info(f"Skipping excluded directory: {item.name}")
                    continue
                    
                # Skip excluded files
                if item.name in self.EXCLUDED_FILES:
                    logger.info(f"Skipping excluded file: {item.name}")
                    continue
                    
                # Process only files with supported extensions
                if item.is_file() and item.suffix in self.SUPPORTED_EXTENSIONS:
                    if self.validate_file(item):
                        files_by_ext[item.suffix].append(item.name)
                        logger.info(f"Found valid file: {item.name}")
                    else:
                        logger.warning(f"Invalid file (empty/corrupted): {item.name}")
                        
        except Exception as e:
            logger.error(f"Error discovering files: {e}")
            
        # Log summary
        for ext, files in files_by_ext.items():
            logger.info(f"Found {len(files)} {ext} files")
            
        return files_by_ext
        
    def validate_file(self, filepath: Path) -> bool:
        """Validate file is not empty or corrupted"""
        try:
            return filepath.exists() and filepath.stat().st_size > 0
        except Exception:
            return False
            
    def is_json_complete(self, json_file: str, expected_files: List[str]) -> bool:
        """Check if JSON file contains all expected entries"""
        try:
            if not os.path.exists(json_file):
                return False
                
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            if not isinstance(data, list):
                return False
                
            # Check count and names
            json_names = {item.get('name') for item in data if isinstance(item, dict)}
            expected_names = set(expected_files)
            
            return json_names == expected_names
            
        except Exception as e:
            logger.error(f"Error checking JSON completeness for {json_file}: {e}")
            return False
            
    def process_txt_files(self, txt_files: List[str]) -> None:
        """Process TXT files"""
        json_file = self.JSON_FILES['.txt']
        
        if self.is_json_complete(json_file, txt_files):
            logger.info(f"TXT processing already complete: {json_file}")
            return
            
        logger.info(f"Processing {len(txt_files)} TXT files")
        results = []
        
        for filename in txt_files:
            filepath = Path(self.BASE_PATH) / filename
            
            try:
                # Try different encodings
                content = None
                for encoding in ['utf-8', 'cp1252', 'latin1']:
                    try:
                        with open(filepath, 'r', encoding=encoding) as f:
                            content = f.read().strip()
                        break
                    except UnicodeDecodeError:
                        continue
                        
                if content is None:
                    logger.error(f"Could not decode file: {filename}")
                    continue
                    
                result = {
                    "name": filename,
                    "content": content
                }
                results.append(result)
                
                # Save immediately for safety
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)
                    
                logger.info(f"Processed TXT file: {filename}")
                
            except Exception as e:
                logger.error(f"Error processing TXT file {filename}: {e}")
                
        logger.info(f"TXT processing complete. Saved {len(results)} files to {json_file}")
        
    def call_kluster_vision_api(self, image_b64: str, prompt: str) -> str:
        """Call Kluster.ai vision API for OCR"""
        headers = {
            "Authorization": f"Bearer {KLUSTER_API_KEY}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": IMAGE_MODEL,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/png;base64,{image_b64}"}
                        }
                    ]
                }
            ],
            "max_tokens": 4000
        }
        
        response = requests.post(
            f"{KLUSTER_BASE_URL}/chat/completions",
            headers=headers,
            json=payload,
            timeout=REQUEST_TIMEOUT
        )
        response.raise_for_status()
        
        result = response.json()
        return result["choices"][0]["message"]["content"].strip()
        
    def process_png_files(self, png_files: List[str]) -> None:
        """Process PNG files with OCR"""
        json_file = self.JSON_FILES['.png']
        
        if self.is_json_complete(json_file, png_files):
            logger.info(f"PNG processing already complete: {json_file}")
            return
            
        logger.info(f"Processing {len(png_files)} PNG files")
        results = []
        
        ocr_prompt = "Rozpoznaj i wypisz w swojej odpowiedzi kompletny tekst z obrazu. Nie dodawaj żadnej innej treści - tylko rozpoznany tekst."
        
        for filename in png_files:
            filepath = Path(self.BASE_PATH) / filename
            
            try:
                # Load and encode image
                with Image.open(filepath) as img:
                    # Convert to RGB if needed
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                        
                    # Optimize size if too large
                    if img.size[0] * img.size[1] > 2000000:  # ~2MP
                        img.thumbnail((1500, 1500), Image.Resampling.LANCZOS)
                        
                    # Convert to base64
                    buffer = io.BytesIO()
                    img.save(buffer, format='PNG')
                    image_b64 = base64.b64encode(buffer.getvalue()).decode()
                    
                # Call OCR API with retry
                content = None
                for attempt in range(self.MAX_RETRIES + 1):
                    try:
                        content = self.call_kluster_vision_api(image_b64, ocr_prompt)
                        break
                    except Exception as e:
                        logger.error(f"OCR attempt {attempt + 1} failed for {filename}: {e}")
                        if attempt == self.MAX_RETRIES:
                            raise
                        time.sleep(RETRY_DELAY)
                        
                if content:
                    result = {
                        "name": filename,
                        "content": content
                    }
                    results.append(result)
                    
                    # Save immediately for safety
                    with open(json_file, 'w', encoding='utf-8') as f:
                        json.dump(results, f, ensure_ascii=False, indent=2)
                        
                    logger.info(f"Processed PNG file: {filename}")
                    
            except Exception as e:
                logger.error(f"Error processing PNG file {filename}: {e}")
                
        logger.info(f"PNG processing complete. Saved {len(results)} files to {json_file}")
        
    def transcribe_audio_assemblyai(self, audio_path: str) -> str:
        """Transcribe audio using AssemblyAI"""
        config = aai.TranscriptionConfig(speech_model=aai.SpeechModel.best)
        transcript = aai.Transcriber(config=config).transcribe(audio_path)
        
        if transcript.status == "error":
            raise RuntimeError(f"Transcription failed: {transcript.error}")
            
        return transcript.text
        
    def process_mp3_files(self, mp3_files: List[str]) -> None:
        """Process MP3 files with transcription"""
        json_file = self.JSON_FILES['.mp3']
        
        if self.is_json_complete(json_file, mp3_files):
            logger.info(f"MP3 processing already complete: {json_file}")
            return
            
        logger.info(f"Processing {len(mp3_files)} MP3 files")
        results = []
        
        for filename in mp3_files:
            filepath = str(Path(self.BASE_PATH) / filename)
            
            try:
                # Transcribe with retry
                content = None
                for attempt in range(self.MAX_RETRIES + 1):
                    try:
                        content = self.transcribe_audio_assemblyai(filepath)
                        break
                    except Exception as e:
                        logger.error(f"Transcription attempt {attempt + 1} failed for {filename}: {e}")
                        if attempt == self.MAX_RETRIES:
                            raise
                        time.sleep(RETRY_DELAY)
                        
                if content:
                    result = {
                        "name": filename,
                        "content": content
                    }
                    results.append(result)
                    
                    # Save immediately for safety
                    with open(json_file, 'w', encoding='utf-8') as f:
                        json.dump(results, f, ensure_ascii=False, indent=2)
                        
                    logger.info(f"Processed MP3 file: {filename}")
                    
            except Exception as e:
                logger.error(f"Error processing MP3 file {filename}: {e}")
                
        logger.info(f"MP3 processing complete. Saved {len(results)} files to {json_file}")
        
    def classify_single_file(self, filename: str, content: str) -> Optional[str]:
        """Classify a single file using LLM and return category (PEOPLE/HARDWARE) or None"""
        headers = {
            "Authorization": f"Bearer {KLUSTER_API_KEY}",
            "Content-Type": "application/json"
        }
        
        # Prepare content for classification
        content_text = f"Plik do klasyfikacji:\n\nPlik: {filename}\nTreść: {content}"
            
        payload = {
            "model": CLASSIFICATION_MODEL,
            "messages": [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": content_text}
            ],
            "max_tokens": 4000,
            "temperature": 0.1
        }
        
        response = requests.post(
            f"{KLUSTER_BASE_URL}/chat/completions",
            headers=headers,
            json=payload,
            timeout=REQUEST_TIMEOUT
        )
        response.raise_for_status()
        
        result = response.json()
        response_text = result["choices"][0]["message"]["content"].strip()
        
        logger.info(f"LLM Classification Response for {filename}: {response_text}")
        
        # Parse response to extract category
        return self.parse_single_classification_response(response_text, filename)
    
    def parse_single_classification_response(self, response_text: str, filename: str) -> Optional[str]:
        """Parse LLM response for a single file to extract category"""
        try:
            logger.info(f"Parsing response for {filename}: {response_text[:300]}...")
            
            # Method 1: Look for XML-like pattern <filename>...</filename>:<category>...</category>
            pattern = r'<filename>([^<]+)</filename>:\s*<category>(PEOPLE|HARDWARE)</category>'
            matches = re.findall(pattern, response_text, re.IGNORECASE)
            
            for found_filename, category in matches:
                # Check if filename matches (case insensitive)
                if found_filename.strip().lower() == filename.lower():
                    logger.info(f"✓ Found XML classification for {filename}: {category}")
                    return category.upper()
            
            # Method 2: Look for category tags only (more flexible)
            category_pattern = r'<category>(PEOPLE|HARDWARE)</category>'
            category_match = re.search(category_pattern, response_text, re.IGNORECASE)
            if category_match:
                category = category_match.group(1).upper()
                logger.info(f"✓ Found category tag for {filename}: {category}")
                return category
                
            # Method 3: Look for simpler pattern "filename: CATEGORY"
            simple_pattern = rf'{re.escape(filename)}\s*:\s*(PEOPLE|HARDWARE)'
            simple_match = re.search(simple_pattern, response_text, re.IGNORECASE)
            if simple_match:
                category = simple_match.group(1).upper()
                logger.info(f"✓ Found simple classification for {filename}: {category}")
                return category
                
            # Method 4: Look for explicit conclusion in response
            if "kategoria końcowa" in response_text.lower():
                if re.search(r'kategoria końcowa[:\s]*people', response_text, re.IGNORECASE):
                    logger.info(f"✓ Found explicit PEOPLE conclusion for {filename}")
                    return "PEOPLE"
                elif re.search(r'kategoria końcowa[:\s]*hardware', response_text, re.IGNORECASE):
                    logger.info(f"✓ Found explicit HARDWARE conclusion for {filename}")
                    return "HARDWARE"
                    
            logger.warning(f"❌ No valid classification found for {filename}")
            logger.warning(f"Response was: {response_text}")
            return None
            
        except Exception as e:
            logger.error(f"Error parsing single classification response for {filename}: {e}")
            return None
            
    def classify_content(self) -> Dict[str, List[str]]:
        """Classify all content using LLM - file by file"""
        logger.info("Starting content classification (file by file)")
        
        # Load all JSON files
        all_content = []
        
        for ext, json_file in self.JSON_FILES.items():
            if os.path.exists(json_file):
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    all_content.extend(data)
                    logger.info(f"Loaded {len(data)} items from {json_file}")
                except Exception as e:
                    logger.error(f"Error loading {json_file}: {e}")
                    
        if not all_content:
            logger.warning("No content found for classification")
            return {"people": [], "hardware": []}
            
        logger.info(f"Classifying {len(all_content)} files individually")
        
        # Classify each file individually
        people = []
        hardware = []
        
        for item in all_content:
            filename = item['name']
            content = item['content']
            
            logger.info(f"Classifying file: {filename}")
            
            # Classify single file with retry
            category = None
            for attempt in range(self.MAX_RETRIES + 1):
                try:
                    category = self.classify_single_file(filename, content)
                    break
                except Exception as e:
                    logger.error(f"Classification attempt {attempt + 1} failed for {filename}: {e}")
                    if attempt == self.MAX_RETRIES:
                        logger.error(f"Failed to classify {filename} after {self.MAX_RETRIES + 1} attempts")
                        break
                    time.sleep(RETRY_DELAY)
            
            # Add to appropriate category
            if category == "PEOPLE":
                people.append(filename)
                logger.info(f"✓ {filename} -> PEOPLE")
            elif category == "HARDWARE":
                hardware.append(filename)
                logger.info(f"✓ {filename} -> HARDWARE")
            else:
                logger.info(f"✗ {filename} -> OTHER (skipped)")
                
        # Sort results alphabetically
        people = sorted(people)
        hardware = sorted(hardware)
                
        result = {"people": people, "hardware": hardware}
        logger.info(f"Classification complete: {result}")
        
        # Save final answer to file
        self.save_final_answer(result)
        
        return result
        
    def save_final_answer(self, classification_result: Dict) -> None:
        """Save final classification result to JSON file"""
        try:
            with open(self.FINAL_ANSWER_FILE, 'w', encoding='utf-8') as f:
                json.dump(classification_result, f, ensure_ascii=False, indent=2)
            logger.info(f"Final answer saved to {self.FINAL_ANSWER_FILE}")
        except Exception as e:
            logger.error(f"Error saving final answer: {e}")
            
    def load_final_answer(self) -> Optional[Dict]:
        """Load final classification result from JSON file"""
        try:
            if not os.path.exists(self.FINAL_ANSWER_FILE):
                logger.error(f"Final answer file not found: {self.FINAL_ANSWER_FILE}")
                return None
                
            with open(self.FINAL_ANSWER_FILE, 'r', encoding='utf-8') as f:
                result = json.load(f)
            logger.info(f"Final answer loaded from {self.FINAL_ANSWER_FILE}: {result}")
            return result
        except Exception as e:
            logger.error(f"Error loading final answer: {e}")
            return None
        
    def submit_to_centrala(self, classification_result: Dict) -> Optional[Dict]:
        """Submit results to centrala with enhanced debugging"""
        logger.info("=== SUBMITTING TO CENTRALA ===")
        
        payload = {
            "task": TASK_NAME,
            "apikey": API_KEY,
            "answer": classification_result
        }
        
        headers = {
            "Content-Type": "application/json; charset=utf-8"
        }
        
        # Print detailed debugging info
        print("\n📤 CENTRALA SUBMISSION")
        print(f"Endpoint: {REPORT_ENDPOINT}")
        print(f"Task: {TASK_NAME}")
        print(f"Payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        print(f"Headers: {headers}")
        
        logger.info(f"Sending payload: {json.dumps(payload, ensure_ascii=False)}")
        
        try:
            response = requests.post(
                REPORT_ENDPOINT,
                headers=headers,
                json=payload,
                timeout=REQUEST_TIMEOUT
            )
            
            # Enhanced response debugging
            print("\n📥 CENTRALA RESPONSE")
            print(f"Status Code: {response.status_code}")
            print(f"Response Headers: {dict(response.headers)}")
            print(f"Response Text: {response.text}")
            
            if response.status_code != 200:
                logger.error(f"HTTP Error {response.status_code}: {response.text}")
                return None
                
            result = response.json()
            logger.info(f"Centrala response: {result}")
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error submitting to centrala: {e}")
            print(f"\n❌ REQUEST ERROR: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error in centrala response: {e}")
            print(f"\n❌ JSON DECODE ERROR: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error submitting to centrala: {e}")
            print(f"\n❌ UNEXPECTED ERROR: {e}")
            return None
            
    def run_centrala_only(self):
        """Load final answer from file and submit to centrala only"""
        logger.info("=== CENTRALA SUBMISSION ONLY ===")
        
        try:
            # Load saved classification result
            classification_result = self.load_final_answer()
            if not classification_result:
                print("\n❌ No final answer file found. Run full workflow first.")
                return
            
            print("\n📋 LOADED CLASSIFICATION RESULT:")
            print(json.dumps(classification_result, ensure_ascii=False, indent=2))
            
            # Submit to centrala
            response = self.submit_to_centrala(classification_result)
            
            # Check for flag
            if response:
                flag = self.check_for_flag(response)
                if flag:
                    print(f"\n🎉 FLAG FOUND: {flag}")
                else:
                    print(f"\n📋 Response: {response}")
            else:
                print("\n❌ Failed to get response from centrala")
                
        except Exception as e:
            logger.error(f"Centrala submission failed: {e}")
            print(f"\n❌ Error: {e}")
            
    def check_for_flag(self, response) -> Optional[str]:
        """Check response for flag"""
        response_str = str(response)
        
        if not response_str:
            logger.info("Empty response")
            return None
            
        # Search for flag in {{FLG:XXXX}} format
        match_curly = re.search(FLAG_REGEX_CURLY, response_str)
        if match_curly:
            flag = match_curly.group()
            logger.info(f"Found flag (curly format): {flag}")
            return flag
            
        # Search for flag in FLG[a-zA-Z0-9_-]+ format
        match = re.search(FLAG_REGEX, response_str)
        if match:
            flag = match.group()
            logger.info(f"Found flag (simple format): {flag}")
            return flag
            
        # Check if text contains the word "flag" or "FLG"
        if "flag" in response_str.lower() or "flg" in response_str.lower():
            logger.info(f"Response contains flag-related text: {response_str}")
            
        logger.info("No flag found in response")
        return None
        
    def run_full_workflow(self):
        """Run complete workflow"""
        logger.info("=== Starting Factory Reports Classification SFA ===")
        
        try:
            # Discover files
            files_by_ext = self.discover_files()
            
            # Process each type
            self.process_txt_files(files_by_ext['.txt'])
            self.process_png_files(files_by_ext['.png'])
            self.process_mp3_files(files_by_ext['.mp3'])
            
            # Classify content
            classification_result = self.classify_content()
            
            # Submit to centrala
            response = self.submit_to_centrala(classification_result)
            
            # Check for flag
            if response:
                flag = self.check_for_flag(response)
                if flag:
                    print(f"\n🎉 FLAG FOUND: {flag}")
                else:
                    print(f"\n📋 Response: {response}")
            else:
                print("\n❌ Failed to get response from centrala")
                
        except Exception as e:
            logger.error(f"Workflow failed: {e}")
            print(f"\n❌ Error: {e}")
            
    def process_audio_only(self):
        """Process only MP3 files"""
        logger.info("=== Processing MP3 files only ===")
        files_by_ext = self.discover_files()
        self.process_mp3_files(files_by_ext['.mp3'])
        
    def process_images_only(self):
        """Process only PNG files"""
        logger.info("=== Processing PNG files only ===")
        files_by_ext = self.discover_files()
        self.process_png_files(files_by_ext['.png'])

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Factory Reports Classification SFA')
    parser.add_argument('--sound', action='store_true', help='Process only MP3 files')
    parser.add_argument('--image', action='store_true', help='Process only PNG files')
    parser.add_argument('--central', action='store_true', help='Submit only to centrala using saved final_answer.json')
    args = parser.parse_args()
    
    processor = FactoryReportsProcessor()
    
    if args.central:
        processor.run_centrala_only()
    elif args.sound:
        processor.process_audio_only()
    elif args.image:
        processor.process_images_only()
    else:
        processor.run_full_workflow()

if __name__ == "__main__":
    main()
