# S02E04 - Implementation Plan: Factory Reports Classification SFA

## Overview

Single File Agent (SFA) dla klasyfikacji raportów z fabryki z wykorzystaniem multimodalnego przetwarzania danych (TXT, PNG, MP3) i kategoryzacji treści związanej z ludźmi i hardware'em.

## Tech Stack & APIs

### Core Technologies

- **Python 3.12+** - główny język implementacji
- **OpenAI API Standard** - kompatybilność z różnymi providerami

### API Providers & Models

```python
# Image processing (PNG)
Provider: kluster.ai
Model: google/gemma-3-27b-it
Usage: OCR + text extraction from images

# Audio transcription (MP3)  
Provider: AssemblyAI
Model: speech_model=aai.SpeechModel.best
API Key: ASSEMBLY_API_KEY

# Final classification
Provider: kluster.ai  
Model: deepseek-ai/DeepSeek-V3-0324
Usage: Content categorization (people vs hardware)
```

### Dependencies

```python
# Core libraries
import os
import json
import argparse
import base64
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# HTTP & API clients
import requests
import assemblyai as aai

# File processing
from PIL import Image
import io
```

## Architecture Design

### Single File Structure

```python
# S02E04_sfa.py
class FactoryReportsProcessor:
    """
    Main SFA class implementing factory reports classification workflow
    """
    
    # Configuration constants (magic numbers)
    MAX_RETRIES = 1
    SUPPORTED_EXTENSIONS = {'.txt', '.png', '.mp3'}
    EXCLUDED_FILES = {'weapons_tests.zip', '2024-11-12_report-99'}
    EXCLUDED_DIRS = {'facts'}
    
    # API configurations
    KLUSTER_API_BASE = "https://api.kluster.ai/v1"
    ASSEMBLYAI_API_KEY = ASSEMBLY_API_KEY
    
    # Output files
    JSON_FILES = {
        '.txt': 'txt.json',
        '.png': 'png.json', 
        '.mp3': 'mp3.json'
    }
```

## Implementation Workflow

### Phase 1: File Discovery & Validation

```python
def discover_files(self, base_path: str) -> Dict[str, List[str]]:
    """
    Skanuje katalog i grupuje pliki według rozszerzeń
    - Filtruje pliki według SUPPORTED_EXTENSIONS
    - Pomija EXCLUDED_FILES i EXCLUDED_DIRS
    - Waliduje czy pliki nie są puste/uszkodzone
    - Zwraca dict {extension: [filenames]}
    """
    
def validate_file(self, filepath: Path) -> bool:
    """
    Sprawdza czy plik jest prawidłowy:
    - Nie jest pusty (size > 0)
    - Ma prawidłowe rozszerzenie
    - Jest czytelny
    """
```

### Phase 2: Content Extraction

#### 2.1 TXT Processing

```python
def process_txt_files(self, txt_files: List[str]) -> None:
    """
    Proces dla plików tekstowych:
    1. Sprawdź czy txt.json już istnieje i jest kompletny
    2. Dla każdego pliku TXT:
       - Odczytaj z encoding detection (utf-8, cp1252, etc.)
       - Zapisz do txt.json jako {"name": "file.txt", "content": "text"}
    3. Waliduj kompletność (count files == count JSON objects)
    """
```

#### 2.2 PNG Processing

```python
def process_png_files(self, png_files: List[str]) -> None:
    """
    Proces dla obrazów PNG:
    1. Sprawdź czy png.json już istnieje i jest kompletny
    2. Dla każdego obrazu PNG:
       - Załaduj i zoptymalizuj obraz (resize if > 2MB)
       - Encode do base64
       - Wyślij do kluster.ai z google/gemma-3-27b-it
       - Prompt: "Rozpoznaj i wypisz w swojej odpowiedzi kompletny tekst z obrazu. Nie dodawaj żadnej innej treści - tylko rozpoznany tekst"
       - Zapisz odpowiedź do png.json
       - Retry logic: MAX_RETRIES = 1
    3. Waliduj kompletność
    """

def call_kluster_vision_api(self, image_b64: str, prompt: str) -> str:
    """
    Kluster.ai API call dla modelu google/gemma-3-27b-it
    """
```

#### 2.3 MP3 Processing  

```python
def process_mp3_files(self, mp3_files: List[str]) -> None:
    """
    Proces dla plików audio MP3:
    1. Sprawdź czy mp3.json już istnieje i jest kompletny
    2. Dla każdego pliku MP3:
       - Configure AssemblyAI client
       - Upload file i wykonaj transkrypcję
       - Sprawdź status (error handling)
       - Zapisz transkrypt do mp3.json
       - Retry logic: MAX_RETRIES = 1
    3. Waliduj kompletność
    """

def transcribe_audio_assemblyai(self, audio_path: str) -> str:
    """
    AssemblyAI transcription using best speech model
    """
```

### Phase 3: Content Classification

```python
def classify_content(self) -> Dict[str, List[str]]:
    """
    Finalny etap klasyfikacji:
    1. Załaduj wszystkie JSON files (txt.json, png.json, mp3.json)
    2. Przygotuj unified content list
    3. Wyślij do kluster.ai z deepseek-ai/DeepSeek-V3-0324
    4. System prompt: klasyfikacja people vs hardware
    5. Parse response i przygotuj final answer
    6. Alfabetyczne sortowanie nazw plików
    """

def call_kluster_classification_api(self, content_data: List[Dict]) -> Dict:
    """
    Kluster.ai API call dla modelu deepseek-ai/DeepSeek-V3-0324
    """
```

### Phase 4: API Submission

```python
def submit_to_centrala(self, classification_result: Dict) -> None:
    """
    Wysyłka do centrali:
    1. Format final JSON according to specification
    2. POST to https://c3ntrala.ag3nts.org/report
    3. Handle response i search for flag
    4. Log results according to SFA BBS style
    """
```

## System Prompts

### Image OCR Prompt

```
Rozpoznaj i wypisz w swojej odpowiedzi kompletny tekst z obrazu. Nie dodawaj żadnej innej treści - tylko rozpoznany tekst.
```

### Classification System Prompt

```
Jesteś specjalistą od analizy raportów fabrycznych. Twoim zadaniem jest sklasyfikowanie treści do dwóch kategorii:

PEOPLE: Raporty zawierające informacje o schwytanych ludziach lub śladach ich obecności (np. monitorowanie, zatrzymania, obserwacje osób)

HARDWARE: Raporty o naprawionych usterkach sprzętowych (np. naprawa maszyn, wymiana części, serwis urządzeń)

WYKLUCZ:
- Raporty związane z oprogramowaniem (software, aplikacje, systemy IT)
- Inne treści niezwiązane z powyższymi kategoriami

Analizuj każdy plik i zwróć odpowiedź w formacie JSON:
{
  "people": ["filename1.ext", "filename2.ext"],
  "hardware": ["filename3.ext", "filename4.ext"]
}

Uwzględniaj tylko te pliki, które pasują do kategorii PEOPLE lub HARDWARE.
```

## Command Line Interface

### Main Execution

```bash
python S02E04_sfa.py
# Pełny workflow: discover -> extract -> classify -> submit
```

### Partial Execution Flags

```bash
python S02E04_sfa.py --sound
# Tylko transkrypcja MP3 files

python S02E04_sfa.py --image  
# Tylko OCR dla PNG files
```

### CLI Implementation

```python
def main():
    parser = argparse.ArgumentParser(description='Factory Reports Classification SFA')
    parser.add_argument('--sound', action='store_true', help='Process only MP3 files')
    parser.add_argument('--image', action='store_true', help='Process only PNG files')
    args = parser.parse_args()
    
    processor = FactoryReportsProcessor()
    
    if args.sound:
        processor.process_audio_only()
    elif args.image:
        processor.process_images_only()
    else:
        processor.run_full_workflow()
```

## Caching & Resume Strategy

### Low-Cost Caching

```python
def is_json_complete(self, json_file: str, expected_files: List[str]) -> bool:
    """
    Sprawdza czy JSON file zawiera wszystkie expected entries:
    - Liczba obiektów == liczba plików
    - Nazwy obiektów == nazwy plików
    - Każdy obiekt ma 'name' i 'content'
    """

def should_skip_processing(self, file_type: str, files: List[str]) -> bool:
    """
    Decyduje czy pominąć processing based na existing JSON completeness
    """
```

## Error Handling & Validation

### Retry Logic

```python
def retry_api_call(self, api_func, *args, **kwargs):
    """
    Generic retry wrapper:
    - MAX_RETRIES = 1
    - Exponential backoff: 2^attempt seconds
    - Log każdy attempt
    - Throw exception po przekroczeniu limits
    """
```

### Validation Layer

```python
def validate_final_output(self, result: Dict) -> bool:
    """
    Final validation przed submission:
    - Sprawdź czy wszystkie pliki zostały processed
    - Verify JSON structure
    - Check alphabetical sorting
    - Validate że tylko supported files w output
    """
```

### Error Reporting

```python
def log_error(self, level: str, message: str, file_context: str = None):
    """
    Consistent error logging:
    - Console output z clear formatting
    - File context dla debugging
    - Error categories: VALIDATION, API, FILE_IO
    """
```

## File Structure

```
S02E04/
├── S02E04_analiza.md          # Analysis document  
├── S02E04_IM_plan.md          # This implementation plan
├── S02E04_sfa.py              # Main SFA implementation
├── txt.json                   # Extracted text content
├── png.json                   # OCR results from images  
├── mp3.json                   # Audio transcriptions
└── classification_result.json # Final categorization
```

## Success Metrics & Validation

### Processing Validation

- [ ] All TXT files read and stored in txt.json
- [ ] All PNG files processed via OCR and stored in png.json  
- [ ] All MP3 files transcribed and stored in mp3.json
- [ ] JSON files contain exact count matching source files
- [ ] File names in JSONs match source file names exactly

### Classification Validation  

- [ ] All content from JSON files analyzed
- [ ] Only "people" and "hardware" categories in output
- [ ] File names alphabetically sorted in each category
- [ ] No file paths, only filenames in final output
- [ ] Valid JSON structure for centrala submission

### API Integration Validation

- [ ] Successful submission to <https://c3ntrala.ag3nts.org/report>
- [ ] Flag extraction from centrala response  
- [ ] Proper error handling and logging throughout
- [ ] Resume capability working correctly

## Risk Mitigation

### High Priority Risks

1. **API Rate Limits**: Sequential processing + retry logic
2. **File Corruption**: Validation before processing
3. **Classification Accuracy**: Clear system prompt + examples

### Medium Priority Risks  

1. **Memory Usage**: Process files individually, not in batch
2. **Network Issues**: Retry logic with exponential backoff
3. **Resume Failures**: JSON completeness validation

## Implementation Checklist

### Core Development

- [ ] Create base FactoryReportsProcessor class
- [ ] Implement file discovery and validation  
- [ ] Build TXT processing pipeline
- [ ] Build PNG OCR pipeline with kluster.ai integration
- [ ] Build MP3 transcription with AssemblyAI
- [ ] Implement content classification with DeepSeek
- [ ] Add centrala submission logic

### CLI & Features

- [ ] Add argparse for --sound and --image flags
- [ ] Implement partial execution workflows
- [ ] Add comprehensive error handling and retry logic
- [ ] Build validation layer for JSON completeness
- [ ] Add flag extraction and logging (SFA BBS style)

### Testing & Validation

- [ ] Test with actual files from pliki_z_fabryki/
- [ ] Verify API integrations work correctly  
- [ ] Test resume functionality with partial JSON files
- [ ] Validate final output format matches requirements
- [ ] End-to-end test with centrala submission

---

**Status**: Implementation plan complete ✅  
**Next**: S02E04_sfa.py development
