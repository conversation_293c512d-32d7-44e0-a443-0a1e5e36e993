# S02E03 - Plan Implementacji Single File Agent

## 🎯 Cel Zadania

Implementacja Single File Agent (SFA) dla zadania S02E03 - generowanie obrazu robota na podstawie opisu z API Centrali używając pollinations.ai z fallbackiem na DALL-E 3.

## 📋 Kluczowe Ustalenia

- **API generowania**: pollinations.ai (główne) + DALL-E 3 (fallback)
- **Format obrazu**: JPG (akceptowalny)
- **Hosting**: lokalny HTTP serwer
- **API Centrali**: zgodnie z dokumentacją
- **Flaga**: w odpowiedzi z `/report` w formacie `{{FLG:....}}`
- **Bez iteracji**: prosty linearny workflow

## 🏗️ Architektura Rozwiązania

### Workflow
```
1. Pobieranie opisu robota z API Centrali
    ↓
2. Przygotowanie promptu (system prompt + opis)
    ↓
3. Generowanie obrazu (pollinations.ai + fallback DALL-E 3)
    ↓
4. Hosting lokalny obrazu
    ↓
5. Wysłanie URL do API Centrali
    ↓
6. Poszukiwanie flagi w odpowiedzi
```

### Struktura Pliku `S02E03_sfa.py`

```python
"""
S02E03 Single File Agent - Robot Image Generation
Generuje obraz robota na podstawie opisu z API Centrali używając pollinations.ai
"""

# --- IMPORTS ---
# --- CONFIGURATION ---
# --- HELPER FUNCTIONS ---
# --- MAIN FUNCTION ---
# --- ENTRY POINT ---
```

## 📦 Imports i Biblioteki

```python
import os
import json
import logging
import requests
import threading
import time
from typing import Dict, Optional
from urllib.parse import quote_plus
from http.server import HTTPServer, SimpleHTTPRequestHandler
from dotenv import load_dotenv
from openai import OpenAI  # fallback tylko
```

## ⚙️ Konfiguracja

### Stałe Aplikacyjne
```python
TASK_NAME = "robotid"
API_BASE_URL = "https://c3ntrala.ag3nts.org"
DATA_ENDPOINT = f"{API_BASE_URL}/data"
REPORT_ENDPOINT = f"{API_BASE_URL}/report"
API_KEY = os.getenv("AIDEVS_API_KEY")

# Pollinations.ai settings
POLLINATIONS_BASE_URL = "https://pollinations.ai/p"
LOCAL_IMAGE_FILENAME = "robot_image.jpg"
LOCAL_SERVER_PORT = 8000

# OpenAI settings (fallback)
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
DALLE_MODEL = "dall-e-3"
DALLE_SIZE = "1024x1024"
DALLE_QUALITY = "standard"

# Request settings
REQUEST_TIMEOUT = 30
RETRY_ATTEMPTS = 2
RETRY_DELAY = 2

# System prompt path
SYSTEM_PROMPT_PATH = os.path.join(
    os.path.dirname(os.path.abspath(__file__)), 
    "S02E03_sys_prompt.txt"
)
```

### Zmienne Środowiskowe
```bash
# Wymagane
AIDEVS_API_KEY=your_centrala_api_key_here

# Opcjonalne (dla fallback)
OPENAI_API_KEY=your_openai_api_key_here
```

## 🔧 Funkcje Pomocnicze

### 1. Walidacja Konfiguracji
```python
def validate_configuration() -> bool:
    """
    Waliduje zmienne środowiskowe
    Returns: True jeśli konfiguracja jest poprawna
    """
    # Sprawdzenie AIDEVS_API_KEY (wymagany)
    # Ostrzeżenie o braku OPENAI_API_KEY (opcjonalny)
    # Logowanie stanu konfiguracji
```

### 2. Wczytywanie System Prompt
```python
def load_system_prompt() -> str:
    """
    Wczytuje skrócony system prompt z pliku S02E03_sys_prompt.txt
    Returns: zawartość pliku lub domyślny prompt
    """
    # Wczytanie z SYSTEM_PROMPT_PATH
    # Fallback na domyślny prompt w przypadku błędu
```

### 3. Pobieranie Opisu Robota
```python
def fetch_robot_description(api_key: str, attempt: int = 1) -> Optional[Dict]:
    """
    GET /data/{apikey}/robotid.json
    Args:
        api_key: klucz API Centrali
        attempt: numer próby (dla retry logic)
    Returns: {"description": "robot description"} lub None
    """
    # URL: https://c3ntrala.ag3nts.org/data/{api_key}/robotid.json
    # Retry logic z RETRY_ATTEMPTS
    # Obsługa błędów HTTP i JSON
```

### 4. Przygotowanie Promptu
```python
def prepare_prompt(system_prompt: str, robot_description: str) -> str:
    """
    Łączy system prompt z opisem robota
    Args:
        system_prompt: szablon promptu z {ROBOT_DESCRIPTION_PLACEHOLDER}
        robot_description: opis robota z API
    Returns: kompletny prompt gotowy do użycia
    """
    # Zastąpienie {ROBOT_DESCRIPTION_PLACEHOLDER}
    # URL encoding dla pollinations.ai (quote_plus)
```

### 5. Generowanie Obrazu - Pollinations.ai
```python
def generate_image_pollinations(prompt: str, attempt: int = 1) -> Optional[str]:
    """
    Generuje obraz używając pollinations.ai
    Args:
        prompt: przygotowany prompt
        attempt: numer próby
    Returns: ścieżka do pobranego pliku JPG lub None
    """
    # URL: https://pollinations.ai/p/{encoded_prompt}
    # Pobranie obrazu z response.content
    # Zapisanie jako LOCAL_IMAGE_FILENAME
    # Retry logic
```

### 6. Generowanie Obrazu - DALL-E 3 (Fallback)
```python
def generate_image_dalle3(prompt: str, attempt: int = 1) -> Optional[str]:
    """
    Fallback na DALL-E 3 w przypadku problemów z pollinations
    Args:
        prompt: przygotowany prompt
        attempt: numer próby
    Returns: ścieżka do pobranego obrazu lub None
    """
    # OpenAI API client
    # DALL-E 3 parameters: model, size, quality
    # Pobranie obrazu z URL zwróconego przez DALL-E
    # Retry logic
```

### 7. Hosting Lokalny
```python
def start_local_server(port: int = LOCAL_SERVER_PORT) -> str:
    """
    Uruchamia lokalny HTTP serwer dla hostingu obrazu
    Args:
        port: port serwera (domyślnie 8000)
    Returns: URL do obrazu (http://localhost:8000/robot_image.jpg)
    """
    # HTTPServer w osobnym wątku (threading)
    # SimpleHTTPRequestHandler
    # Sprawdzenie dostępności portu
    # Zwrócenie URL do obrazu
```

### 8. Wysyłanie Raportu
```python
def submit_report(image_url: str, api_key: str, attempt: int = 1) -> Optional[Dict]:
    """
    POST /report
    Args:
        image_url: URL do wygenerowanego obrazu
        api_key: klucz API Centrali
        attempt: numer próby
    Returns: {"code": 0, "message": "{{FLG:....}}"} lub None
    """
    # Payload: {"task": "robotid", "apikey": api_key, "answer": image_url}
    # POST do https://c3ntrala.ag3nts.org/report
    # Retry logic
    # Obsługa błędów HTTP i JSON
```

### 9. Wyciąganie Flagi
```python
def extract_flag(response: Dict) -> Optional[str]:
    """
    Szuka flagi w formacie {{FLG:....}} w response['message']
    Args:
        response: odpowiedź z API /report
    Returns: znaleziona flaga lub None
    """
    # Regex pattern: r"\{\{FLG:[^}]+\}\}"
    # Wyszukiwanie w response.get('message', '')
    # Logowanie znalezionej flagi
```

## 🎯 Główna Funkcja

```python
def main():
    """Główna funkcja orkiestrująca proces"""
    
    logger.info("=== S02E03 Robot Image Generation Started ===")
    
    # 1. Walidacja konfiguracji
    if not validate_configuration():
        logger.error("Configuration validation failed")
        return
    
    # 2. Pobranie opisu robota
    logger.info("Fetching robot description...")
    robot_data = fetch_robot_description(API_KEY)
    if not robot_data:
        logger.error("Failed to fetch robot description")
        return
    
    robot_description = robot_data.get('description', '')
    logger.info(f"Robot description: {robot_description}")
    
    # 3. Przygotowanie promptu
    logger.info("Preparing prompt...")
    system_prompt = load_system_prompt()
    full_prompt = prepare_prompt(system_prompt, robot_description)
    logger.info(f"Full prompt prepared (length: {len(full_prompt)})")
    
    # 4. Generowanie obrazu (pollinations + fallback)
    logger.info("Generating image with pollinations.ai...")
    image_path = generate_image_pollinations(full_prompt)
    
    if not image_path:
        logger.warning("Pollinations failed, trying DALL-E 3 fallback...")
        image_path = generate_image_dalle3(full_prompt)
        if not image_path:
            logger.error("Both image generation methods failed")
            return
    
    logger.info(f"Image generated successfully: {image_path}")
    
    # 5. Hosting lokalny
    logger.info("Starting local server for image hosting...")
    image_url = start_local_server()
    logger.info(f"Image hosted at: {image_url}")
    
    # Krótkie opóźnienie dla stabilizacji serwera
    time.sleep(2)
    
    # 6. Wysyłanie raportu
    logger.info("Submitting report to Centrala...")
    response = submit_report(image_url, API_KEY)
    if not response:
        logger.error("Failed to submit report")
        return
    
    logger.info(f"Report submitted successfully: {response}")
    
    # 7. Poszukiwanie flagi
    flag = extract_flag(response)
    if flag:
        print(f"\n🎉 FLAGA ZNALEZIONA: {flag}")
        logger.info(f"Flag found: {flag}")
    else:
        logger.info("No flag found in response")
        print(f"Response: {response}")
    
    logger.info("=== S02E03 Process Completed ===")
```

## 🛡️ Obsługa Błędów

### Logowanie
```python
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)
```

### Retry Logic
- **Funkcje API**: 2 próby z opóźnieniem 2 sekund
- **Exponential backoff** dla problemów z siecią
- **Graceful degradation**: pollinations.ai → DALL-E 3

### Exception Handling
- **HTTP Errors**: requests.exceptions.RequestException
- **JSON Errors**: json.JSONDecodeError
- **OpenAI Errors**: openai.APIError, RateLimitError
- **File I/O Errors**: FileNotFoundError, PermissionError

## 📏 Compliance z Najlepszymi Praktykami

### SOLID Principles
- **S (Single Responsibility)**: Każda funkcja ma jedną odpowiedzialność
- **O (Open/Closed)**: Łatwo dodać nowe API generowania obrazów
- **L (Liskov Substitution)**: Fallback DALL-E 3 nie łamie funkcjonalności
- **I (Interface Segregation)**: Minimalne interfejsy funkcji
- **D (Dependency Inversion)**: Konfiguracja przez zmienne środowiskowe

### DRY, KISS, YAGNI
- **DRY**: Wspólne funkcje retry logic, brak powtórzeń
- **KISS**: Prosty linearny workflow, czytelny kod
- **YAGNI**: Tylko niezbędne funkcje, bez nadmiarowych feature'ów

## 📁 Struktura Folderów

```
S02E03/
├── S02E03_sfa.py           # Single File Agent (główny plik)
├── S02E03_sys_prompt.txt   # Skrócony system prompt
├── S02E03_analiza.md       # Analiza zadania
├── S02E03_IM_plan.md       # Niniejszy plan implementacji
├── robot_image.jpg         # Wygenerowany obraz (tymczasowy)
└── .env                    # Zmienne środowiskowe (opcjonalnie)
```

## ✅ Checklisty Walidacji

### Funkcjonalność
- [ ] Pobieranie opisu robota z `/data/{apikey}/robotid.json`
- [ ] Wczytywanie system prompt z pliku
- [ ] Przygotowanie kompletnego promptu
- [ ] Generowanie obrazu przez pollinations.ai
- [ ] Fallback na DALL-E 3 w przypadku problemów
- [ ] Hosting lokalny obrazu przez HTTP server
- [ ] Wysyłanie raportu do `/report`
- [ ] Poszukiwanie flagi w odpowiedzi
- [ ] Logowanie całego procesu

### Jakość Kodu
- [ ] Zgodność z SOLID principles
- [ ] Type hints dla wszystkich funkcji
- [ ] Comprehensive error handling
- [ ] Retry logic dla API calls
- [ ] Proper logging configuration
- [ ] Environment variables validation
- [ ] Clean code structure (imports → config → functions → main)

### Testowanie
- [ ] **Happy Path**: pomyślne wykonanie całego procesu
- [ ] **Pollinations Failure**: test fallback na DALL-E 3
- [ ] **Network Issues**: retry logic
- [ ] **Invalid API Key**: obsługa błędów autoryzacji
- [ ] **Empty Description**: obsługa nieprawidłowych danych
- [ ] **Server Port Conflict**: obsługa zajętego portu

## 🚀 Entry Point

```python
if __name__ == "__main__":
    main()
```

## 📝 Uwagi Implementacyjne

1. **URL Encoding**: Używanie `urllib.parse.quote_plus` dla promptu w pollinations.ai
2. **Threading**: HTTP server w osobnym wątku, aby nie blokować głównego procesu
3. **Graceful Shutdown**: Możliwość zatrzymania serwera po zakończeniu zadania
4. **Cross-Platform**: Kod działający na Windows, macOS, Linux
5. **Resource Management**: Proper cleanup plików tymczasowych

## 🎯 Oczekiwany Rezultat

Po uruchomieniu `python S02E03_sfa.py`:

1. Agent pobierze aktualny opis robota z API Centrali
2. Wygeneruje obraz używając pollinations.ai (lub DALL-E 3 jako fallback)
3. Uruchomi lokalny serwer HTTP dla hostingu obrazu
4. Wyśle URL obrazu do API Centrali
5. Znajdzie i wyświetli flagę w formacie: `🎉 FLAGA ZNALEZIONA: {{FLG:...}}`

---

**Autor**: AI Agent  
**Data**: 2025-05-23  
**Wersja**: 1.0.0
