# S02E03 - Robot Image Generation Agent

## 🤖 Description

This project implements a **Single File Agent (SFA)** designed to interact with the Centrala API for the `robotid` task. The agent's primary function is to:

1. Fetch a description of a robot from the Centrala API.
2. Prepare a detailed prompt by combining a predefined system prompt with the fetched robot description.
3. Utilize OpenAI's DALL-E 3 model to generate an image based on this prompt.
4. Optionally, download and save the generated image locally.
5. Submit the URL of the DALL-E 3 generated image back to the Centrala API.
6. Extract a "flag" from the Centrala API's response upon successful submission.

The agent is robust, with logging, configuration validation, and error handling.

---

## ✨ Features

- **Dynamic Image Generation:** Generates robot images based on descriptions provided by an external API.
- **DALL-E 3 Integration:** Leverages OpenAI's DALL-E 3 for high-quality image creation.
- **API Interaction:** Communicates with the Centrala API to fetch tasks and submit results.
- **Customizable Prompts:** Uses a base system prompt (from `S02E03_sys_prompt.txt`) dynamically updated with robot details.
- **Configuration Management:** Loads API keys and settings from environment variables (`.env` file).
- **Local Image Caching:** Downloads and saves the generated image to a local file.
- **Logging:** Provides detailed logging for monitoring and debugging.
- **Error Handling:** Handles request failures and other issues.
- **Flag Extraction:** Parses API responses to retrieve a specific flag.

---

## ⚙️ Workflow

The agent follows these steps:

1. **Initialization & Cleanup:**
    - Loads environment variables.
    - Configures logging.
    - Cleans up any previously generated image files (e.g., `robot_image.jpg`).
2. **Configuration Validation:**
    - Checks if essential API keys (`AIDEVS_API_KEY`, `OPENAI_API_KEY`) are set.
    - Verifies the existence of the system prompt file (`S02E03_sys_prompt.txt`).
3. **Fetch Robot Description:**
    - Makes a GET request to the Centrala API (`https://c3ntrala.ag3nts.org/data/{API_KEY}/robotid.json`) to retrieve the robot's description.
4. **Prepare Prompt:**
    - Reads the system prompt from `S02E03_sys_prompt.txt`.
    - Replaces the `{ROBOT_DESCRIPTION_PLACEHOLDER}` in the system prompt with the description fetched from the API.
5. **Generate Image with DALL-E 3:**
    - Sends a request to the OpenAI API with the prepared prompt, specifying `dall-e-3` model, `1024x1024` size, and `standard` quality.
    - Receives the URL of the generated image.
6. **Download and Save Image (Optional but Implemented):**
    - Downloads the image from the DALL-E 3 URL.
    - Saves it locally as `robot_image.jpg` (or the configured `LOCAL_IMAGE_FILENAME`).
    - If this step fails, the agent logs a warning but continues with the URL submission.
7. **Submit Report:**
    - Makes a POST request to the Centrala API's report endpoint (`https://c3ntrala.ag3nts.org/report`).
    - The payload includes the task name (`robotid`), API key, and the DALL-E 3 image URL as the answer.
8. **Extract Flag:**
    - Parses the response from the Centrala API.
    - Uses a regular expression (`r"\{\{FLG:[^}]+\}\}"`) to find and extract the flag.
9. **Completion:**
    - Logs the success or failure of the task, including the obtained flag if successful.
    - Exits with an appropriate status code.

---

## 📋 Prerequisites

- Python 3.x
- Access to the Centrala API and a valid `AIDEVS_API_KEY`.
- Access to the OpenAI API and a valid `OPENAI_API_KEY`.

---

## 🛠️ Configuration

1. **Environment Variables:**
    Create a `.env` file in the same directory as `S02E03_sfa.py` with the following content:

    ```env
    AIDEVS_API_KEY="your_aidevs_api_key"
    OPENAI_API_KEY="your_openai_api_key"
    ```

    Replace `your_aidevs_api_key` and `your_openai_api_key` with your actual API keys.

2. **System Prompt:**
    The file `S02E03_sys_prompt.txt` contains the base prompt for DALL-E 3. It should be in the same directory as the script. Example content:

    ```text
    Create a photorealistic image of an industrial robot based on this description. Show the robot in a factory setting with clear details of all mentioned characteristics.

    REQUIREMENTS: High quality, industrial aesthetic, metallic materials, factory lighting, neutral pose.

    ROBOT DESCRIPTION: {ROBOT_DESCRIPTION_PLACEHOLDER}

    Generate exactly as described with all specified features clearly visible.
    ```

3. **Script Constants (Optional Adjustments):**
    The script `S02E03_sfa.py` contains several constants at the beginning that can be adjusted if needed:
    - `TASK_NAME`: Default is `robotid`.
    - `API_BASE_URL`: Default is `https://c3ntrala.ag3nts.org`.
    - `LOCAL_IMAGE_FILENAME`: Default is `robot_image.jpg`.
    - `DALLE_MODEL`: Default is `dall-e-3`.
    - `DALLE_SIZE`: Default is `1024x1024`.
    - `DALLE_QUALITY`: Default is `standard`.
    - `REQUEST_TIMEOUT`: Default is 30 seconds.

---

## 🚀 Usage

1. Ensure all prerequisites are met and configuration is done.
2. Install the required Python packages:

    ```bash
    pip install requests python-dotenv openai
    ```

3. Run the script from your terminal:

    ```bash
    python S02E03_sfa.py
    ```

    The script will log its progress to the console. If successful, it will print the extracted flag.

---

## 📁 File Structure

```text
.
├── S02E03_sfa.py         # Main Python script for the agent
├── S02E03_sys_prompt.txt # System prompt template for DALL-E 3
├── .env                  # Environment variables (API keys - create this file)
└── README.md             # This file
```

---

## 📦 Dependencies

The script relies on the following Python libraries:

- `os`: For interacting with the operating system (environment variables, file paths).
- `json`: For parsing JSON data from API responses.
- `logging`: For application-level logging.
- `requests`: For making HTTP requests to external APIs.
- `re`: For regular expression operations (flag extraction).
- `sys`: For system-specific parameters and functions (exit codes).
- `python-dotenv`: For loading environment variables from a `.env` file.
- `openai`: The official OpenAI Python library for interacting with DALL-E 3.
