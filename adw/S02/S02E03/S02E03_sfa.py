"""
S02E03 Single File Agent - Robot Image Generation

This agent generates robot images based on descriptions from Centrala API using DALL-E 3,
and submits the DALL-E 3 image URL directly to Centrala to get a flag from the API response.

Author: Augment Agent
Date: 2025-01-20
Version: 1.2.0
"""

# --- IMPORTS ---
import os
import json
import logging
import requests
import re
import sys
from typing import Dict, Optional
from dotenv import load_dotenv
from openai import OpenAI

# --- CONFIGURATION ---
# Load environment variables
load_dotenv()

# --- APPLICATION SETTINGS ---
TASK_NAME = "robotid"
API_BASE_URL = "https://c3ntrala.ag3nts.org"
DATA_ENDPOINT = f"{API_BASE_URL}/data"
REPORT_ENDPOINT = f"{API_BASE_URL}/report"
API_KEY = os.getenv("AIDEVS_API_KEY")

# Image generation settings
LOCAL_IMAGE_FILENAME = "robot_image.jpg"

# OpenAI DALL-E 3 settings
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
DALLE_MODEL = "dall-e-3"
DALLE_SIZE = "1024x1024"
DALLE_QUALITY = "standard"

# Request settings
REQUEST_TIMEOUT = 30
RETRY_ATTEMPTS = 0
RETRY_DELAY = 2

# System prompt path
SYSTEM_PROMPT_PATH = os.path.join(
    os.path.dirname(os.path.abspath(__file__)), 
    "S02E03_sys_prompt.txt"
)

# --- LOGGING CONFIGURATION ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# --- HELPER FUNCTIONS ---

def cleanup_old_images() -> None:
    """
    Removes any existing robot image files to ensure fresh generation.
    """
    logger.info("Cleaning up old image files...")
    
    # List of possible image file names to clean up (including configured filename)
    image_files = [
        LOCAL_IMAGE_FILENAME,
        "robot_image.jpg",
        "robot_image.jpeg", 
        "robot_image.png",
        "robot_image.webp"
    ]
    
    # Remove duplicates
    image_files = list(set(image_files))
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    for filename in image_files:
        file_path = os.path.join(current_dir, filename)
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                logger.info(f"Removed old image file: {filename}")
            except Exception as e:
                logger.warning(f"Could not remove {filename}: {e}")
    
    logger.info("Old image cleanup completed")

def validate_configuration() -> bool:
    """
    Validates that all required configuration variables are set.

    Returns:
        bool: True if configuration is valid, False otherwise
    """
    logger.info("Validating configuration...")
    
    if not API_KEY:
        logger.critical("AIDEVS_API_KEY is not set")
        return False
    
    if not os.path.exists(SYSTEM_PROMPT_PATH):
        logger.critical(f"System prompt file not found: {SYSTEM_PROMPT_PATH}")
        return False
    
    logger.info("Configuration validated successfully")
    return True

def fetch_robot_description() -> Optional[str]:
    """
    Fetches robot description from Centrala API.

    Returns:
        Optional[str]: Robot description or None if failed
    """
    url = f"{DATA_ENDPOINT}/{API_KEY}/{TASK_NAME}.json"
    logger.info(f"Fetching robot description from: {url}")
    
    try:
        response = requests.get(url, timeout=REQUEST_TIMEOUT)
        response.raise_for_status()
        
        data = response.json()
        description = data.get("description")
        
        if not description:
            logger.error("No description found in API response")
            return None
            
        logger.info(f"Robot description received: {description[:100]}...")
        return description
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching robot description: {e}")
        return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response: {e}")
        return None

def prepare_prompt(robot_description: str) -> str:
    """
    Prepares the complete prompt by combining system prompt with robot description.

    Args:
        robot_description: The robot description from API

    Returns:
        str: Complete prompt for image generation
    """
    logger.info("Preparing prompt by combining system prompt with robot description...")
    
    try:
        with open(SYSTEM_PROMPT_PATH, 'r', encoding='utf-8') as file:
            system_prompt = file.read()
        
        logger.info(f"System prompt loaded, length: {len(system_prompt)} characters")
        
        # Replace placeholder with actual description
        complete_prompt = system_prompt.replace(
            "{ROBOT_DESCRIPTION_PLACEHOLDER}", 
            robot_description
        )
        
        logger.info(f"Complete prompt prepared, total length: {len(complete_prompt)} characters")
        logger.info(f"Complete prompt preview: {complete_prompt[:150]}{'...' if len(complete_prompt) > 150 else ''}")
        
        return complete_prompt
        
    except Exception as e:
        logger.error(f"Error preparing prompt: {e}")
        fallback_prompt = f"Create a photorealistic image of an industrial robot: {robot_description}"
        logger.warning(f"Using fallback prompt: {fallback_prompt}")
        return fallback_prompt

def generate_image_dalle3(prompt: str) -> Optional[str]:
    """
    Generates image using DALL-E 3.

    Args:
        prompt: Image generation prompt

    Returns:
        Optional[str]: Image URL from DALL-E 3 or None if failed
    """
    if not OPENAI_API_KEY:
        logger.error("OpenAI API key not available")
        return None
    
    logger.info("Generating image via DALL-E 3...")
    logger.info(f"DALL-E 3 prompt: {prompt[:200]}{'...' if len(prompt) > 200 else ''}")
    
    try:
        client = OpenAI(api_key=OPENAI_API_KEY)
        
        logger.info("Sending request to DALL-E 3...")
        response = client.images.generate(
            model=DALLE_MODEL,
            prompt=prompt,
            size=DALLE_SIZE,
            quality=DALLE_QUALITY,
            n=1
        )
        
        image_url = response.data[0].url
        logger.info(f"DALL-E 3 generated image URL: {image_url}")
        
        return image_url
        
    except Exception as e:
        logger.error(f"Error generating image via DALL-E 3: {e}")
        return None

def download_and_save_image(image_url: str, filename: str = "robot_image.jpg") -> Optional[str]:
    """
    Downloads image from DALL-E 3 URL and saves it to disk.

    Args:
        image_url: DALL-E 3 image URL
        filename: Local filename to save the image

    Returns:
        Optional[str]: Local file path if successful, None if failed
    """
    logger.info(f"Downloading and saving image from: {image_url}")
    
    try:
        # Download image from URL
        response = requests.get(image_url, timeout=REQUEST_TIMEOUT)
        response.raise_for_status()
        
        # Determine file path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        file_path = os.path.join(current_dir, filename)
        
        # Save image to disk
        with open(file_path, 'wb') as file:
            file.write(response.content)
        
        logger.info(f"Image saved successfully to: {file_path}")
        logger.info(f"Image size: {len(response.content)} bytes")
        
        return file_path
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Error downloading image: {e}")
        return None
    except Exception as e:
        logger.error(f"Error saving image to disk: {e}")
        return None

def submit_report(image_url: str) -> Optional[str]:
    """
    Submits DALL-E 3 image URL to Centrala API and returns the response.

    Args:
        image_url: DALL-E 3 image URL

    Returns:
        Optional[str]: API response text or None if failed
    """
    logger.info(f"Submitting report with image URL: {image_url}")
    
    payload = {
        "task": TASK_NAME,
        "apikey": API_KEY,
        "answer": image_url
    }
    
    try:
        response = requests.post(
            REPORT_ENDPOINT,
            json=payload,
            timeout=REQUEST_TIMEOUT
        )
        response.raise_for_status()
        
        response_text = response.text
        logger.info(f"Report submitted successfully. Response: {response_text[:200]}...")
        
        return response_text
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Error submitting report: {e}")
        return None

def extract_flag(response_text: str) -> Optional[str]:
    """
    Extracts flag from API response.

    Args:
        response_text: API response text

    Returns:
        Optional[str]: Extracted flag or None if not found
    """
    logger.info("Searching for flag in response...")
    
    # Try to find flag in format {{FLG:...}}
    flag_pattern = r"\{\{FLG:[^}]+\}\}"
    match = re.search(flag_pattern, response_text)
    
    if match:
        flag = match.group(0)
        logger.info(f"Flag found: {flag}")
        return flag
    else:
        logger.warning("Flag not found in response")
        logger.info(f"Full response: {response_text}")
        return None

# --- MAIN FUNCTION ---

def main():
    """
    Main function that orchestrates the entire robot image generation workflow.
    """
    logger.info("=== S02E03 Robot Image Generation Agent Started ===")
    
    try:
        # Step 1: Clean up old images
        logger.info("🧹 Step 1: Cleaning up old image files...")
        cleanup_old_images()
        
        # Step 2: Validate configuration
        logger.info("⚙️  Step 2: Validating configuration...")
        if not validate_configuration():
            logger.critical("Configuration validation failed")
            return False
        
        # Step 3: Fetch robot description
        logger.info("📡 Step 3: Fetching robot description from Centrala API...")
        robot_description = fetch_robot_description()
        if not robot_description:
            logger.critical("Failed to fetch robot description")
            return False
        
        # Step 4: Prepare prompt
        logger.info("📝 Step 4: Preparing complete prompt...")
        prompt = prepare_prompt(robot_description)
        
        # Step 5: Generate image using DALL-E 3
        logger.info("🎨 Step 5: Generating robot image using DALL-E 3...")
        image_url = generate_image_dalle3(prompt)
        
        if not image_url:
            logger.critical("DALL-E 3 image generation failed")
            return False
        
        # Step 6: Download and save image from DALL-E 3 URL
        logger.info("💾 Step 6: Downloading and saving image from DALL-E 3 URL...")
        image_path = download_and_save_image(image_url, LOCAL_IMAGE_FILENAME)
        if image_path:
            logger.info(f"✅ Image saved locally at: {image_path}")
        else:
            logger.warning("⚠️ Failed to save image locally (continuing with URL submission)")
        
        # Step 7: Submit report with DALL-E 3 URL
        logger.info("📤 Step 7: Submitting report to Centrala API with DALL-E 3 URL...")
        response = submit_report(image_url)
        if not response:
            logger.critical("Failed to submit report")
            return False
        
        # Step 8: Extract flag
        logger.info("🚩 Step 8: Extracting flag from response...")
        flag = extract_flag(response)
        if flag:
            logger.info(f"🎉 SUCCESS! Flag obtained: {flag}")
        else:
            logger.error("Failed to extract flag from response")
            return False
        
        logger.info("=== Task completed successfully ===")
        return True
        
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return False

# --- ENTRY POINT ---

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
