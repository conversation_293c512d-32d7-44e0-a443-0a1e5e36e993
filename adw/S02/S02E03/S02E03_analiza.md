# S02E03 - <PERSON><PERSON><PERSON>: Generowanie Grafiki Robota

## Cel Zadania

Zadanie polega na stworzeniu systemu, który automatycznie:

1. **Pobiera aktualny opis robota** z dynamicznego endpointa (zmienia się co 10 minut)
2. **Generuje obraz robota** na podstawie tego opisu przy użyciu DALL-E 3
3. **Udostępnia wygenerowany obraz** publicznie pod URL-em
4. **Wysyła link do obrazu** do API Centrali w ramach zadania "robotid"

## Specyfikacja Techniczna

### Wymagania Funkcjonalne

- **Źródło danych**: `https://c3ntrala.ag3nts.org/data/{API_KEY}/robotid.json`
- **Model generowania**: DALL-E 3 (OpenAI API)
- **Format obrazu**: PNG, 1024x1024px
- **Dost<PERSON>p<PERSON>ść**: Obraz musi być publicznie dostępny pod URL
- **Endpoint docelowy**: `/report` API Centrali
- **Nazwa zadania**: `robotid`

### Wymagania Niefunkcjonalne

- **Timing**: Opis robota zmienia się co 10 minut
- **Jakość**: Musi używać DALL-E 3 (wersja 2 niewystarczająca)
- **Zgodność**: Wygenerowany obraz musi dokładnie odpowiadać opisowi
- **Dostępność URL**: Link musi prowadzić bezpośrednio do pliku obrazu

## Architektura Rozwiązania

### Single File Agent (SFA)

Zgodnie z wzorcem używanym w workspace, implementacja w formie jednego pliku Python:

```
S02E03/
├── S02E03_sfa.py           # Single File Agent
├── S02E03_analiza.md       # Niniejsza analiza
├── .env                    # zmienne środowiskowe (opcjonalnie)
└── logs/                   # pliki logów (opcjonalnie)
```

### Przepływ Danych

```
1. Pobieranie opisu robota z API Centrali
    ↓
2. Przetwarzanie opisu na prompt dla DALL-E 3
    ↓
3. Generowanie obrazu przez DALL-E 3
    ↓
4. Otrzymanie URL do wygenerowanego obrazu
    ↓
5. Wysłanie URL do API Centrali
```

## Plan Implementacji

### Etap 1: Przygotowanie środowiska

- Konfiguracja zmiennych środowiskowych:
  - `OPENAI_API_KEY` - klucz do OpenAI API
  - `CENTRALA_API_KEY` - klucz do API Centrali
- Walidacja dostępu do wymaganych API

### Etap 2: Pobieranie danych

```python
def fetch_robot_description(api_key: str) -> dict:
    """Pobiera aktualny opis robota z API Centrali"""
    url = f"https://c3ntrala.ag3nts.org/data/{api_key}/robotid.json"
    # Implementacja z obsługą błędów HTTP
```

### Etap 3: Przetwarzanie opisu

```python
def prepare_dalle_prompt(description: str) -> str:
    """Przekształca opis robota w precyzyjny prompt dla DALL-E 3"""
    # Analiza opisu i wzbogacenie o instrukcje wizualne
    # Zapewnienie zgodności z wymaganiami (PNG, 1024x1024)
```

### Etap 4: Generowanie obrazu

```python
def generate_robot_image(prompt: str) -> str:
    """Generuje obraz robota używając DALL-E 3"""
    # Wywołanie OpenAI API z parametrami:
    # - model: "dall-e-3"
    # - size: "1024x1024"
    # - quality: "standard" lub "hd"
    # - response_format: "url"
```

### Etap 5: Wysyłanie odpowiedzi

```python
def submit_to_centrala(image_url: str, api_key: str) -> dict:
    """Wysyła URL obrazu do API Centrali"""
    payload = {
        "task": "robotid",
        "apikey": api_key,
        "answer": image_url
    }
    # POST do https://c3ntrala.ag3nts.org/report
```

### Etap 6: Orkiestracja

```python
def main():
    """Główna funkcja orkiestrująca cały proces"""
    # 1. Pobieranie opisu
    # 2. Generowanie obrazu
    # 3. Wysyłanie odpowiedzi
    # 4. Obsługa błędów i logowanie
```

## Stack Technologiczny

### Język Programowania

- **Python 3.x** - doskonała integracja z OpenAI API

### Biblioteki

```python
# API i HTTP
import requests          # komunikacja z API
from openai import OpenAI # OpenAI API (DALL-E 3)

# Zarządzanie danymi
import json             # obsługa JSON
import os               # zmienne środowiskowe
from dotenv import load_dotenv  # .env files (opcjonalnie)

# Utilities
import logging          # logowanie
import time            # retry delays
from typing import Dict, Optional  # type hints
```

### Modele AI

- **DALL-E 3** - najnowsza wersja zapewniająca najlepszą jakość obrazów

## Kluczowe Aspekty Techniczne

### 1. Prompt Engineering dla DALL-E 3

- DALL-E 3 wymaga precyzyjnych, opisowych promptów
- Należy przekształcić opis robota w szczegółowy prompt wizualny
- Uwzględnić kontekst fabryczny i techniczny
- Zapewnić zgodność z wymaganiami formatu

### 2. Timing i Cache

- Opis robota zmienia się co 10 minut
- Implementacja mechanizmu sprawdzania zmian
- Opcjonalny cache ostatniego opisu

### 3. Obsługa Błędów

```python
# Rate limiting OpenAI API
# Błędy generowania obrazów
# Problemy z dostępnością endpointów
# Retry logic z exponential backoff
```

### 4. URL i Hosting

- DALL-E 3 zwraca bezpośredni URL do obrazu
- URL jest ważny przez ograniczony czas
- Nie trzeba hostować obrazu samodzielnie
- Walidacja dostępności URL przed wysłaniem

## Potencjalne Wyzwania

### 1. Synchronizacja z Aktualizacjami

- **Problem**: Opis zmienia się co 10 minut
- **Rozwiązanie**: Pobieranie na żądanie, bez cachowania między sesjami

### 2. Jakość Promptów

- **Problem**: Przekształcenie opisu tekstowego w efektywny prompt wizualny
- **Rozwiązanie**: Analiza struktury opisu i wzbogacenie o szczegóły wizualne

### 3. Koszty API

- **Problem**: DALL-E 3 jest relatywnie drogi
- **Rozwiązanie**: Optymalizacja promptów, unikanie niepotrzebnych wywołań

### 4. Walidacja Zgodności

- **Problem**: "The picture you sent us does not match the description"
- **Rozwiązanie**: Precyzyjne promptowanie, walidacja opisu przed generowaniem

### 5. Rate Limiting

- **Problem**: Ograniczenia API OpenAI i Centrali
- **Rozwiązanie**: Implementacja retry logic z odpowiednimi opóźnieniami

## Struktura Kodu

### Sekcje Single File Agent

```python
# 1. Imports i konfiguracja
# 2. Stałe i zmienne środowiskowe
# 3. Funkcje pomocnicze (API calls)
# 4. Funkcje przetwarzania (prompt preparation)
# 5. Funkcja główna (orchestration)
# 6. Obsługa błędów i logowanie
# 7. Punkt wejścia (__name__ == "__main__")
```

## Przykładowy Format Odpowiedzi

```json
{
    "task": "robotid",
    "apikey": "your-api-key-here",
    "answer": "https://oaidalleapiprodscus.blob.core.windows.net/private/org-xyz/user-abc/img-def.png"
}
```

## Walidacja i Testowanie

### Checklisty

- [ ] Pobieranie aktualnego opisu robota
- [ ] Generowanie obrazu DALL-E 3 w formacie PNG 1024x1024
- [ ] Publiczna dostępność URL obrazu
- [ ] Poprawny format JSON odpowiedzi
- [ ] Obsługa błędów API
- [ ] Logowanie procesu

### Scenariusze Testowe

1. **Happy Path**: Pomyślne wykonanie całego procesu
2. **API Errors**: Obsługa błędów API (rate limiting, timeouts)
3. **Invalid Description**: Reakcja na niepoprawny format opisu
4. **Network Issues**: Obsługa problemów z siecią
5. **URL Validation**: Sprawdzenie dostępności wygenerowanego obrazu

## Zgodność z Wzorcami Workspace

Implementacja zgodna z wzorcem Single File Agent używanym w:

- `adw/S02/S02E01/*/sfa.py`
- `adw/S02/S02E02/S02E02_sfa.py`

Zachowanie struktury:

- Sekcje konfiguracji
- Funkcje pomocnicze
- Główna funkcja orkiestracji
- Obsługa błędów
- Punkt wejścia

## Uwagi Końcowe

To zadanie stanowi doskonałe ćwiczenie w:

- Integrowaniu różnych API (pobieranie danych + generowanie obrazów)
- Pracy z modelami generatywnymi w kontekście automatyzacji
- Obsłudze dynamicznych danych (zmieniających się co 10 minut)
- Prompt engineering dla modeli wizualnych
- Budowaniu odpornych na błędy systemów agentowych

Kluczem do sukcesu jest precyzyjne promptowanie DALL-E 3 oraz robustna obsługa błędów API i problemów z siecią.
