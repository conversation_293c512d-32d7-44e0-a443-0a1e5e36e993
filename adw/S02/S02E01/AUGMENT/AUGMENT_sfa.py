"""
Single File Agent (SFA) dla zadania S02E01 - Lokalizacja Instytutu Profesora Maja

Ten skrypt implementuje agenta, który:
1. Transkrybuje nagrania audio na tekst
2. Analizuje transkrypcje przy użyciu LLM
3. <PERSON><PERSON>a nazwę ulicy, na której znajduje się instytut profesora Maja
4. Wysyła odpowiedź do API Centrali

Autor: Augment Agent
Data: 2023-05-22
Wersja: 1.1.0
"""

# --- IMPORTY ---
import os
import re
import json
import logging
import time
import requests
import glob
from typing import Dict, List, Any, Tuple, Optional, Union
from dotenv import load_dotenv
from openai import OpenAI, APIError, APIConnectionError, RateLimitError
import assemblyai as aai

# --- KONFIGURACJA ---
# Ładowanie zmiennych środowiskowych
load_dotenv()

# --- USTAWIENIA APLIKACJI ---
# Konfiguracja zadania
TASK_NAME = "mp3"
API_BASE_URL = "https://c3ntrala.ag3nts.org"
REPORT_ENDPOINT = f"{API_BASE_URL}/report"
API_KEY = os.getenv("AIDEVS_API_KEY")  # Klucz API dla Centrali

# Ścieżka do katalogu z plikami audio
AUDIO_DIR = "/Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S02/S02E01/ZZZ_TRIALS"  # Katalog z plikami audio

# Ścieżka do pliku z transkrypcjami
TRANSCRIPTIONS_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "transcriptions.json")

# Ustawienia żądań
REQUEST_TIMEOUT = 30  # sekundy
RETRY_ATTEMPTS = 0  # Bez ponownych prób
RETRY_DELAY = 2  # sekundy

# --- USTAWIENIA LLM ---
LLM_MODEL = os.getenv("KLUSTER_LLM_MODEL", os.getenv("OPENAI_LLM_MODEL"))
LLM_API_KEY = os.getenv("KLUSTER_API_KEY", os.getenv("OPENAI_API_KEY"))
LLM_BASE_URL = os.getenv("KLUSTER_BASE_URL", os.getenv("OPENAI_BASE_URL"))
LLM_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", "4000"))
LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.3"))
LLM_TOP_P = float(os.getenv("LLM_TOP_P", "1.0"))

# --- USTAWIENIA ASSEMBLY AI ---
ASSEMBLY_API_KEY = os.getenv("ASSEMBLY_API_KEY")
# Konfiguracja AssemblyAI
aai.settings.api_key = ASSEMBLY_API_KEY

# Ścieżka do pliku z system promptem
SYSTEM_PROMPT_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "AUGMENT_sys_prompt.txt")

# --- WZORCE REGEX ---
# Wzorce do wyszukiwania flagi
FLAG_REGEX = r"FLG[a-zA-Z0-9_-]+"
FLAG_REGEX_CURLY = r"\{\{FLG:[a-zA-Z0-9_-]+\}\}"

# --- KONFIGURACJA LOGOWANIA ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# --- STAŁE ---
# Komunikaty błędów
MAX_RETRIES_ERROR = "Osiągnięto maksymalną liczbę prób"
# Klucze dla formatu JSON
JSON_TITLE_KEY = "tytuł"
JSON_CONTENT_KEY = "treść"

# --- FUNKCJE POMOCNICZE ---

def validate_configuration() -> bool:
    """
    Sprawdza, czy wszystkie wymagane zmienne konfiguracyjne są ustawione.

    Returns:
        bool: True jeśli konfiguracja jest poprawna, False w przeciwnym razie
    """
    required_env_vars = {
        "AIDEVS_API_KEY": "Klucz API dla Centrali",
        "KLUSTER_API_KEY": "Klucz API dla Kluster.ai",
        "ASSEMBLY_API_KEY": "Klucz API dla AssemblyAI"
    }
    missing_vars = []

    for var, description in required_env_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"{var} ({description})")

    if missing_vars:
        logger.critical(f"Brakujące wymagane zmienne środowiskowe: {', '.join(missing_vars)}")
        return False

    # Sprawdź, czy API_KEY jest ustawiony
    if not API_KEY:
        logger.critical("API_KEY nie jest ustawiony, sprawdź zmienną środowiskową AIDEVS_API_KEY")
        return False

    # Sprawdź, czy LLM_API_KEY jest ustawiony
    if not LLM_API_KEY:
        logger.warning("LLM_API_KEY nie jest ustawiony, używanie KLUSTER_API_KEY")

    return True

def get_audio_files() -> Dict[str, bytes]:
    """
    Pobiera pliki audio z lokalnego katalogu.

    Returns:
        Dict[str, bytes]: Słownik z nazwami plików jako kluczami i danymi plików jako wartościami
    """
    logger.info(f"Pobieranie plików audio z katalogu: {AUDIO_DIR}")

    audio_files = {}

    try:
        # Znajdź wszystkie pliki .m4a w katalogu
        audio_file_paths = glob.glob(os.path.join(AUDIO_DIR, "*.m4a"))

        if not audio_file_paths:
            logger.error(f"Nie znaleziono plików audio w katalogu: {AUDIO_DIR}")
            return {}

        logger.info(f"Znaleziono {len(audio_file_paths)} plików audio: {[os.path.basename(p) for p in audio_file_paths]}")

        # Wczytaj każdy plik audio
        for file_path in audio_file_paths:
            file_name = os.path.basename(file_path)
            logger.info(f"Wczytywanie pliku: {file_name}")

            try:
                with open(file_path, "rb") as f:
                    audio_files[file_name] = f.read()
                logger.info(f"Plik {file_name} wczytany pomyślnie ({len(audio_files[file_name])} bajtów)")
            except Exception as e:
                logger.error(f"Błąd podczas wczytywania pliku {file_name}: {e}")

        logger.info(f"Wczytano {len(audio_files)} plików audio")
        return audio_files

    except Exception as e:
        logger.error(f"Błąd podczas wczytywania plików audio: {e}")
        return {}

def transcribe_audio(audio_data: bytes, file_name: str, attempt: int = 1) -> Optional[str]:
    """
    Transkrybuje dane audio na tekst przy użyciu AssemblyAI.

    Args:
        audio_data: Dane audio jako bajty
        file_name: Nazwa pliku audio (dla celów logowania)
        attempt: Numer bieżącej próby (dla logiki ponownych prób)

    Returns:
        Optional[str]: Transkrypcja tekstu lub None w przypadku niepowodzenia
    """
    logger.info(f"Transkrypcja pliku audio: {file_name} (próba {attempt}/{RETRY_ATTEMPTS + 1})")

    try:
        # Zapisz plik tymczasowo
        temp_file_path = f"temp_{file_name}"
        with open(temp_file_path, "wb") as f:
            f.write(audio_data)

        # Konfiguracja AssemblyAI
        config = aai.TranscriptionConfig(speech_model=aai.SpeechModel.best, language_code="pl")

        # Transkrypcja przy użyciu AssemblyAI
        logger.info(f"Rozpoczęcie transkrypcji pliku {file_name} przy użyciu AssemblyAI")
        transcript = aai.Transcriber(config=config).transcribe(temp_file_path)

        # Usuń plik tymczasowy
        os.remove(temp_file_path)

        # Sprawdź, czy transkrypcja się powiodła
        if transcript.status == "error":
            error_message = getattr(transcript, "error", "Nieznany błąd")
            logger.error(f"Błąd podczas transkrypcji: {error_message}")

            # Logika ponownych prób
            if attempt <= RETRY_ATTEMPTS:
                logger.info(f"Ponowna próba za {RETRY_DELAY} sekundy...")
                time.sleep(RETRY_DELAY)
                return transcribe_audio(audio_data, file_name, attempt + 1)
            else:
                logger.error(MAX_RETRIES_ERROR)
                return None

        logger.info(f"Transkrypcja zakończona pomyślnie: {len(transcript.text)} znaków")
        return transcript.text

    except Exception as e:
        logger.error(f"Nieoczekiwany błąd podczas transkrypcji: {e}")

        # Usuń plik tymczasowy w przypadku błędu
        if os.path.exists(f"temp_{file_name}"):
            os.remove(f"temp_{file_name}")

        # Logika ponownych prób
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Ponowna próba za {RETRY_DELAY} sekundy...")
            time.sleep(RETRY_DELAY)
            return transcribe_audio(audio_data, file_name, attempt + 1)
        else:
            logger.error(MAX_RETRIES_ERROR)
            return None

def prepare_context(transcriptions: Dict[str, str]) -> str:
    """
    Przygotowuje kontekst dla LLM na podstawie transkrypcji.

    Args:
        transcriptions: Słownik z nazwami plików jako kluczami i transkrypcjami jako wartościami

    Returns:
        str: Połączony tekst transkrypcji
    """
    logger.info("Przygotowywanie kontekstu dla LLM")

    context = "Poniżej znajdują się transkrypcje przesłuchań świadków:\n\n"

    for file_name, transcription in transcriptions.items():
        # Wyodrębnij nazwę świadka z nazwy pliku (zakładając format "Imię.m4a")
        witness_name = file_name.split('.')[0]
        context += f"--- Przesłuchanie: {witness_name} ---\n{transcription}\n\n"

    logger.info(f"Kontekst przygotowany: {len(context)} znaków")
    return context

def load_system_prompt() -> str:
    """
    Wczytuje system prompt z pliku.

    Returns:
        str: Zawartość pliku z system promptem
    """
    try:
        with open(SYSTEM_PROMPT_PATH, 'r', encoding='utf-8') as file:
            system_prompt = file.read()
        logger.info(f"Wczytano system prompt z pliku: {SYSTEM_PROMPT_PATH}")
        return system_prompt
    except Exception as e:
        logger.error(f"Błąd podczas wczytywania system promptu: {e}")
        logger.warning("Używanie domyślnego system promptu")
        return "Ustal nazwę ulicy, na której znajduje się instytut, gdzie wykłada profesor Andrzej Maj."

def call_llm(prompt: str, attempt: int = 1) -> Optional[str]:
    """
    Wywołuje LLM z podanym promptem i obsługuje błędy z logiką ponownych prób.
    Używa modelu DeepSeek-V3-0324 przez API Kluster.ai.

    Args:
        prompt: Prompt użytkownika do wysłania do LLM
        attempt: Numer bieżącej próby (dla logiki ponownych prób)

    Returns:
        Optional[str]: Odpowiedź LLM lub None w przypadku niepowodzenia
    """
    logger.info(f"Wywołanie LLM z promptem (próba {attempt}/{RETRY_ATTEMPTS + 1})")

    try:
        # Wczytaj system prompt z pliku
        system_prompt = load_system_prompt()

        client_params = {"api_key": LLM_API_KEY}
        if LLM_BASE_URL:
            client_params["base_url"] = LLM_BASE_URL

        client = OpenAI(**client_params)

        logger.info(f"Używanie modelu: {LLM_MODEL} przez API: {LLM_BASE_URL}")

        completion = client.chat.completions.create(
            model=LLM_MODEL,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            temperature=LLM_TEMPERATURE,
            max_completion_tokens=LLM_MAX_TOKENS,
            top_p=LLM_TOP_P
        )

        response = completion.choices[0].message.content.strip()
        logger.info("Wywołanie LLM zakończone pomyślnie")
        return response

    except (APIError, APIConnectionError, RateLimitError) as e:
        logger.error(f"Błąd API LLM: {e}")

        # Logika ponownych prób
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Ponowna próba za {RETRY_DELAY} sekundy...")
            time.sleep(RETRY_DELAY)
            return call_llm(prompt, attempt + 1)
        else:
            logger.error(MAX_RETRIES_ERROR)
            return None
    except Exception as e:
        logger.error(f"Nieoczekiwany błąd podczas wywołania LLM: {e}")
        return None

def send_answer(answer: str, attempt: int = 1) -> Optional[Dict]:
    """
    Wysyła odpowiedź do API Centrali.

    Args:
        answer: Odpowiedź do wysłania (nazwa ulicy)
        attempt: Numer bieżącej próby (dla logiki ponownych prób)

    Returns:
        Optional[Dict]: Odpowiedź z serwera lub None w przypadku niepowodzenia
    """
    logger.info(f"Wysyłanie odpowiedzi do {REPORT_ENDPOINT} (próba {attempt}/{RETRY_ATTEMPTS + 1})")

    # Upewnij się, że odpowiedź jest poprawnie sformatowana
    # Usuń białe znaki z początku i końca
    clean_answer = answer.strip()

    # Usuń cudzysłowy, jeśli są
    if (clean_answer.startswith('"') and clean_answer.endswith('"')) or \
       (clean_answer.startswith("'") and clean_answer.endswith("'")):
        clean_answer = clean_answer[1:-1]

    # UWAGA: Nie filtrujemy cyfr ani innych znaków - wysyłamy dokładnie to, co zostało wyodrębnione
    # z tagów <street></street> lub alternatywnej metody

    logger.info(f"Oryginalna odpowiedź: '{answer}'")
    logger.info(f"Oczyszczona odpowiedź: '{clean_answer}'")

    payload = {
        "task": TASK_NAME,
        "apikey": API_KEY,
        "answer": clean_answer  # Używamy oczyszczonej odpowiedzi
    }

    headers = {
        "Content-Type": "application/json; charset=utf-8"
    }

    try:
        # Logowanie pełnego payloadu przed wysłaniem
        logger.info(f"Wysyłanie payloadu: {json.dumps(payload, ensure_ascii=False)}")
        logger.info(f"Używany API_KEY: {API_KEY}")

        response = requests.post(
            REPORT_ENDPOINT,
            json=payload,  # Używamy json zamiast data, aby requests automatycznie serializował payload
            headers=headers,
            timeout=REQUEST_TIMEOUT
        )

        # Logowanie pełnej odpowiedzi serwera
        logger.info(f"Kod odpowiedzi: {response.status_code}")
        logger.info(f"Nagłówki odpowiedzi: {dict(response.headers)}")
        logger.info(f"Treść odpowiedzi: {response.text}")

        # Wyświetl pełną odpowiedź w konsoli
        print(f"\n{'='*50}")
        print(f"ODPOWIEDŹ Z CENTRALI:")
        print(f"Kod: {response.status_code}")
        print(f"Nagłówki: {dict(response.headers)}")
        print(f"Treść: {response.text}")
        print(f"{'='*50}\n")

        response.raise_for_status()

        result = response.json()
        logger.info("Odpowiedź wysłana pomyślnie")
        return result

    except requests.exceptions.RequestException as e:
        logger.error(f"Błąd podczas wysyłania odpowiedzi: {e}")

        if hasattr(e, 'response') and hasattr(e.response, 'text'):
            logger.error(f"Odpowiedź serwera: {e.response.text}")
            logger.error(f"Nagłówki odpowiedzi: {dict(e.response.headers) if hasattr(e.response, 'headers') else 'Brak nagłówków'}")

            # Wyświetl pełną odpowiedź błędu w konsoli
            print(f"\n{'='*50}")
            print(f"BŁĄD Z CENTRALI:")
            print(f"Kod: {e.response.status_code if hasattr(e.response, 'status_code') else 'Brak kodu'}")
            print(f"Nagłówki: {dict(e.response.headers) if hasattr(e.response, 'headers') else 'Brak nagłówków'}")
            print(f"Treść: {e.response.text}")
            print(f"{'='*50}\n")

        # Logika ponownych prób
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Ponowna próba za {RETRY_DELAY} sekundy...")
            time.sleep(RETRY_DELAY)
            return send_answer(answer, attempt + 1)
        else:
            logger.error(MAX_RETRIES_ERROR)
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Błąd dekodowania odpowiedzi JSON: {e}")
        return None

def check_for_flag(text: str) -> Optional[str]:
    """
    Sprawdza, czy tekst zawiera flagę w formatach:
    - FLG[a-zA-Z0-9_-]+
    - {{FLG:[a-zA-Z0-9_-]+}}

    Args:
        text: Tekst do sprawdzenia

    Returns:
        Optional[str]: Znaleziona flaga lub None, jeśli nie znaleziono
    """
    if not text:
        return None

    # Wyszukaj flagę w formacie {{FLG:XXXX}}
    match_curly = re.search(FLAG_REGEX_CURLY, text)
    if match_curly:
        flag = match_curly.group(0)
        logger.info(f"Znaleziono flagę w formacie {{{{FLG:XXXX}}}}: {flag}")
        return flag

    # Wyszukaj flagę w formacie FLG[a-zA-Z0-9_-]+
    match = re.search(FLAG_REGEX, text)
    if match:
        flag = match.group(0)
        logger.info(f"Znaleziono flagę w formacie FLG[a-zA-Z0-9_-]+: {flag}")
        return flag

    # Sprawdź, czy tekst zawiera słowo "flag" lub "FLG"
    if "flag" in text.lower() or "flg" in text.lower():
        logger.info(f"Tekst zawiera słowo 'flag' lub 'FLG': {text}")
        return text

    logger.info("Nie znaleziono flagi w tekście")
    return None

def save_transcriptions(transcriptions: Dict[str, str]) -> bool:
    """
    Zapisuje transkrypcje do pliku JSON w formacie zbioru obiektów z tytułem i treścią.

    Args:
        transcriptions: Słownik z nazwami plików jako kluczami i transkrypcjami jako wartościami

    Returns:
        bool: True jeśli zapis się powiódł, False w przeciwnym razie
    """
    try:
        # Przygotuj dane w formacie zbioru obiektów
        formatted_transcriptions = []

        # Sprawdź, czy plik już istnieje
        if os.path.exists(TRANSCRIPTIONS_FILE):
            logger.info(f"Plik z transkrypcjami już istnieje: {TRANSCRIPTIONS_FILE}")
            # Wczytaj istniejące transkrypcje
            with open(TRANSCRIPTIONS_FILE, 'r', encoding='utf-8') as f:
                formatted_transcriptions = json.load(f)

            # Utwórz słownik istniejących transkrypcji dla łatwiejszego wyszukiwania
            existing_titles = {item[JSON_TITLE_KEY]: i for i, item in enumerate(formatted_transcriptions)}

            # Dodaj lub zaktualizuj transkrypcje
            for file_name, transcription_text in transcriptions.items():
                if file_name in existing_titles:
                    # Zaktualizuj istniejącą transkrypcję
                    index = existing_titles[file_name]
                    formatted_transcriptions[index][JSON_CONTENT_KEY] = transcription_text
                    logger.info(f"Zaktualizowano transkrypcję dla pliku: {file_name}")
                else:
                    # Dodaj nową transkrypcję
                    formatted_transcriptions.append({
                        JSON_TITLE_KEY: file_name,
                        JSON_CONTENT_KEY: transcription_text
                    })
                    logger.info(f"Dodano nową transkrypcję dla pliku: {file_name}")
        else:
            # Utwórz nowy plik z transkrypcjami
            for file_name, transcription_text in transcriptions.items():
                formatted_transcriptions.append({
                    JSON_TITLE_KEY: file_name,
                    JSON_CONTENT_KEY: transcription_text
                })
                logger.info(f"Dodano transkrypcję dla pliku: {file_name}")

        # Zapisz transkrypcje do pliku
        with open(TRANSCRIPTIONS_FILE, 'w', encoding='utf-8') as f:
            json.dump(formatted_transcriptions, f, ensure_ascii=False, indent=2)

        logger.info(f"Zapisano {len(transcriptions)} transkrypcji do pliku: {TRANSCRIPTIONS_FILE}")
        return True

    except Exception as e:
        logger.error(f"Błąd podczas zapisywania transkrypcji: {e}")
        return False

def load_transcriptions() -> Dict[str, str]:
    """
    Wczytuje transkrypcje z pliku JSON.

    Returns:
        Dict[str, str]: Słownik z nazwami plików jako kluczami i transkrypcjami jako wartościami
    """
    try:
        if os.path.exists(TRANSCRIPTIONS_FILE):
            with open(TRANSCRIPTIONS_FILE, 'r', encoding='utf-8') as f:
                formatted_transcriptions = json.load(f)

            # Konwertuj format zbioru obiektów na słownik
            transcriptions = {}
            for item in formatted_transcriptions:
                if JSON_TITLE_KEY in item and JSON_CONTENT_KEY in item:
                    transcriptions[item[JSON_TITLE_KEY]] = item[JSON_CONTENT_KEY]

            logger.info(f"Wczytano {len(transcriptions)} transkrypcji z pliku: {TRANSCRIPTIONS_FILE}")
            return transcriptions
        else:
            logger.warning(f"Plik z transkrypcjami nie istnieje: {TRANSCRIPTIONS_FILE}")
            return {}

    except Exception as e:
        logger.error(f"Błąd podczas wczytywania transkrypcji: {e}")
        return {}

def prepare_audio_data() -> Optional[str]:
    """
    Przygotowuje dane audio: wczytuje pliki audio, transkrybuje je i przygotowuje kontekst.

    Returns:
        Optional[str]: Przygotowany kontekst lub None w przypadku niepowodzenia
    """
    try:
        # Sprawdź, czy istnieją już transkrypcje
        existing_transcriptions = load_transcriptions()
        if existing_transcriptions:
            logger.info(f"Znaleziono {len(existing_transcriptions)} istniejących transkrypcji")

        # Krok 1: Pobierz pliki audio z lokalnego katalogu
        audio_files = get_audio_files()
        if not audio_files:
            logger.error("Nie znaleziono plików audio. Wyjście.")
            return None

        # Krok 2: Transkrybuj tylko te pliki, które nie mają jeszcze transkrypcji
        new_transcriptions = {}
        for file_name, audio_data in audio_files.items():
            if file_name in existing_transcriptions:
                logger.info(f"Plik {file_name} ma już transkrypcję, pomijam")
                continue

            logger.info(f"Transkrybuję plik: {file_name}")
            transcription = transcribe_audio(audio_data, file_name)
            if transcription:
                new_transcriptions[file_name] = transcription

        # Krok 3: Zapisz nowe transkrypcje
        if new_transcriptions:
            logger.info(f"Zapisuję {len(new_transcriptions)} nowych transkrypcji")
            save_transcriptions(new_transcriptions)

        # Krok 4: Połącz wszystkie transkrypcje
        all_transcriptions = {**existing_transcriptions, **new_transcriptions}

        if not all_transcriptions:
            logger.error("Nie udało się transkrybować żadnego pliku audio. Wyjście.")
            return None

        # Krok 5: Przygotuj kontekst dla LLM
        return prepare_context(all_transcriptions)

    except Exception as e:
        logger.error(f"Błąd podczas przygotowywania danych audio: {e}")
        return None

def extract_street_name(text: str) -> str:
    """
    Wyodrębnia nazwę ulicy z odpowiedzi LLM, szukając tagów <street></street>.

    Args:
        text: Odpowiedź LLM

    Returns:
        str: Wyodrębniona nazwa ulicy
    """
    # Szukaj tagów <street></street>
    import re
    street_pattern = re.compile(r'<street>(.*?)</street>', re.DOTALL)
    match = street_pattern.search(text)

    if match:
        # Jeśli znaleziono tagi <street></street>, wyodrębnij całą zawartość (włącznie z cyframi i innymi znakami)
        street_name = match.group(1).strip()
        logger.info(f"Znaleziono nazwę ulicy w tagach <street></street>: '{street_name}'")
        # Nie filtrujemy cyfr ani innych znaków - wysyłamy dokładnie to, co jest w tagach
        return street_name

    # Jeśli nie znaleziono tagów, użyj alternatywnej metody
    logger.warning("Nie znaleziono tagów <street></street>, używanie alternatywnej metody")

    # Usuń białe znaki z początku i końca
    clean_text = text.strip()

    # Jeśli odpowiedź zawiera wiele linii, weź tylko ostatnią (która powinna zawierać samą nazwę)
    if "\n" in clean_text:
        lines = clean_text.split("\n")
        # Usuń puste linie
        lines = [line.strip() for line in lines if line.strip()]
        if lines:
            clean_text = lines[-1].strip()  # Weź ostatnią niepustą linię

    # Usuń cudzysłowy, jeśli są
    if (clean_text.startswith('"') and clean_text.endswith('"')) or \
       (clean_text.startswith("'") and clean_text.endswith("'")):
        clean_text = clean_text[1:-1]

    # Usuń prefiksy w języku polskim
    polish_prefixes = ["ulica ", "ul. ", "Ulica ", "Ul. "]
    for prefix in polish_prefixes:
        if clean_text.startswith(prefix):
            clean_text = clean_text[len(prefix):]
            break

    # Usuń prefiksy w języku angielskim
    english_prefixes = ["street ", "st. ", "Street ", "St. ", "avenue ", "ave. ", "Avenue ", "Ave. "]
    for prefix in english_prefixes:
        if clean_text.startswith(prefix):
            clean_text = clean_text[len(prefix):]
            break

    # Usuń frazy typu "The answer is" lub "Final answer:"
    phrases_to_remove = [
        "The answer is ", "the answer is ",
        "Final answer: ", "final answer: ",
        "Answer: ", "answer: ",
        "Location: ", "location: "
    ]
    for phrase in phrases_to_remove:
        if clean_text.startswith(phrase):
            clean_text = clean_text[len(phrase):]
            break

    logger.info(f"Wyodrębniona nazwa ulicy (alternatywna metoda): '{clean_text}'")
    return clean_text



def transcribe_all_files():
    """
    Transkrybuje wszystkie pliki audio z katalogu i zapisuje wyniki do pliku JSON.

    Returns:
        bool: True jeśli transkrypcja się powiodła, False w przeciwnym razie
    """
    logger.info("--- Rozpoczęcie transkrypcji wszystkich plików audio ---")

    # Sprawdź konfigurację
    if not validate_configuration():
        logger.critical("Nieprawidłowa konfiguracja. Wyjście.")
        return False

    # Sprawdź, czy katalog z plikami audio istnieje
    if not os.path.exists(AUDIO_DIR):
        logger.critical(f"Katalog z plikami audio nie istnieje: {AUDIO_DIR}")
        return False

    try:
        # Pobierz pliki audio
        audio_files = get_audio_files()
        if not audio_files:
            logger.error("Nie znaleziono plików audio. Wyjście.")
            return False

        logger.info(f"Znaleziono {len(audio_files)} plików audio do transkrypcji")

        # Wczytaj istniejące transkrypcje
        existing_transcriptions = load_transcriptions()

        # Transkrybuj tylko te pliki, które nie mają jeszcze transkrypcji
        new_transcriptions = {}
        for file_name, audio_data in audio_files.items():
            if file_name in existing_transcriptions:
                logger.info(f"Plik {file_name} ma już transkrypcję, pomijam")
                continue

            logger.info(f"Transkrybuję plik: {file_name}")
            transcription = transcribe_audio(audio_data, file_name)
            if transcription:
                new_transcriptions[file_name] = transcription
                # Zapisz transkrypcję natychmiast po każdym pliku
                save_transcriptions({file_name: transcription})

        # Podsumowanie
        total_transcriptions = len(existing_transcriptions) + len(new_transcriptions)
        logger.info(f"Zakończono transkrypcję. Łącznie {total_transcriptions} plików zostało transkrybowanych.")
        logger.info(f"Nowe transkrypcje: {len(new_transcriptions)}, Istniejące transkrypcje: {len(existing_transcriptions)}")

        return True

    except Exception as e:
        logger.critical(f"Nieoczekiwany błąd podczas transkrypcji: {e}", exc_info=True)
        return False

    finally:
        logger.info("--- Transkrypcja zakończona ---")

def process_all_transcriptions():
    """
    Przetwarza wszystkie transkrypcje z pliku JSON w jednym zapytaniu do LLM.
    """
    logger.info("--- Rozpoczęcie przetwarzania wszystkich transkrypcji ---")

    # Sprawdź konfigurację
    if not validate_configuration():
        logger.critical("Nieprawidłowa konfiguracja. Wyjście.")
        return

    try:
        # Sprawdź, czy plik z transkrypcjami istnieje
        if not os.path.exists(TRANSCRIPTIONS_FILE):
            logger.critical(f"Plik z transkrypcjami nie istnieje: {TRANSCRIPTIONS_FILE}")

            # Sprawdź, czy katalog z plikami audio istnieje
            if not os.path.exists(AUDIO_DIR):
                logger.critical(f"Katalog z plikami audio nie istnieje: {AUDIO_DIR}")
                return

            # Transkrybuj pliki audio
            if not transcribe_all_files():
                logger.error("Nie udało się transkrybować plików audio. Wyjście.")
                return

        # Wczytaj transkrypcje bezpośrednio z pliku JSON
        try:
            with open(TRANSCRIPTIONS_FILE, 'r', encoding='utf-8') as f:
                transcriptions_data = json.load(f)

            logger.info(f"Wczytano {len(transcriptions_data)} transkrypcji z pliku: {TRANSCRIPTIONS_FILE}")
        except Exception as e:
            logger.error(f"Błąd podczas wczytywania transkrypcji z pliku: {e}")
            return

        # Przygotuj kontekst dla wszystkich transkrypcji
        context = "Poniżej znajdują się transkrypcje przesłuchań świadków:\n\n"

        # Dodaj wszystkie transkrypcje do kontekstu
        witness_count = 0
        for item in transcriptions_data:
            if JSON_TITLE_KEY in item and JSON_CONTENT_KEY in item:
                witness_name = item[JSON_TITLE_KEY].split('.')[0]
                context += f"--- Przesłuchanie: {witness_name} ---\n{item[JSON_CONTENT_KEY]}\n\n"
                logger.info(f"Dodano osobę do kontekstu: {witness_name}")
                witness_count += 1

        logger.info(f"Przygotowano kontekst: {len(context)} znaków, liczba świadków: {witness_count}")

        # Wywołaj LLM dla wszystkich transkrypcji (w języku polskim)
        prompt = f"Teraz zgodnie z instrukcjami i na podstawie dostarczonych zeznań znajdź nazwę ulicy przy której znajduje się instytut profesora Maja. Przedstaw szczegółowy proces rozumowania, a następnie podaj nazwę ulicy w tagach <street></street>.\n\n{context}"
        llm_response = call_llm(prompt)

        if not llm_response:
            logger.error("Nie udało się uzyskać odpowiedzi od LLM. Wyjście.")
            return

        # Logowanie pełnej odpowiedzi LLM
        logger.info(f"Pełna odpowiedź LLM: '{llm_response}'")

        # Sprawdź, czy odpowiedź zawiera flagę
        flag = check_for_flag(llm_response)
        if flag:
            logger.info(f"SUKCES! Znaleziono flagę: {flag}")
            print(f"\n{'='*50}\nFLAGA: {flag}\n{'='*50}\n")
            return

        # Wyodrębnij nazwę ulicy z odpowiedzi LLM
        street_name = extract_street_name(llm_response)

        # Wyślij odpowiedź do API Centrali
        response = send_answer(street_name)

        if not response:
            logger.error("Nie udało się wysłać odpowiedzi. Wyjście.")
            return

        # Sprawdź odpowiedź z API pod kątem flagi
        flag = check_for_flag(str(response))

        if flag:
            logger.info(f"SUKCES! Znaleziono flagę: {flag}")
            print(f"\n{'='*50}\nFLAGA: {flag}\n{'='*50}\n")
            return

        logger.info("Przetwarzanie zakończone bez znalezienia flagi.")
        print(f"\n{'='*50}\nOSTATECZNA ODPOWIEDŹ: {json.dumps(response, indent=2)}\n{'='*50}\n")

    except Exception as e:
        logger.critical(f"Nieoczekiwany błąd: {e}", exc_info=True)

    logger.info("--- Przetwarzanie wszystkich transkrypcji zakończone ---")

def main():
    """
    Główna funkcja koordynująca przepływ pracy agenta.
    """
    logger.info(f"--- Rozpoczęcie agenta {TASK_NAME} ---")

    # Sprawdź, czy chcemy tylko transkrybować pliki
    transcribe_only = os.getenv("TRANSCRIBE_ONLY", "False").lower() in ("true", "1", "t")

    if transcribe_only:
        logger.info("Tryb: tylko transkrypcja plików audio")
        transcribe_all_files()
    else:
        logger.info("Tryb: pełne wykonanie zadania")
        process_all_transcriptions()

    logger.info(f"--- Agent {TASK_NAME} zakończony ---")

# --- PUNKT WEJŚCIA ---

if __name__ == "__main__":
    main()
