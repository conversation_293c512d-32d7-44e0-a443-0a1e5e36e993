# Analiza Zadania S02E01: Lokalizacja Instytutu Profesora Maja

## Cel Zadania

Głównym celem zadania jest ustalenie nazwy ulicy, na której znajduje się konkretny instytut uczelni, gdzie wykłada profesor Andrzej Maj. Informacje potrzebne do rozwiązania zadania są ukryte w nagraniach przesłuchań świadków.

## Szczegółowy Opis Zadania

### Dane Wejściowe

- Pliki audio w formacie `.m4a` znajdujące się w lokalnym katalogu
- Nagrania zawierają zeznania świadków, którzy mieli kontakt z profesorem Majem
- Jedno z nagrań (Rafała) jest szczególnie ważne, ale może być chaotyczne
- Zeznania mogą zawierać sprzeczne lub uzupełniające się informacje

### Oczekiwany Wynik

- Na<PERSON>wa ul<PERSON>, na której znajduje się instytut, gdzie wykłada profesor Maj
- Odpowiedź w formacie JSON wysłana do API Centrali
- Odpowiedź powinna być zakodowana w UTF-8
- Odpowiedź powinna zawierać nazwę ulicy w tagach `<street></street>`
- System powinien wykrywać flagę w odpowiedzi w formatach:
  - `FLG[a-zA-Z0-9_-]+`
  - `{{FLG:[a-zA-Z0-9_-]+}}`

### Kluczowe Wyzwania

- Odróżnienie adresu instytutu od adresu głównej siedziby uczelni
- Analiza potencjalnie sprzecznych informacji z różnych zeznań
- Interpretacja chaotycznego nagrania Rafała
- Wykorzystanie wiedzy o strukturze polskich uczelni
- Prawidłowe wyodrębnienie nazwy ulicy z odpowiedzi LLM
- Obsługa różnych formatów flag w odpowiedzi

## Plan Działania

### 1. Przygotowanie Środowiska

- Skonfigurować zmienne środowiskowe dla kluczy API:
  - `AIDEVS_API_KEY` - klucz API dla Centrali
  - `KLUSTER_API_KEY` - klucz API dla Kluster.ai (alternatywa dla OpenAI)
  - `ASSEMBLY_API_KEY` - klucz API dla AssemblyAI (transkrypcja audio)
- Skonfigurować zmienne środowiskowe dla modelu LLM:
  - `LLM_MODEL` - model do użycia (domyślnie "deepseek-ai/DeepSeek-V3-0324")
  - `LLM_BASE_URL` - bazowy URL API (domyślnie "https://api.kluster.ai/v1")

### 2. Transkrypcja Nagrań

- Wykorzystać AssemblyAI do transkrypcji plików audio
- Zapisać transkrypcje w formacie JSON z podziałem na świadków
- Implementować mechanizm buforowania transkrypcji, aby uniknąć ponownej transkrypcji tych samych plików

### 3. Przygotowanie Kontekstu dla LLM

- Połączyć wszystkie transkrypcje w jeden spójny tekst
- Zachować informację o źródle każdej transkrypcji (imię świadka)
- Dodać odpowiednie nagłówki i formatowanie dla lepszej czytelności

### 4. Sformułowanie Promptu dla LLM

- Stworzyć precyzyjny system prompt w języku polskim zawierający:
  - Jasne określenie celu (nazwa ulicy konkretnego instytutu)
  - Instrukcję analizy krok po kroku (10 kroków)
  - Wskazówkę o zwróceniu szczególnej uwagi na zeznanie Rafała
  - Instrukcję zwrócenia nazwy ulicy w tagach `<street></street>`
- Sformułować prompt użytkownika w języku polskim

### 5. Analiza przez LLM

- Przekazać system prompt i prompt użytkownika wraz z transkrypcjami do modelu LLM
- Wykorzystać model DeepSeek-V3-0324 przez API Kluster.ai
- Uzyskać odpowiedź zawierającą nazwę ulicy w tagach `<street></street>`

### 6. Wyodrębnienie Nazwy Ulicy

- Wyodrębnić nazwę ulicy z odpowiedzi LLM przy użyciu wyrażeń regularnych
- Szukać tagów `<street></street>` w odpowiedzi
- Jeśli tagi nie zostaną znalezione, użyć alternatywnej metody ekstrakcji

### 7. Wysłanie Odpowiedzi

- Sformatować odpowiedź w wymaganym formacie JSON:

  ```json
  {
    "task": "mp3",
    "apikey": "YOUR_API_KEY",
    "answer": "Nazwa ulicy"
  }
  ```

- Wysłać odpowiedź metodą POST do API Centrali z nagłówkiem `Content-Type: application/json; charset=utf-8`
- Sprawdzić odpowiedź pod kątem obecności flagi

## Proponowany Stack Technologiczny

### Język Programowania

- **Python** - ze względu na bogactwo bibliotek do przetwarzania audio i integracji z LLM

### Biblioteki i Narzędzia

- **requests** - do wysyłania odpowiedzi do API Centrali
- **openai** - do korzystania z API Kluster.ai (kompatybilne z OpenAI)
- **assemblyai** - do transkrypcji audio
- **python-dotenv** - do zarządzania zmiennymi środowiskowymi
- **re** - do przetwarzania wyrażeń regularnych
- **json** - do obsługi formatu JSON
- **logging** - do logowania informacji i błędów

### Modele

- **Transkrypcja Audio**: AssemblyAI z modelem `speech_model=aai.SpeechModel.best` i `language_code="pl"`
- **Analiza Tekstu**: DeepSeek-V3-0324 przez API Kluster.ai

### Struktura Projektu

- **AUGMENT_sfa.py** - Single File Agent implementujący całą logikę
- **AUGMENT_sys_prompt.txt** - Plik z promptem systemowym
- **transcriptions.json** - Plik z zapisanymi transkrypcjami

## Uwagi Końcowe

- Kluczem do sukcesu jest precyzyjne sformułowanie promptu dla LLM w języku polskim
- Warto zwrócić szczególną uwagę na zeznanie Rafała jako potencjalnie najbardziej wartościowe źródło informacji
- Należy pamiętać o rozróżnieniu między adresem instytutu a adresem głównej siedziby uczelni
- Odpowiedź powinna zawierać nazwę ulicy w tagach `<street></street>`
- Należy obsługiwać różne formaty flag w odpowiedzi
- Wszystkie dane powinny być kodowane w UTF-8
- Należy implementować mechanizm buforowania transkrypcji, aby uniknąć ponownej transkrypcji tych samych plików
- Należy logować wszystkie kroki i błędy dla łatwiejszego debugowania
