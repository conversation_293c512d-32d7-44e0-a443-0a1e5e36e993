# Single File Agent (SFA) dla zadania S02E01 - Lokalizacja Instytutu Profesora Maja

## Opis zadania

Celem zadania jest ustalenie nazwy ulicy, przy której znajduje się instytut, gdzie wykłada profesor Andrzej Maj. Informacje potrzebne do rozwiązania zadania są ukryte w nagraniach przesłuchań świadków w formacie `.m4a`.

## Struktura agenta

Agent został zaimplementowany jako pojedy<PERSON>zy plik Python (`AUGMENT_sfa.py`), który wykonuje następujące zadania:

1. Transkrybuje nagrania audio na tekst przy użyciu AssemblyAI
2. Analizuje transkrypcje przy użyciu modelu LLM (DeepSeek-V3-0324)
3. Ustala nazwę ulicy, na której znajduje się instytut profesora Maja
4. <PERSON><PERSON><PERSON><PERSON> odpowiedź do API Centrali

### Pliki projektu

- **AUGMENT_sfa.py** - główny plik agenta zawierający całą logikę
- **AUGMENT_sys_prompt.txt** - plik zawierający systemowy prompt dla modelu LLM
- **transcriptions.json** - plik przechowujący transkrypcje nagrań (tworzony automatycznie)
- **README.md** - dokumentacja projektu (ten plik)
- **AUGMENT_analiza.md** - analiza zadania
- **AUGMENT_IM_plan.md** - szczegółowy plan implementacji

## Wymagania

### Zależności

Agent wymaga następujących bibliotek Python:

- requests
- python-dotenv
- openai
- assemblyai

Można je zainstalować za pomocą:

```bash
pip install requests python-dotenv openai assemblyai
```

### Zmienne środowiskowe

Przed uruchomieniem agenta należy skonfigurować następujące zmienne środowiskowe:

- `AIDEVS_API_KEY` - klucz API dla Centrali (c3ntrala.ag3nts.org)
- `KLUSTER_API_KEY` - klucz API dla Kluster.ai (alternatywa dla OpenAI)
- `ASSEMBLY_API_KEY` - klucz API dla AssemblyAI (transkrypcja audio)

Opcjonalnie można skonfigurować:

- `LLM_MODEL` - model do użycia (domyślnie "deepseek-ai/DeepSeek-V3-0324")
- `LLM_BASE_URL` - bazowy URL API (domyślnie "<https://api.kluster.ai/v1>")
- `LLM_MAX_TOKENS` - maksymalna liczba tokenów w odpowiedzi (domyślnie 4000)
- `LLM_TEMPERATURE` - temperatura generowania (domyślnie 0.6)
- `LLM_TOP_P` - parametr top_p (domyślnie 1.0)
- `TRANSCRIBE_ONLY` - jeśli ustawione na "true", agent tylko transkrybuje pliki audio bez analizy (domyślnie "false")

Zmienne środowiskowe można ustawić w pliku `.env` w katalogu projektu:

```
AIDEVS_API_KEY=twój_klucz_api
KLUSTER_API_KEY=twój_klucz_kluster
ASSEMBLY_API_KEY=twój_klucz_assembly
```

## Workflow agenta

Agent działa w następujący sposób:

1. **Inicjalizacja**:
   - Ładowanie zmiennych środowiskowych
   - Konfiguracja logowania
   - Walidacja konfiguracji

2. **Transkrypcja audio**:
   - Wczytanie plików audio z lokalnego katalogu
   - Transkrypcja plików audio przy użyciu AssemblyAI
   - Zapisanie transkrypcji do pliku JSON dla ponownego użycia

3. **Analiza transkrypcji**:
   - Wczytanie transkrypcji z pliku JSON
   - Przygotowanie kontekstu dla modelu LLM
   - Wczytanie systemowego promptu z pliku
   - Wywołanie modelu LLM z przygotowanym promptem i kontekstem

4. **Przetwarzanie odpowiedzi**:
   - Wyodrębnienie nazwy ulicy z odpowiedzi LLM (z tagów `<street></street>`)
   - Wysłanie odpowiedzi do API Centrali
   - Sprawdzenie odpowiedzi pod kątem obecności flagi

## Uruchomienie agenta

### Standardowe uruchomienie

Aby uruchomić agenta w standardowym trybie (transkrypcja + analiza + wysłanie odpowiedzi):

```bash
python AUGMENT_sfa.py
```

### Tryb tylko transkrypcji

Aby uruchomić agenta tylko w trybie transkrypcji (bez analizy i wysyłania odpowiedzi):

```bash
TRANSCRIBE_ONLY=true python AUGMENT_sfa.py
```

lub

```bash
export TRANSCRIBE_ONLY=true
python AUGMENT_sfa.py
```

## Szczegóły implementacji

### Konfiguracja ścieżek

Agent zakłada, że pliki audio znajdują się w katalogu:

```
/Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S02/S02E01/ZZZ_TRIALS
```

Można to zmienić modyfikując zmienną `AUDIO_DIR` w pliku `AUGMENT_sfa.py`.

### Buforowanie transkrypcji

Agent implementuje mechanizm buforowania transkrypcji, aby uniknąć ponownej transkrypcji tych samych plików. Transkrypcje są zapisywane w pliku `transcriptions.json` w formacie:

```json
[
  {
    "tytuł": "nazwa_pliku.m4a",
    "treść": "transkrypcja tekstu"
  },
  ...
]
```

### Obsługa błędów

Agent implementuje kompleksową obsługę błędów:

- Walidacja konfiguracji przed uruchomieniem
- Obsługa błędów API (AssemblyAI, Kluster.ai, Centrala)
- Logowanie wszystkich kroków i błędów
- Bezpieczne zamykanie zasobów

### Wyodrębnianie nazwy ulicy

Agent wyodrębnia nazwę ulicy z odpowiedzi LLM na dwa sposoby:

1. Główny: wyszukiwanie tagów `<street></street>` w odpowiedzi
2. Alternatywny: analiza tekstu odpowiedzi, jeśli tagi nie zostały znalezione

### Wykrywanie flagi

Agent wykrywa flagę w odpowiedzi w dwóch formatach:

- `FLG[a-zA-Z0-9_-]+`
- `{{FLG:[a-zA-Z0-9_-]+}}`

## Przykładowe użycie

```bash
# Ustawienie zmiennych środowiskowych
export AIDEVS_API_KEY=twój_klucz_api
export KLUSTER_API_KEY=twój_klucz_kluster
export ASSEMBLY_API_KEY=twój_klucz_assembly

# Uruchomienie agenta
python AUGMENT_sfa.py
```

## Rozwiązywanie problemów

### Brak transkrypcji

Jeśli agent nie może transkrybować plików audio:

1. Sprawdź, czy klucz API AssemblyAI jest poprawny
2. Sprawdź, czy pliki audio istnieją w katalogu `AUDIO_DIR`
3. Sprawdź, czy pliki audio są w formacie `.m4a`

### Brak odpowiedzi od LLM

Jeśli agent nie może uzyskać odpowiedzi od LLM:

1. Sprawdź, czy klucz API Kluster.ai jest poprawny
2. Sprawdź, czy model LLM jest dostępny
3. Sprawdź, czy prompt systemowy jest poprawny

### Brak odpowiedzi od Centrali

Jeśli agent nie może wysłać odpowiedzi do Centrali:

1. Sprawdź, czy klucz API Centrali jest poprawny
2. Sprawdź, czy endpoint API Centrali jest dostępny
3. Sprawdź, czy format odpowiedzi jest poprawny

## Autor

Augment Agent
