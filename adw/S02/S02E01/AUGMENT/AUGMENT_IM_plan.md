# Plan implementacji Single File Agent (SFA) dla zadania S02E01

## Analiza wymagań

Na podstawie analizy dokumentów i przykładowego kodu, zadanie polega na:

1. Pobraniu archiwum `przesluchania.zip` zawierającego pliki audio w formacie `.m4a`
2. Transkrypcji nagrań audio na tekst przy użyciu modelu ASR (np. OpenAI Whisper)
3. Przygotowaniu kontekstu dla modelu LLM na podstawie transkrypcji
4. Wysłaniu zapytania do LLM z odpowiednim promptem systemowym
5. Wyodrębnieniu nazwy ulicy z odpowiedzi LLM
6. Wysłaniu odpowiedzi do API Centrali w formacie JSON
7. Weryfikacji odpowiedzi pod kątem obecności flagi

## Struktura aplikacji

Aplikacja będzie zaimplementowana jako pojedync<PERSON> plik Python (SFA) z następującymi sekcjami:

1. **Importy i konfiguracja** - import niezbędnych bibliotek i konfiguracja zmiennych środowiskowych
2. **Stałe i zmienne globalne** - definicja stałych i zmiennych globalnych
3. **Funkcje pomocnicze** - funkcje do pobierania danych, transkrypcji, komunikacji z LLM, itp.
4. **Funkcja główna** - koordynacja całego procesu
5. **Punkt wejścia** - uruchomienie aplikacji

## Szczegółowy plan implementacji

### 1. Importy i konfiguracja

```python
import os
import re
import json
import logging
import time
import requests
import zipfile
import io
from typing import Dict, List, Any, Tuple, Optional, Union
from dotenv import load_dotenv
from openai import OpenAI, APIError, APIConnectionError, RateLimitError
```

### 2. Stałe i zmienne globalne

```python
# Ładowanie zmiennych środowiskowych
load_dotenv()

# Konfiguracja zadania
TASK_NAME = "mp3"
API_BASE_URL = "https://c3ntrala.ag3nts.org"
DATA_URL = f"{API_BASE_URL}/dane/przesluchania.zip"
REPORT_ENDPOINT = f"{API_BASE_URL}/answer"
API_KEY = os.getenv("API_KEY")

# Konfiguracja LLM
LLM_MODEL = os.getenv("LLM_MODEL", "gpt-4o")
LLM_API_KEY = os.getenv("OPENAI_API_KEY")
LLM_BASE_URL = os.getenv("OPENAI_API_BASE_URL")  # Opcjonalne, dla niestandardowych endpointów

# Konfiguracja logowania
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# System prompt dla LLM
SYSTEM_PROMPT = """
Jesteś agentem wyspecjalizowanym w analizie danych tekstowych i wykorzystaniu wiedzy o świecie do rozwiązywania zadań. Twoim celem jest ustalenie nazwy ulicy, na której znajduje się KONKRETNY INSTYTUT uczelni, gdzie wykłada profesor Andrzej Maj.

Otrzymasz tekst będący transkrypcją nagrań z przesłuchań świadków. Ten tekst jest Twoim głównym źródłem informacji o profesorze Maju i jego powiązaniach z uczelnią. Pamiętaj, że model nie posiada wcześniejszej wiedzy o profesorze Maju; cała wiedza o nim musi pochodzić z dostarczonego tekstu.

Proces rozwiązania zadania:
1. Dokładnie przeanalizuj dostarczony tekst transkrypcji.
2. Wykorzystaj swoją wewnętrzną wiedzę na temat polskich uczelni i ich struktur (instytuty, wydziały, lokalizacje).
3. Szukaj informacji w tekście, które łączą profesora Maja z konkretnym instytutem i jego lokalizacją.
4. Bądź świadomy, że niektóre fragmenty tekstu mogą być chaotyczne, niejasne lub celowo mylące. Krytycznie oceniaj informacje.
5. Skup się WYŁĄCZNIE na lokalizacji (ulicy) KONKRETNEGO INSTYTUTU, a nie głównej siedziby uczelni, jeśli są różne.
6. Zastosuj "myślenie na głos" (chain-of-thought) - analizuj informacje krok po kroku, wyciągaj wnioski i uzasadniaj swoje rozumowanie.
7. Na podstawie analizy tekstu i swojej wiedzy, ustal ostateczną nazwę ulicy.

Twoja ostateczna odpowiedź powinna być TYLKO nazwą ulicy. Nie dołączaj żadnych dodatkowych wyjaśnień ani formatowania poza samą nazwą ulicy. Ta nazwa ulicy zostanie następnie użyta do sformułowania odpowiedzi w formacie JSON do Centrali.

Pamiętaj o kluczowych elementach:
- Cel: Ulica KONKRETNEGO INSTYTUTU, gdzie wykłada prof. Andrzej Maj.
- Źródło wiedzy o profesorze: TYLKO dostarczony tekst transkrypcji.
- Dodatkowa wiedza: Twoja wiedza o polskich uczelniach.
- Analiza: Krok po kroku, krytycznie, z uwzględnieniem potencjalnie mylących informacji.
- Wynik: Sama nazwa ulicy.
"""

# Wzorzec do wyszukiwania flagi
FLAG_REGEX = r"FLG[a-zA-Z0-9_-]+"

# Maksymalna liczba kroków w pętli (bezpiecznik)
MAX_STEPS = 5
```

### 3. Funkcje pomocnicze

#### 3.1. Walidacja konfiguracji

```python
def validate_configuration() -> bool:
    """
    Sprawdza, czy wszystkie wymagane zmienne konfiguracyjne są ustawione.

    Returns:
        bool: True jeśli konfiguracja jest poprawna, False w przeciwnym razie
    """
    required_vars = ["API_KEY", "LLM_API_KEY"]
    missing_vars = []

    for var in required_vars:
        if not globals().get(var) and not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        logger.critical(f"Brakujące wymagane zmienne konfiguracyjne: {', '.join(missing_vars)}")
        return False

    return True
```

#### 3.2. Pobieranie plików

```python
def download_file(url: str, attempt: int = 1) -> Optional[bytes]:
    """
    Pobiera plik z podanego URL z logiką ponownych prób.

    Args:
        url: URL do pobrania pliku
        attempt: Numer bieżącej próby (dla logiki ponownych prób)

    Returns:
        Optional[bytes]: Pobrane dane jako bajty lub None w przypadku niepowodzenia
    """
    logger.info(f"Pobieranie pliku z {url} (próba {attempt}/3)")

    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()

        logger.info(f"Plik pobrany pomyślnie ({len(response.content)} bajtów)")
        return response.content

    except requests.exceptions.RequestException as e:
        logger.error(f"Błąd podczas pobierania pliku: {e}")

        # Logika ponownych prób
        if attempt <= 2:
            logger.info(f"Ponowna próba za 2 sekundy...")
            time.sleep(2)
            return download_file(url, attempt + 1)
        else:
            logger.error("Osiągnięto maksymalną liczbę prób")
            return None
```

#### 3.3. Wyodrębnianie plików audio

```python
def extract_audio_files(zip_data: bytes) -> Dict[str, bytes]:
    """
    Wyodrębnia pliki audio z danych ZIP.

    Args:
        zip_data: Dane ZIP jako bajty

    Returns:
        Dict[str, bytes]: Słownik z nazwami plików jako kluczami i danymi plików jako wartościami
    """
    logger.info("Wyodrębnianie plików audio z archiwum ZIP")

    audio_files = {}

    try:
        with zipfile.ZipFile(io.BytesIO(zip_data)) as zip_ref:
            for file_name in zip_ref.namelist():
                if file_name.lower().endswith('.m4a'):
                    logger.info(f"Wyodrębnianie pliku: {file_name}")
                    audio_files[file_name] = zip_ref.read(file_name)

        logger.info(f"Wyodrębniono {len(audio_files)} plików audio")
        return audio_files

    except zipfile.BadZipFile as e:
        logger.error(f"Błąd podczas wyodrębniania plików: {e}")
        return {}
```

#### 3.4. Transkrypcja audio

```python
def transcribe_audio(audio_data: bytes, file_name: str, attempt: int = 1) -> Optional[str]:
    """
    Transkrybuje dane audio na tekst przy użyciu OpenAI Whisper.

    Args:
        audio_data: Dane audio jako bajty
        file_name: Nazwa pliku audio (dla celów logowania)
        attempt: Numer bieżącej próby (dla logiki ponownych prób)

    Returns:
        Optional[str]: Transkrypcja tekstu lub None w przypadku niepowodzenia
    """
    logger.info(f"Transkrypcja pliku audio: {file_name} (próba {attempt}/3)")

    try:
        client_params = {"api_key": LLM_API_KEY}
        if LLM_BASE_URL:
            client_params["base_url"] = LLM_BASE_URL

        client = OpenAI(**client_params)

        # Zapisz plik tymczasowo
        temp_file_path = f"temp_{file_name}"
        with open(temp_file_path, "wb") as f:
            f.write(audio_data)

        # Transkrypcja przy użyciu OpenAI Whisper
        with open(temp_file_path, "rb") as audio_file:
            transcription = client.audio.transcriptions.create(
                file=audio_file,
                model="whisper-1",
                language="pl"
            )

        # Usuń plik tymczasowy
        os.remove(temp_file_path)

        logger.info(f"Transkrypcja zakończona pomyślnie: {len(transcription.text)} znaków")
        return transcription.text

    except (APIError, APIConnectionError, RateLimitError) as e:
        logger.error(f"Błąd API OpenAI podczas transkrypcji: {e}")

        # Logika ponownych prób
        if attempt <= 2:
            logger.info(f"Ponowna próba za 2 sekundy...")
            time.sleep(2)
            return transcribe_audio(audio_data, file_name, attempt + 1)
        else:
            logger.error("Osiągnięto maksymalną liczbę prób")
            return None
    except Exception as e:
        logger.error(f"Nieoczekiwany błąd podczas transkrypcji: {e}")

        # Usuń plik tymczasowy w przypadku błędu
        if os.path.exists(f"temp_{file_name}"):
            os.remove(f"temp_{file_name}")

        return None
```

#### 3.5. Przygotowanie kontekstu

```python
def prepare_context(transcriptions: Dict[str, str]) -> str:
    """
    Przygotowuje kontekst dla LLM na podstawie transkrypcji.

    Args:
        transcriptions: Słownik z nazwami plików jako kluczami i transkrypcjami jako wartościami

    Returns:
        str: Połączony tekst transkrypcji
    """
    logger.info("Przygotowywanie kontekstu dla LLM")

    context = "Poniżej znajdują się transkrypcje przesłuchań świadków:\n\n"

    for file_name, transcription in transcriptions.items():
        # Wyodrębnij nazwę świadka z nazwy pliku (zakładając format "Imię.m4a")
        witness_name = file_name.split('.')[0]
        context += f"--- Przesłuchanie: {witness_name} ---\n{transcription}\n\n"

    logger.info(f"Kontekst przygotowany: {len(context)} znaków")
    return context
```

#### 3.6. Wywołanie LLM

```python
def call_llm(prompt: str, system_prompt: str = SYSTEM_PROMPT, attempt: int = 1) -> Optional[str]:
    """
    Wywołuje LLM z podanym promptem i obsługuje błędy z logiką ponownych prób.

    Args:
        prompt: Prompt użytkownika do wysłania do LLM
        system_prompt: Prompt systemowy do użycia (domyślnie SYSTEM_PROMPT)
        attempt: Numer bieżącej próby (dla logiki ponownych prób)

    Returns:
        Optional[str]: Odpowiedź LLM lub None w przypadku niepowodzenia
    """
    logger.info(f"Wywołanie LLM z promptem (próba {attempt}/3)")

    try:
        client_params = {"api_key": LLM_API_KEY}
        if LLM_BASE_URL:
            client_params["base_url"] = LLM_BASE_URL

        client = OpenAI(**client_params)

        completion = client.chat.completions.create(
            model=LLM_MODEL,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            max_tokens=2000
        )

        response = completion.choices[0].message.content.strip()
        logger.info("Wywołanie LLM zakończone pomyślnie")
        return response

    except (APIError, APIConnectionError, RateLimitError) as e:
        logger.error(f"Błąd API LLM: {e}")

        # Logika ponownych prób
        if attempt <= 2:
            logger.info(f"Ponowna próba za 2 sekundy...")
            time.sleep(2)
            return call_llm(prompt, system_prompt, attempt + 1)
        else:
            logger.error("Osiągnięto maksymalną liczbę prób")
            return None
    except Exception as e:
        logger.error(f"Nieoczekiwany błąd podczas wywołania LLM: {e}")
        return None
```

#### 3.7. Wysyłanie odpowiedzi

```python
def send_answer(answer: str, attempt: int = 1) -> Optional[Dict]:
    """
    Wysyła odpowiedź do API Centrali.

    Args:
        answer: Odpowiedź do wysłania (nazwa ulicy)
        attempt: Numer bieżącej próby (dla logiki ponownych prób)

    Returns:
        Optional[Dict]: Odpowiedź z serwera lub None w przypadku niepowodzenia
    """
    logger.info(f"Wysyłanie odpowiedzi do {REPORT_ENDPOINT} (próba {attempt}/3)")

    payload = {
        "task": TASK_NAME,
        "apikey": API_KEY,
        "answer": answer
    }

    headers = {
        "Content-Type": "application/json"
    }

    try:
        response = requests.post(
            REPORT_ENDPOINT,
            json=payload,
            headers=headers,
            timeout=10
        )
        response.raise_for_status()

        result = response.json()
        logger.info("Odpowiedź wysłana pomyślnie")
        return result

    except requests.exceptions.RequestException as e:
        logger.error(f"Błąd podczas wysyłania odpowiedzi: {e}")

        if hasattr(e, 'response') and hasattr(e.response, 'text'):
            logger.error(f"Odpowiedź serwera: {e.response.text}")

        # Logika ponownych prób
        if attempt <= 2:
            logger.info(f"Ponowna próba za 2 sekundy...")
            time.sleep(2)
            return send_answer(answer, attempt + 1)
        else:
            logger.error("Osiągnięto maksymalną liczbę prób")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Błąd dekodowania odpowiedzi JSON: {e}")
        return None
```

#### 3.8. Sprawdzanie flagi

```python
def check_for_flag(text: str) -> Optional[str]:
    """
    Sprawdza, czy tekst zawiera flagę.

    Args:
        text: Tekst do sprawdzenia

    Returns:
        Optional[str]: Znaleziona flaga lub None, jeśli nie znaleziono
    """
    if not text:
        return None

    # Wyszukaj flagę przy użyciu wyrażenia regularnego
    match = re.search(FLAG_REGEX, text)
    if match:
        flag = match.group(0)
        logger.info(f"Znaleziono flagę: {flag}")
        return flag

    # Sprawdź, czy tekst zawiera słowo "flag"
    if "flag" in text.lower():
        logger.info(f"Tekst zawiera słowo 'flag': {text}")
        return text

    logger.info("Nie znaleziono flagi w tekście")
    return None
```

#### 3.9. Zapisywanie i wczytywanie transkrypcji

```python
def save_transcriptions(transcriptions: Dict[str, str]) -> bool:
    """
    Zapisuje transkrypcje do pliku JSON w formacie zbioru obiektów z tytułem i treścią.

    Args:
        transcriptions: Słownik z nazwami plików jako kluczami i transkrypcjami jako wartościami

    Returns:
        bool: True jeśli zapis się powiódł, False w przeciwnym razie
    """
    try:
        # Przygotuj dane w formacie zbioru obiektów
        formatted_transcriptions = []

        # Sprawdź, czy plik już istnieje
        if os.path.exists(TRANSCRIPTIONS_FILE):
            logger.info(f"Plik z transkrypcjami już istnieje: {TRANSCRIPTIONS_FILE}")
            # Wczytaj istniejące transkrypcje
            with open(TRANSCRIPTIONS_FILE, 'r', encoding='utf-8') as f:
                formatted_transcriptions = json.load(f)

            # Utwórz słownik istniejących transkrypcji dla łatwiejszego wyszukiwania
            existing_titles = {item[JSON_TITLE_KEY]: i for i, item in enumerate(formatted_transcriptions)}

            # Dodaj lub zaktualizuj transkrypcje
            for file_name, transcription_text in transcriptions.items():
                if file_name in existing_titles:
                    # Zaktualizuj istniejącą transkrypcję
                    index = existing_titles[file_name]
                    formatted_transcriptions[index][JSON_CONTENT_KEY] = transcription_text
                    logger.info(f"Zaktualizowano transkrypcję dla pliku: {file_name}")
                else:
                    # Dodaj nową transkrypcję
                    formatted_transcriptions.append({
                        JSON_TITLE_KEY: file_name,
                        JSON_CONTENT_KEY: transcription_text
                    })
                    logger.info(f"Dodano nową transkrypcję dla pliku: {file_name}")
        else:
            # Utwórz nowy plik z transkrypcjami
            for file_name, transcription_text in transcriptions.items():
                formatted_transcriptions.append({
                    JSON_TITLE_KEY: file_name,
                    JSON_CONTENT_KEY: transcription_text
                })
                logger.info(f"Dodano transkrypcję dla pliku: {file_name}")

        # Zapisz transkrypcje do pliku
        with open(TRANSCRIPTIONS_FILE, 'w', encoding='utf-8') as f:
            json.dump(formatted_transcriptions, f, ensure_ascii=False, indent=2)

        logger.info(f"Zapisano {len(transcriptions)} transkrypcji do pliku: {TRANSCRIPTIONS_FILE}")
        return True

    except Exception as e:
        logger.error(f"Błąd podczas zapisywania transkrypcji: {e}")
        return False

def load_transcriptions() -> Dict[str, str]:
    """
    Wczytuje transkrypcje z pliku JSON.

    Returns:
        Dict[str, str]: Słownik z nazwami plików jako kluczami i transkrypcjami jako wartościami
    """
    try:
        if os.path.exists(TRANSCRIPTIONS_FILE):
            with open(TRANSCRIPTIONS_FILE, 'r', encoding='utf-8') as f:
                formatted_transcriptions = json.load(f)

            # Konwertuj format zbioru obiektów na słownik
            transcriptions = {}
            for item in formatted_transcriptions:
                if JSON_TITLE_KEY in item and JSON_CONTENT_KEY in item:
                    transcriptions[item[JSON_TITLE_KEY]] = item[JSON_CONTENT_KEY]

            logger.info(f"Wczytano {len(transcriptions)} transkrypcji z pliku: {TRANSCRIPTIONS_FILE}")
            return transcriptions
        else:
            logger.warning(f"Plik z transkrypcjami nie istnieje: {TRANSCRIPTIONS_FILE}")
            return {}

    except Exception as e:
        logger.error(f"Błąd podczas wczytywania transkrypcji: {e}")
        return {}
```

#### 3.10. Wyodrębnianie nazwy ulicy

```python
def extract_street_name(text: str) -> str:
    """
    Wyodrębnia nazwę ulicy z odpowiedzi LLM, szukając tagów <street></street>.

    Args:
        text: Odpowiedź LLM

    Returns:
        str: Wyodrębniona nazwa ulicy
    """
    # Szukaj tagów <street></street>
    import re
    street_pattern = re.compile(r'<street>(.*?)</street>', re.DOTALL)
    match = street_pattern.search(text)

    if match:
        # Jeśli znaleziono tagi <street></street>, wyodrębnij całą zawartość (włącznie z cyframi i innymi znakami)
        street_name = match.group(1).strip()
        logger.info(f"Znaleziono nazwę ulicy w tagach <street></street>: '{street_name}'")
        # Nie filtrujemy cyfr ani innych znaków - wysyłamy dokładnie to, co jest w tagach
        return street_name

    # Jeśli nie znaleziono tagów, użyj alternatywnej metody
    logger.warning("Nie znaleziono tagów <street></street>, używanie alternatywnej metody")

    # Usuń białe znaki z początku i końca
    clean_text = text.strip()

    # Jeśli odpowiedź zawiera wiele linii, weź tylko ostatnią (która powinna zawierać samą nazwę)
    if "\n" in clean_text:
        lines = clean_text.split("\n")
        # Usuń puste linie
        lines = [line.strip() for line in lines if line.strip()]
        if lines:
            clean_text = lines[-1].strip()  # Weź ostatnią niepustą linię

    # Usuń cudzysłowy, jeśli są
    if (clean_text.startswith('"') and clean_text.endswith('"')) or \
       (clean_text.startswith("'") and clean_text.endswith("'")):
        clean_text = clean_text[1:-1]

    # Usuń prefiksy w języku polskim
    polish_prefixes = ["ulica ", "ul. ", "Ulica ", "Ul. "]
    for prefix in polish_prefixes:
        if clean_text.startswith(prefix):
            clean_text = clean_text[len(prefix):]
            break

    # Usuń prefiksy w języku angielskim
    english_prefixes = ["street ", "st. ", "Street ", "St. ", "avenue ", "ave. ", "Avenue ", "Ave. "]
    for prefix in english_prefixes:
        if clean_text.startswith(prefix):
            clean_text = clean_text[len(prefix):]
            break

    # Usuń frazy typu "The answer is" lub "Final answer:"
    phrases_to_remove = [
        "The answer is ", "the answer is ",
        "Final answer: ", "final answer: ",
        "Answer: ", "answer: ",
        "Location: ", "location: "
    ]
    for phrase in phrases_to_remove:
        if clean_text.startswith(phrase):
            clean_text = clean_text[len(phrase):]
            break

    logger.info(f"Wyodrębniona nazwa ulicy (alternatywna metoda): '{clean_text}'")
    return clean_text
```

#### 3.11. Przygotowanie danych audio

```python
def prepare_audio_data() -> Optional[str]:
    """
    Przygotowuje dane audio: wczytuje pliki audio, transkrybuje je i przygotowuje kontekst.

    Returns:
        Optional[str]: Przygotowany kontekst lub None w przypadku niepowodzenia
    """
    try:
        # Sprawdź, czy istnieją już transkrypcje
        existing_transcriptions = load_transcriptions()
        if existing_transcriptions:
            logger.info(f"Znaleziono {len(existing_transcriptions)} istniejących transkrypcji")

        # Krok 1: Pobierz pliki audio z lokalnego katalogu
        audio_files = get_audio_files()
        if not audio_files:
            logger.error("Nie znaleziono plików audio. Wyjście.")
            return None

        # Krok 2: Transkrybuj tylko te pliki, które nie mają jeszcze transkrypcji
        new_transcriptions = {}
        for file_name, audio_data in audio_files.items():
            if file_name in existing_transcriptions:
                logger.info(f"Plik {file_name} ma już transkrypcję, pomijam")
                continue

            logger.info(f"Transkrybuję plik: {file_name}")
            transcription = transcribe_audio(audio_data, file_name)
            if transcription:
                new_transcriptions[file_name] = transcription

        # Krok 3: Zapisz nowe transkrypcje
        if new_transcriptions:
            logger.info(f"Zapisuję {len(new_transcriptions)} nowych transkrypcji")
            save_transcriptions(new_transcriptions)

        # Krok 4: Połącz wszystkie transkrypcje
        all_transcriptions = {**existing_transcriptions, **new_transcriptions}

        if not all_transcriptions:
            logger.error("Nie udało się transkrybować żadnego pliku audio. Wyjście.")
            return None

        # Krok 5: Przygotuj kontekst dla LLM
        return prepare_context(all_transcriptions)

    except Exception as e:
        logger.error(f"Błąd podczas przygotowywania danych audio: {e}")
        return None
```

#### 3.12. Transkrypcja wszystkich plików

```python
def transcribe_all_files():
    """
    Transkrybuje wszystkie pliki audio z katalogu i zapisuje wyniki do pliku JSON.

    Returns:
        bool: True jeśli transkrypcja się powiodła, False w przeciwnym razie
    """
    logger.info("--- Rozpoczęcie transkrypcji wszystkich plików audio ---")

    # Sprawdź konfigurację
    if not validate_configuration():
        logger.critical("Nieprawidłowa konfiguracja. Wyjście.")
        return False

    # Sprawdź, czy katalog z plikami audio istnieje
    if not os.path.exists(AUDIO_DIR):
        logger.critical(f"Katalog z plikami audio nie istnieje: {AUDIO_DIR}")
        return False

    try:
        # Pobierz pliki audio
        audio_files = get_audio_files()
        if not audio_files:
            logger.error("Nie znaleziono plików audio. Wyjście.")
            return False

        logger.info(f"Znaleziono {len(audio_files)} plików audio do transkrypcji")

        # Wczytaj istniejące transkrypcje
        existing_transcriptions = load_transcriptions()

        # Transkrybuj tylko te pliki, które nie mają jeszcze transkrypcji
        new_transcriptions = {}
        for file_name, audio_data in audio_files.items():
            if file_name in existing_transcriptions:
                logger.info(f"Plik {file_name} ma już transkrypcję, pomijam")
                continue

            logger.info(f"Transkrybuję plik: {file_name}")
            transcription = transcribe_audio(audio_data, file_name)
            if transcription:
                new_transcriptions[file_name] = transcription
                # Zapisz transkrypcję natychmiast po każdym pliku
                save_transcriptions({file_name: transcription})

        # Podsumowanie
        total_transcriptions = len(existing_transcriptions) + len(new_transcriptions)
        logger.info(f"Zakończono transkrypcję. Łącznie {total_transcriptions} plików zostało transkrybowanych.")
        logger.info(f"Nowe transkrypcje: {len(new_transcriptions)}, Istniejące transkrypcje: {len(existing_transcriptions)}")

        return True

    except Exception as e:
        logger.critical(f"Nieoczekiwany błąd podczas transkrypcji: {e}", exc_info=True)
        return False

    finally:
        logger.info("--- Transkrypcja zakończona ---")
```

#### 3.13. Przetwarzanie wszystkich transkrypcji

```python
def process_all_transcriptions():
    """
    Przetwarza wszystkie transkrypcje z pliku JSON w jednym zapytaniu do LLM.
    """
    logger.info("--- Rozpoczęcie przetwarzania wszystkich transkrypcji ---")

    # Sprawdź konfigurację
    if not validate_configuration():
        logger.critical("Nieprawidłowa konfiguracja. Wyjście.")
        return

    try:
        # Sprawdź, czy plik z transkrypcjami istnieje
        if not os.path.exists(TRANSCRIPTIONS_FILE):
            logger.critical(f"Plik z transkrypcjami nie istnieje: {TRANSCRIPTIONS_FILE}")

            # Sprawdź, czy katalog z plikami audio istnieje
            if not os.path.exists(AUDIO_DIR):
                logger.critical(f"Katalog z plikami audio nie istnieje: {AUDIO_DIR}")
                return

            # Transkrybuj pliki audio
            if not transcribe_all_files():
                logger.error("Nie udało się transkrybować plików audio. Wyjście.")
                return

        # Wczytaj transkrypcje bezpośrednio z pliku JSON
        try:
            with open(TRANSCRIPTIONS_FILE, 'r', encoding='utf-8') as f:
                transcriptions_data = json.load(f)

            logger.info(f"Wczytano {len(transcriptions_data)} transkrypcji z pliku: {TRANSCRIPTIONS_FILE}")
        except Exception as e:
            logger.error(f"Błąd podczas wczytywania transkrypcji z pliku: {e}")
            return

        # Przygotuj kontekst dla wszystkich transkrypcji
        context = "Poniżej znajdują się transkrypcje przesłuchań świadków:\n\n"

        # Dodaj wszystkie transkrypcje do kontekstu
        witness_count = 0
        for item in transcriptions_data:
            if JSON_TITLE_KEY in item and JSON_CONTENT_KEY in item:
                witness_name = item[JSON_TITLE_KEY].split('.')[0]
                context += f"--- Przesłuchanie: {witness_name} ---\n{item[JSON_CONTENT_KEY]}\n\n"
                logger.info(f"Dodano osobę do kontekstu: {witness_name}")
                witness_count += 1

        logger.info(f"Przygotowano kontekst: {len(context)} znaków, liczba świadków: {witness_count}")

        # Wywołaj LLM dla wszystkich transkrypcji (w języku polskim)
        prompt = f"Teraz zgodnie z instrukcjami i na podstawie dostarczonych zeznań znajdź nazwę ulicy przy której znajduje się instytut profesora Maja. Przedstaw szczegółowy proces rozumowania, a następnie podaj nazwę ulicy w tagach <street></street>.\n\n{context}"
        llm_response = call_llm(prompt)

        if not llm_response:
            logger.error("Nie udało się uzyskać odpowiedzi od LLM. Wyjście.")
            return

        # Logowanie pełnej odpowiedzi LLM
        logger.info(f"Pełna odpowiedź LLM: '{llm_response}'")

        # Sprawdź, czy odpowiedź zawiera flagę
        flag = check_for_flag(llm_response)
        if flag:
            logger.info(f"SUKCES! Znaleziono flagę: {flag}")
            print(f"\n{'='*50}\nFLAGA: {flag}\n{'='*50}\n")
            return

        # Wyodrębnij nazwę ulicy z odpowiedzi LLM
        street_name = extract_street_name(llm_response)

        # Wyślij odpowiedź do API Centrali
        response = send_answer(street_name)

        if not response:
            logger.error("Nie udało się wysłać odpowiedzi. Wyjście.")
            return

        # Sprawdź odpowiedź z API pod kątem flagi
        flag = check_for_flag(str(response))

        if flag:
            logger.info(f"SUKCES! Znaleziono flagę: {flag}")
            print(f"\n{'='*50}\nFLAGA: {flag}\n{'='*50}\n")
            return

        logger.info("Przetwarzanie zakończone bez znalezienia flagi.")
        print(f"\n{'='*50}\nOSTATECZNA ODPOWIEDŹ: {json.dumps(response, indent=2)}\n{'='*50}\n")

    except Exception as e:
        logger.critical(f"Nieoczekiwany błąd: {e}", exc_info=True)

    logger.info("--- Przetwarzanie wszystkich transkrypcji zakończone ---")
```

### 4. Funkcja główna

```python
def main():
    """
    Główna funkcja koordynująca przepływ pracy agenta.
    """
    logger.info(f"--- Rozpoczęcie agenta {TASK_NAME} ---")

    # Sprawdź, czy chcemy tylko transkrybować pliki
    transcribe_only = os.getenv("TRANSCRIBE_ONLY", "False").lower() in ("true", "1", "t")

    if transcribe_only:
        logger.info("Tryb: tylko transkrypcja plików audio")
        transcribe_all_files()
    else:
        logger.info("Tryb: pełne wykonanie zadania")
        process_all_transcriptions()

    logger.info(f"--- Agent {TASK_NAME} zakończony ---")
```

### 5. Punkt wejścia

```python
if __name__ == "__main__":
    main()
```

## Podsumowanie planu implementacji

Powyższy plan implementacji obejmuje wszystkie wymagane funkcjonalności dla Single File Agent (SFA) do zadania S02E01:

1. **Dostęp do danych** - wczytywanie plików audio z lokalnego katalogu
2. **Przetwarzanie audio** - transkrypcja plików audio przy użyciu AssemblyAI
3. **Buforowanie transkrypcji** - zapisywanie i wczytywanie transkrypcji z pliku JSON
4. **Analiza tekstu** - przygotowanie kontekstu i wywołanie LLM (DeepSeek-V3-0324 przez API Kluster.ai)
5. **Wyodrębnianie odpowiedzi** - wyodrębnianie nazwy ulicy z tagów `<street></street>`
6. **Wysyłanie odpowiedzi** - formatowanie i wysyłanie odpowiedzi do API z kodowaniem UTF-8
7. **Weryfikacja flagi** - sprawdzanie odpowiedzi pod kątem obecności flagi w różnych formatach

Aplikacja jest zgodna z najlepszymi praktykami programowania, takimi jak:

- **DRY** (Don't Repeat Yourself) - funkcje są modułowe i wielokrotnego użytku
- **KISS** (Keep It Simple, Stupid) - prosta struktura i czytelny kod
- **YAGNI** (You Aren't Gonna Need It) - tylko niezbędne funkcjonalności
- **Obsługa błędów** - kompleksowa obsługa wyjątków i logowanie

Aplikacja korzysta z API Kluster.ai (kompatybilne z OpenAI) zgodnie z wymaganiami, a wszystkie konfiguracje (w tym model i API_BASE) są pobierane ze zmiennych środowiskowych.