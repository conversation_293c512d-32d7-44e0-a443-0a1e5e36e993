import os
import re
import json
import logging
import time
import requests
from typing import Dict, List, Any, Optional, Union
from dotenv import load_dotenv
from openai import OpenAI, APIError, APIConnectionError, RateLimitError
import assemblyai as aai

# --- CONFIGURATION ---
load_dotenv()

# --- APPLICATION SETTINGS ---
# These settings should be customized for each specific agent implementation
TASK_NAME = "mp3"
API_BASE_URL = os.getenv("API_BASE_URL", "https://c3ntrala.ag3nts.org")
DATA_ENDPOINT = f"{API_BASE_URL}/data"
REPORT_ENDPOINT = f"{API_BASE_URL}/report"
API_KEY = os.getenv("AIDEVS_API_KEY")

# Request settings
REQUEST_TIMEOUT = 10  # seconds
RETRY_ATTEMPTS = 0
RETRY_DELAY = 2  # seconds

# --- LLM SETTINGS ---
LLM_MODEL = os.getenv("KLUSTER_LLM_MODEL", os.getenv("OPENAI_LLM_MODEL"))
LLM_API_KEY = os.getenv("KLUSTER_API_KEY", os.getenv("OPENAI_API_KEY"))
LLM_BASE_URL = os.getenv("KLUSTER_BASE_URL", os.getenv("OPENAI_BASE_URL"))
LLM_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", "4000"))
LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.3"))
LLM_TOP_P = float(os.getenv("LLM_TOP_P", "1.0"))

# AssemblyAI Settings
ASSEMBLY_API_KEY = os.getenv("ASSEMBLY_API_KEY")

# Paths
AUDIO_FILES_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "ZZZ_TRIALS")
SYSTEM_PROMPT_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "ROO_sys_prompt.txt")
TRANSCRIPTIONS_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "transcriptions.json")

# --- REGEX PATTERNS ---
FLAG_REGEX = r"FLG[a-zA-Z0-9_-]+"
FLAG_REGEX_CURLY = r"\{\{FLG:[a-zA-Z0-9_-]+\}\}"

# --- LOGGING CONFIGURATION ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# --- HELPER FUNCTIONS ---

def validate_configuration() -> bool:
    """
    Validates that all required configuration variables are set.
    """
    required_env_vars = {
        "ASSEMBLY_API_KEY": "API key for AssemblyAI transcription service"
    }
    missing_vars = []

    for var, description in required_env_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"{var} ({description})")

    if missing_vars:
        logger.critical(f"Missing required environment variables: {', '.join(missing_vars)}")
        return False
    
    # Check if API_KEY (AIDEVS_API_KEY) is set
    if not API_KEY:
        logger.critical("API_KEY (AIDEVS_API_KEY) is not set.")
        return False

    # Check if LLM_API_KEY (KLUSTER_API_KEY or OPENAI_API_KEY) is set
    if not LLM_API_KEY:
        logger.critical("LLM_API_KEY (KLUSTER_API_KEY or OPENAI_API_KEY) is not set.")
        return False

    if not os.path.exists(AUDIO_FILES_DIR):
        logger.critical(f"Audio files directory not found: {AUDIO_FILES_DIR}")
        return False

    if not os.path.exists(SYSTEM_PROMPT_PATH):
        logger.critical(f"System prompt file not found: {SYSTEM_PROMPT_PATH}")
        return False

    return True

def load_transcriptions(json_path: str) -> List[Dict[str, str]]:
    """
    Loads existing transcriptions from a JSON file.
    Returns an empty list if the file does not exist or is empty/invalid.
    """
    if not os.path.exists(json_path):
        logger.info(f"Transcriptions file not found: {json_path}. Starting with empty list.")
        return []
    
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if not content:
                logger.info(f"Transcriptions file is empty: {json_path}. Starting with empty list.")
                return []
            transcriptions = json.loads(content)
            if not isinstance(transcriptions, list):
                logger.warning(f"Transcriptions file content is not a list: {json_path}. Starting with empty list.")
                return []
            logger.info(f"Loaded {len(transcriptions)} existing transcriptions from {json_path}")
            return transcriptions
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON from {json_path}: {e}. Starting with empty list.")
        return []
    except Exception as e:
        logger.error(f"Error loading transcriptions from {json_path}: {e}. Starting with empty list.")
        return []

def save_transcriptions(transcriptions_list: List[Dict[str, str]], json_path: str):
    """
    Saves the list of transcriptions to a JSON file.
    """
    try:
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(transcriptions_list, f, indent=2, ensure_ascii=False)
        logger.info(f"Saved {len(transcriptions_list)} transcriptions to {json_path}")
    except Exception as e:
        logger.error(f"Error saving transcriptions to {json_path}: {e}")

def transcribe_audio_files(audio_dir: str, json_path: str) -> List[Dict[str, str]]:
    """
    Transcribes audio files from the specified directory using AssemblyAI,
    checking against existing transcriptions in a JSON file.
    """
    logger.info(f"Starting audio transcription process for directory: {audio_dir}")
    aai.settings.api_key = ASSEMBLY_API_KEY
    
    existing_transcriptions = load_transcriptions(json_path)
    existing_titles = {t["title"] for t in existing_transcriptions}
    
    audio_files = [f for f in os.listdir(audio_dir) if f.endswith(".m4a")]
    if not audio_files:
        logger.warning(f"No .m4a files found in {audio_dir}")
        return existing_transcriptions # Return existing if no new files

    new_transcriptions_added = False

    for filename in audio_files:
        if filename in existing_titles:
            logger.info(f"Transcription for {filename} already exists. Skipping.")
            continue
        audio_file_path = os.path.join(audio_dir, filename)
        config = aai.TranscriptionConfig(speech_model=aai.SpeechModel.best, language_code="pl")
        transcriber = aai.Transcriber(config=config)
        
        for attempt in range(RETRY_ATTEMPTS + 1):
            try:
                logger.info(f"Transcribing {filename} (attempt {attempt + 1}/{RETRY_ATTEMPTS + 1})")
                transcript = transcriber.transcribe(audio_file_path)
                
                if transcript.status == "error":
                    logger.error(f"Transcription failed for {filename}: {transcript.error}")
                    if attempt == RETRY_ATTEMPTS:
                        logger.error(f"Max retries reached for {filename}. Skipping.")
                    continue # Continue to next attempt or next file
                
                existing_transcriptions.append({"title": filename, "content": transcript.text})
                logger.info(f"Successfully transcribed and added {filename}")
                new_transcriptions_added = True
                break # Break retry loop on success
            except Exception as e:
                logger.error(f"Error during transcription of {filename}: {e}")
                if attempt < RETRY_ATTEMPTS:
                    logger.info(f"Retrying transcription in {RETRY_DELAY} seconds...")
                    time.sleep(RETRY_DELAY)
                else:
                    logger.error(f"Max retries reached for {filename}. Skipping.")
    
    if new_transcriptions_added:
        save_transcriptions(existing_transcriptions, json_path)
    else:
        logger.info("No new transcriptions were added.")

    return existing_transcriptions

def load_system_prompt(path: str) -> str:
    """
    Loads the system prompt from a specified file path.
    """
    try:
        with open(path, 'r', encoding='utf-8') as file:
            system_prompt = file.read()
        logger.info(f"Loaded system prompt from: {path}")
        return system_prompt
    except Exception as e:
        logger.critical(f"Failed to load system prompt from {path}: {e}")
        raise

def query_llm(user_prompt: str, system_prompt_content: str) -> Optional[str]:
    """
    Calls the LLM with the given user and system prompts, handling retries and iteration limit.
    """
    logger.info("Calling LLM...")
    
    client = OpenAI(api_key=LLM_API_KEY, base_url=LLM_BASE_URL)
    
    messages = [
        {"role": "system", "content": system_prompt_content},
        {"role": "user", "content": user_prompt}
    ]
    
    for iteration in range(5): # Max 5 iterations as per plan
        logger.info(f"LLM query iteration: {iteration + 1}/5")
        try:
            completion = client.chat.completions.create(
                model=LLM_MODEL,
                messages=messages,
                temperature=LLM_TEMPERATURE,
                max_tokens=LLM_MAX_TOKENS,
                top_p=LLM_TOP_P
            )
            
            llm_response_content = completion.choices[0].message.content.strip()
            logger.info("LLM call successful.")
            logger.debug(f"LLM Raw Response: {llm_response_content}")

            # Check for flag in LLM's response (if LLM is expected to return it)
            # This is a safety check, the main flag check is after sending to Centrala
            if "flag" in llm_response_content.lower() or "flg" in llm_response_content.lower():
                logger.warning(f"Potential flag found in LLM response: {llm_response_content}")
                # If the LLM directly returns the flag, we might want to stop here
                # For this task, the flag is expected from Centrala, so we continue.

            # If the LLM's response is the final answer (street name), return it.
            # Otherwise, if it's part of a "thinking aloud" process, append it to messages
            # and continue if more iterations are needed.
            # For this specific task, we expect a direct answer, so we return.
            return llm_response_content

        except (APIError, APIConnectionError, RateLimitError) as e:
            logger.error(f"LLM API error (iteration {iteration + 1}): {e}")
            if iteration < RETRY_ATTEMPTS: # Use RETRY_ATTEMPTS for API errors within iteration
                logger.info(f"Retrying LLM call in {RETRY_DELAY} seconds...")
                time.sleep(RETRY_DELAY)
            else:
                logger.error("Maximum LLM retry attempts reached for this iteration.")
                return None
        except Exception as e:
            logger.error(f"Unexpected error during LLM call (iteration {iteration + 1}): {e}")
            return None
            
    logger.warning("LLM query reached maximum iterations (5) without a definitive answer.")
    return None

def extract_street_name(llm_response: str) -> Optional[str]:
    """
    Extracts the street name from the LLM's response.
    Assumes the LLM is instructed to return only the street name.
    """
    if llm_response:
        # Simple strip to remove any leading/trailing whitespace or quotes
        street_name = llm_response.strip()
        if (street_name.startswith('"') and street_name.endswith('"')) or \
           (street_name.startswith("'") and street_name.endswith("'")):
            street_name = street_name[1:-1]
        logger.info(f"Extracted street name: '{street_name}'")
        return street_name
    logger.warning("LLM response was empty, cannot extract street name.")
    return None

def send_answer_to_centrala(street_name: str, api_key: str, task_name: str) -> Optional[Dict]:
    """
    Sends the final answer (street name) to the Centrala API.
    """
    logger.info(f"Sending answer to Centrala: '{street_name}' for task '{task_name}'")
    
    payload = {
        "task": task_name,
        "apikey": api_key,
        "answer": street_name
    }

    headers = {
        "Content-Type": "application/json; charset=utf-8"
    }

    logger.info(f"Sending payload: {json.dumps(payload, ensure_ascii=False)}")

    for attempt in range(RETRY_ATTEMPTS + 1):
        try:
            logger.info(f"Sending report (attempt {attempt + 1}/{RETRY_ATTEMPTS + 1})")
            response = requests.post(
                REPORT_ENDPOINT,
                json=payload,
                headers=headers,
                timeout=REQUEST_TIMEOUT
            )
            
            logger.info(f"Response code: {response.status_code}")
            logger.info(f"Response content: {response.text}")

            print(f"\n{'='*50}")
            print(f"RESPONSE FROM CENTRALA:")
            print(f"Code: {response.status_code}")
            print(f"Content: {response.text}")
            print(f"{'='*50}\n")

            response.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx)
            result = response.json()
            logger.info("Report sent successfully.")
            return result

        except requests.exceptions.RequestException as e:
            logger.error(f"Error sending report (attempt {attempt + 1}): {e}")
            if hasattr(e, 'response') and hasattr(e.response, 'text'):
                logger.error(f"Server response: {e.response.text}")
            
            if attempt < RETRY_ATTEMPTS:
                logger.info(f"Retrying report in {RETRY_DELAY} seconds...")
                time.sleep(RETRY_DELAY)
            else:
                logger.error("Maximum retry attempts reached for sending report.")
                return None
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON response from Centrala: {e}")
            return None
    return None

def verify_and_log_flag(response_from_centrala: Optional[Dict]) -> Optional[str]:
    """
    Checks if the response from Centrala contains a flag and logs it.
    """
    if not response_from_centrala:
        logger.warning("No response from Centrala to check for flag.")
        return None

    response_str = str(response_from_centrala)

    match_curly = re.search(FLAG_REGEX_CURLY, response_str)
    if match_curly:
        flag = match_curly.group(0)
        logger.info(f"Flag found in {{FLG:XXXX}} format: {flag}")
        print(f"\n{'='*50}\nFLAG: {flag}\n{'='*50}\n")
        return flag

    match = re.search(FLAG_REGEX, response_str)
    if match:
        flag = match.group(0)
        logger.info(f"Flag found in FLG[a-zA-Z0-9_-]+ format: {flag}")
        print(f"\n{'='*50}\nFLAG: {flag}\n{'='*50}\n")
        return flag

    if "flag" in response_str.lower() or "flg" in response_str.lower():
        logger.info(f"Text contains the word 'flag' or 'FLG': {response_str}")
        print(f"\n{'='*50}\nPOTENTIAL FLAG (no specific regex match): {response_str}\n{'='*50}\n")
        return response_str # Return the whole response if it contains "flag" but no regex match

    logger.info("No flag found in Centrala response.")
    return None

# --- MAIN FUNCTION ---

def main():
    """
    Main function that orchestrates the agent's workflow for S02E01.
    """
    logger.info(f"--- Starting {TASK_NAME} SFA agent ---")

    if not validate_configuration():
        logger.critical("Configuration validation failed. Exiting.")
        return

    try:
        # Step 1: Transcribe audio files and load/save to JSON
        all_transcriptions = transcribe_audio_files(AUDIO_FILES_DIR, TRANSCRIPTIONS_FILE)
        if not all_transcriptions:
            logger.critical("No transcriptions available (either failed or none found/generated). Exiting.")
            return

        # Step 2: Load system prompt
        system_prompt_content = load_system_prompt(SYSTEM_PROMPT_PATH)
        
        # Step 3: Prepare user prompt for LLM
        # Concatenate transcription content for the LLM prompt
        transcriptions_text_for_llm = "\n\n".join([f"--- Transkrypcja z pliku: {t['title']} ---\n{t['content']}" for t in all_transcriptions])
        user_prompt_for_llm = f"Oto transkrypcje przesłuchań świadków:\n\n{transcriptions_text_for_llm}\n\nNa podstawie tych transkrypcji oraz swojej wiedzy, podaj NAZWĘ ULICY, na której znajduje się KONKRETNY INSTYTUT uczelni, gdzie wykłada profesor Andrzej Maj. Pamiętaj, że szukamy ulicy instytutu, a nie głównej siedziby uczelni. Podaj tylko samą nazwę ulicy, bez dodatkowych wyjaśnień."
        
        # Step 4: Query LLM
        llm_raw_response = query_llm(user_prompt_for_llm, system_prompt_content)
        if not llm_raw_response:
            logger.critical("Failed to get a valid response from LLM. Exiting.")
            return

        # Step 5: Extract street name
        street_name = extract_street_name(llm_raw_response)
        if not street_name:
            logger.critical("Failed to extract street name from LLM response. Exiting.")
            return

        # Step 6: Send answer to Centrala
        centrala_response = send_answer_to_centrala(street_name, API_KEY, TASK_NAME)
        if not centrala_response:
            logger.critical("Failed to send answer to Centrala or received no response. Exiting.")
            return

        # Step 7: Verify and log flag
        flag = verify_and_log_flag(centrala_response)
        if flag:
            logger.info(f"Task completed successfully. Flag: {flag}")
        else:
            logger.info("Task completed, but no flag was explicitly found in Centrala's response.")
            logger.info(f"Centrala's response: {json.dumps(centrala_response, indent=2, ensure_ascii=False)}")

    except Exception as e:
        logger.critical(f"An unexpected error occurred during the main workflow: {e}", exc_info=True)

    logger.info(f"--- {TASK_NAME} SFA agent finished ---")

# --- SCRIPT ENTRY POINT ---
if __name__ == "__main__":
    main()