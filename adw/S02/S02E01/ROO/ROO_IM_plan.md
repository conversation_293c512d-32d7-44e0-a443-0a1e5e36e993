# Plan Implementacji Aplikacji Single File Agent (SFA) - S02E01

## Wstęp

Niniejszy dokument przedstawia szczegółowy plan implementacji aplikacji Single File Agent (SFA) dla zadania S02E01. Celem aplikacji jest automatyczne pozyskanie nazwy ulicy instytutu, w którym wykłada profesor Andrzej Maj, na podstawie analizy transkrypcji nagrań świadków, a następnie wysłanie tej informacji do Centrali. Plan uwzględnia wymagania projektowe zawarte w [`ROO_analiza.md`](adw/S02/S02E01/ROO/ROO_analiza.md), przykładową implementację z [`sfa_bbs.py`](adw/sfa_bbs.py) oraz dodatkowe wytyczne dotyczące kluczy API, transkrypcji audio, lokalizacji plików i weryfikacji flagi. Implementacja będzie zgodna z najlepszymi praktykami projektowania oprogramowania (DRY, KISS, YAGNI, SOLID).

## Struktura Aplikacji SFA

Aplikacja zostanie zaimplementowana jako pojedynczy plik Pythona, wzorując się na strukturze `sfa_bbs.py`. Główne komponenty i metody będą obejmować:

*   **`main()`:** Główna funkcja orkiestrująca cały proces.
*   **`load_environment_variables()`:** Metoda do bezpiecznego ładowania kluczy API z zmiennych środowiskowych.
*   **`setup_logging()`:** Konfiguracja systemu logowania zgodnie z przykładem z `sfa_bbs.py`.
*   **`transcribe_audio_files(audio_dir)`:** Metoda odpowiedzialna za transkrypcję plików audio.
    *   Będzie iterować po plikach `.m4a` w podanym katalogu.
    *   Wykorzysta bibliotekę `assemblyai` do transkrypcji.
    *   Zwróci połączony tekst transkrypcji.
*   **`prepare_llm_prompt(transcriptions_text, system_prompt_path)`:** Metoda do konstruowania promptu dla LLM.
    *   Wczyta system prompt z [`ROO_sys_prompt.txt`](adw/S02/S02E01/ROO/ROO_sys_prompt.txt).
    *   Połączy go z tekstem transkrypcji.
*   **`query_llm(prompt_messages)`:** Metoda do wysyłania zapytań do modelu LLM.
    *   Będzie używać standardu OpenAI API.
    *   Zaimplementuje mechanizm "myślenia na głos" (chain of thought) poprzez odpowiednie instrukcje w prompcie.
    *   Będzie zawierać bezpiecznik automatycznie kończący pętlę po 5 krokach w przypadku iteracji.
*   **`extract_street_name(llm_response)`:** Metoda do ekstrakcji nazwy ulicy z odpowiedzi LLM.
*   **`send_answer_to_centrala(street_name, api_key)`:** Metoda do wysyłania odpowiedzi do Centrali.
    *   Wyśle żądanie POST do `https://c3ntrala.ag3nts.org/report`.
    *   Zaimplementuje obsługę błędów i logowanie odpowiedzi.
*   **`verify_and_log_flag(response_from_centrala)`:** Metoda do weryfikacji flagi w odpowiedzi z Centrali.
    *   Będzie szukać słowa "flag" lub "FLG".
    *   Wypisze flagę do konsoli, jeśli zostanie znaleziona.
*   **`handle_error(error_message)`:** Centralna obsługa błędów i logowanie.

## Szczegółowy Plan Implementacji

### 1. Konfiguracja i Zmienne Środowiskowe

*   **Klucze API:** Klucze API dla AssemblyAI i OpenAI (jeśli będzie używane) oraz klucz API do Centrali będą ładowane z zmiennych środowiskowych (np. `AIDEVS_API_KEY`, `KLUSTER_API_KEY`, `OPENAI_API_KEY`).
*   Użycie biblioteki `os` do odczytu zmiennych środowiskowych.

### 2. Obsługa Logowania i Błędów

*   Zaimplementowanie modułu `logging` w Pythonie.
*   Konfiguracja loggera do zapisywania informacji, ostrzeżeń i błędów.
*   Wykorzystanie bloków `try-except` do obsługi potencjalnych błędów (np. błędy sieciowe, błędy API, błędy transkrypcji, błędy parsowania odpowiedzi LLM).
*   Zgodność z przykładami obsługi błędów z `sfa_bbs.py`.

### 3. Transkrypcja Nagrań Audio i Zarządzanie Transkrypcjami

*   **Lokalizacja plików:** Pliki `.m4a` znajdują się w katalogu `/Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S02/S02E01/ZZZ_TRIALS`. Aplikacja będzie iterować po wszystkich plikach `.m4a` w tym katalogu.
*   **Plik transkrypcji JSON:** Transkrypcje będą zapisywane w pliku `transcriptions.json` w formacie listy obiektów `{"title":"nazwa_pliku", "content":"transkrypcja"}`.
*   **Weryfikacja istniejących transkrypcji:** Przed każdą transkrypcją, aplikacja sprawdzi, czy dla danego pliku audio istnieje już wpis w `transcriptions.json`. Jeśli tak, transkrypcja zostanie pominięta. Jeśli nie, zostanie przeprowadzona i dopisana do pliku.
*   **Biblioteka:** Użycie `assemblyai` zgodnie z dostarczonym przykładem.
    ```python
    import assemblyai as aai
    import os
    import json
    import logging

    # Ustawienia AssemblyAI API Key
    aai.settings.api_key = os.getenv("ASSEMBLY_API_KEY") # Zgodnie z nową nazwą zmiennej środowiskowej

    def transcribe_audio_files_and_save(audio_dir: str, output_json_path: str) -> str:
        transcriptions_data = []
        
        # Wczytaj istniejące transkrypcje, jeśli plik istnieje
        if os.path.exists(output_json_path):
            try:
                with open(output_json_path, 'r', encoding='utf-8') as f:
                    transcriptions_data = json.load(f)
                logging.info(f"Loaded existing transcriptions from {output_json_path}")
            except json.JSONDecodeError:
                logging.warning(f"Could not decode JSON from {output_json_path}. Starting with empty transcriptions.")
                transcriptions_data = []
        
        existing_titles = {item["title"] for item in transcriptions_data}

        audio_files = [f for f in os.listdir(audio_dir) if f.endswith(".m4a")]
        if not audio_files:
            logging.warning(f"No .m4a files found in {audio_dir}")
            return ""

        for filename in audio_files:
            if filename in existing_titles:
                logging.info(f"Transcription for {filename} already exists. Skipping.")
                continue

            audio_file_path = os.path.join(audio_dir, filename)
            config = aai.TranscriptionConfig(speech_model=aai.SpeechModel.best)
            transcriber = aai.Transcriber(config=config)
            
            try:
                logging.info(f"Transcribing {filename}...")
                transcript = transcriber.transcribe(audio_file_path)
                
                if transcript.status == "error":
                    logging.error(f"Transcription failed for {filename}: {transcript.error}")
                    continue
                
                transcriptions_data.append({"title": filename, "content": transcript.text})
                logging.info(f"Successfully transcribed and added {filename}")
            except Exception as e:
                logging.error(f"Error during transcription of {filename}: {e}")
        
        # Zapisz zaktualizowane transkrypcje do pliku JSON
        with open(output_json_path, 'w', encoding='utf-8') as f:
            json.dump(transcriptions_data, f, indent=2, ensure_ascii=False)
        logging.info(f"All transcriptions saved to {output_json_path}")

        # Zwróć połączony tekst wszystkich transkrypcji dla LLM
        return "\n".join([f"--- Transkrypcja z pliku: {item['title']} ---\n{item['content']}\n" for item in transcriptions_data])
    ```
*   **Agregacja:** Wszystkie transkrypcje (zarówno nowe, jak i wczytane z pliku) zostaną połączone w jeden duży string, który posłuży jako kontekst dla LLM.

### 4. Interakcja z LLM

*   **Standard API:** Zapytania do LLM będą realizowane w standardzie OpenAI API.
*   **System Prompt:** Wczytanie treści z [`adw/S02/S02E01/ROO/ROO_sys_prompt.txt`](adw/S02/S02E01/ROO/ROO_sys_prompt.txt) jako `system` message.
*   **User Prompt:** Połączone transkrypcje zostaną przekazane jako `user` message.
*   **Iteracja i Bezpiecznik:**
    *   Zaimplementowanie pętli do iteracyjnego zadawania pytań LLM, jeśli zajdzie taka potrzeba (np. w przypadku, gdy pierwsza odpowiedź nie jest wystarczająco precyzyjna).
    *   Bezpiecznik: Pętla zostanie automatycznie zakończona po maksymalnie 5 iteracjach, aby zapobiec zapętleniu i nadmiernemu zużyciu tokenów.
    *   W każdej iteracji, historia konwersacji (poprzednie wiadomości i odpowiedzi) będzie przekazywana do LLM, aby utrzymać kontekst.
*   **Ekstrakcja odpowiedzi:** Po otrzymaniu odpowiedzi od LLM, zostanie podjęta próba ekstrakcji nazwy ulicy. Można użyć prostych metod parsowania stringów lub wyrażeń regularnych, zakładając, że LLM będzie trzymał się formatu odpowiedzi (sama nazwa ulicy).

### 5. Wysłanie Odpowiedzi do Centrali

*   **Endpoint:** `https://c3ntrala.ag3nts.org/report`
*   **Metoda:** POST
*   **Payload JSON:**
    ```json
    {
      "task": "mp3",
      "apikey": "YOUR_API_KEY",
      "answer": "Nazwa ulicy"
    }
    ```
*   Użycie biblioteki `requests` do wysłania żądania.
*   Obsługa odpowiedzi z Centrali, w tym kodów statusu HTTP i treści odpowiedzi.

### 6. Weryfikacja Flagi i Zakończenie

*   Po otrzymaniu odpowiedzi z Centrali, jej treść zostanie przeszukana pod kątem wystąpienia słów "flag" lub "FLG".
*   Jeśli flaga zostanie znaleziona, zostanie wypisana do konsoli, a aplikacja zakończy działanie.
*   W przeciwnym razie, aplikacja zaloguje informację o niepowodzeniu i zakończy działanie.

## Zależności (Libraries/Packages)

Następujące biblioteki będą wymagane i zostaną dodane do pliku `requirements.txt` lub zainstalowane bezpośrednio:

*   `assemblyai`
*   `openai` (do interakcji z LLM)
*   `requests` (do pobierania danych i wysyłania odpowiedzi do Centrali)
*   `python-dotenv` (do ładowania zmiennych środowiskowych z pliku `.env` dla ułatwienia rozwoju)