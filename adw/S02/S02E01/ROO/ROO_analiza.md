# Analiza Zadania S02E01

## Wyjaśnienie Zadania

Głównym celem zadania jest ustalenie nazwy ulicy, na której znajduje się konkretny instytut uczelni, w którym wykłada profesor Andrzej Maj. Informacje potrzebne do rozwiązania tej zagadki są ukryte w nagraniach z przesłuchań świadków. Materiał audio jest obszerny i może zawierać sprzeczne lub uzupełniające się zeznania. Kluczowe jest nagranie Rafała, które jest uznane za najbardziej wiarygodne ze względu na jego bliskie kontakty z profesorem.

Zadanie wymaga automatycznego przetworzenia danych audio, przekształcenia ich na tekst, a następnie wykorzystania modelu językowego (LLM) do analizy tych transkrypcji w celu wyciągnięcia wniosków i ustalenia poszukiwanej nazwy ulicy. Ostateczna odpowiedź (nazwa ulicy) musi zostać wysłana do Centrali za pomocą określonego API.

## Propozycja Optymalnego Planu Działania

Oto sugerowany plan działania, oparty na krokach opisanych w zadaniu:

1. **Pobranie i rozpakowanie archiwum:** Należy pobrać plik `przesluchania.zip` ze wskazanego adresu URL i rozpakować go, aby uzyskać dostęp do pojedynczych plików audio w formacie `.m4a`.
2. **Transkrypcja nagrań:** Każdy plik `.m4a` musi zostać przetworzony przez model zamieniający mowę na tekst (ASR). Wynikiem tego kroku będą transkrypcje tekstowe każdego z przesłuchań.
3. **Agregacja transkrypcji:** Wszystkie wygenerowane transkrypcje tekstowe powinny zostać połączone w jeden spójny blok tekstu. Ten połączony tekst posłuży jako główny kontekst dla modelu LLM.
4. **Przygotowanie i wykonanie promptu dla LLM:** Należy sformułować precyzyjny prompt dla modelu LLM. Prompt ten powinien zawierać:
    * Jasne polecenie znalezienia ulicy konkretnego instytutu, a nie głównej siedziby uczelni.
    * Cały zagregowany tekst transkrypcji jako kontekst.
    * Instrukcję dla modelu, aby analizował transkrypcje krok po kroku ("myślenie na głos").
    * Polecenie, aby model wykorzystał swoją wewnętrzną wiedzę na temat polskich uczelni w celu weryfikacji i ustalenia prawidłowej ulicy.
    * Wskazówkę, że niektóre zeznania mogą być mylące lub sprzeczne, co model powinien wziąć pod uwagę.
    * Należy rozważyć, czy prompt powinien być w języku polskim, czy angielskim, aby uzyskać najlepsze rezultaty (zadanie sugeruje eksperymentowanie z tym).
5. **Ekstrakcja odpowiedzi:** Po przetworzeniu promptu przez LLM, należy wyodrębnić z odpowiedzi modelu ustaloną nazwę ulicy.
6. **Wysłanie odpowiedzi do Centrali:** Ostatnim krokiem jest wysłanie nazwy ulicy do wskazanego endpointu API (`https://c3ntrala.ag3nts.org/report`) w formacie JSON, zawierającym `task: "mp3"`, klucz API oraz ustaloną nazwę ulicy w polu `answer`.

## Propozycja Stacku Technologicznego

Do realizacji tego zadania można wykorzystać następujące narzędzia i technologie:

* **Język Programowania:** Python jest naturalnym wyborem ze względu na bogactwo bibliotek do przetwarzania danych, obsługi API i integracji z modelami ML/LLM.
* **Pobieranie i rozpakowywanie:**
  * Biblioteka `requests` w Pythonie do pobrania pliku zip.
  * Standardowa biblioteka `zipfile` w Pythonie do rozpakowania archiwum.
* **Transkrypcja Audio (ASR):**
  * **OpenAI Whisper:** Można użyć API OpenAI lub lokalnej implementacji, np. biblioteki `whisper` (wymaga zainstalowania odpowiednich zależności, np. `ffmpeg`).
  * **Lokalny Model ASR:** Jeśli zadanie ma być ćwiczeniem z modelami lokalnymi, można poszukać innych modeli ASR dostępnych do uruchomienia lokalnie.
* **Przetwarzanie Tekstu:** Standardowe funkcje i metody manipulacji stringami w Pythonie.
* **Model Językowy (LLM):**
  * **Modele OpenAI (np. GPT-4, GPT-3.5):** Dostępne poprzez API OpenAI.
  * **Lokalne Modele LLM:** Zgodnie ze wskazówką, można użyć modeli takich jak Bielik, uruchomionych lokalnie np. za pomocą `ollama` lub biblioteki `transformers` z odpowiednim modelem.
* **Wysłanie Odpowiedzi:**
  * Biblioteka `requests` w Pythonie do wysłania żądania POST z odpowiedzią w formacie JSON do API Centrali.
