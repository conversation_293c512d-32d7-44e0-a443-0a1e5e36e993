## Analiza Zadania S02E01

**Cel Zadania:**

Ustalenie nazwy ulicy, na której znajduje się konkretny instytut uczelni, gdzie wykłada profesor Andrzej Maj. Informacje do rozwiązania zadania znajdują się w nagraniach z przesłuchań świadków.

**Szczegółowy opis zadania:**

1. **Pobranie danych:** Pobierz archiwum `przesluchania.zip` z adresu: <https://c3ntrala.ag3nts.org/dane/przesluchania.zip>.
2. **Rozpakowanie archiwum:** Rozpakuj pobrane archiwum. Zawiera ono pliki audio w formacie `.m4a`.
3. **Transkrypcja nagrań:** Wygeneruj transkrypcje tekstowe dla każdego pliku `.m4a` przy użyciu modelu ASR (np. OpenAI Whisper lub lokalnego modelu jak Bielik).
4. **Przygotowanie kontekstu dla LLM:** Połącz wszystkie wygenerowane transkrypcje w jeden spójny tekst. Ten tekst będzie stanowił kontekst dla promptu LLM.
5. **Sformułowanie promptu:** Stwórz prompt dla LLM zawierający połączone transkrypcje i instrukcje. Model ma przeanalizować tekst krok po kroku, wykorzystać swoją wiedzę o polskich uczelniach i ustalić ulicę *konkretnego instytutu*, a nie głównej siedziby uczelni. W prompcie uwzględnij informację o potencjalnie chaotycznych lub mylących nagraniach.
6. **Wysłanie odpowiedzi:** Wyślij ustaloną nazwę ulicy do Centrali (<https://c3ntrala.ag3nts.org/report>) w formacie JSON:

    ```json
    {
      "task": "mp3",
      "apikey": "YOUR_API_KEY",
      "answer": "Nazwa ulicy"
    }
    ```

    Pamiętaj o zastąpieniu `YOUR_API_KEY` swoim kluczem API i kodowaniu UTF-8.

**Proponowany plan działania:**

1. Napisz skrypt/program do pobrania i rozpakowania archiwum `przesluchania.zip`.
2. Zaimplementuj moduł do transkrypcji plików `.m4a` przy użyciu wybranego modelu ASR (API lub lokalnie).
3. Napisz kod do połączenia wszystkich transkrypcji w jeden tekst.
4. Przygotuj funkcję do dynamicznego tworzenia promptu dla LLM, zawierającego transkrypcje i instrukcje.
5. Zintegruj się z wybranym modelem LLM (API lub lokalnie), wyślij prompt i przetwórz odpowiedź, aby wyodrębnić nazwę ulicy.
6. Zbuduj obiekt JSON z odpowiedzią w wymaganym formacie.
7. Wyślij żądanie HTTP POST z odpowiedzią JSON do Centrali.

**Propozycja stacku technologicznego:**

* **Język programowania:** Python (biblioteki: `requests`, `zipfile`, `openai` lub `transformers`).
* **Pobieranie/Rozpakowywanie:** `requests`, `zipfile`.
* **Transkrypcja (ASR):** `openai` (dla Whisper API) lub `transformers` (dla modeli lokalnych).
* **Model językowy (LLM):** `openai` (dla modeli GPT API) lub `transformers`/inne narzędzia (dla modeli lokalnych jak Bielik).
* **Wysłanie odpowiedzi:** `requests`.

**Uwagi:**

* Kluczowe jest precyzyjne sformułowanie promptu dla LLM, aby model skupił się na *instytucie* i wykorzystał swoją wiedzę o uczelniach.
* Należy uwzględnić, że nagrania mogą zawierać mylące informacje.
* Model LLM nie ma wiedzy o profesorze Maju poza tym, co znajduje się w transkrypcjach.
