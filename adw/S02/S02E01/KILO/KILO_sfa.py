import os
import json
import glob
import requests
import logging
from dotenv import load_dotenv
import assemblyai as aai
from openai import OpenAI
from typing import List, Dict

# --- Configuration and Settings ---
# Based on sfa_bbs.py structure
class AppSettings:
    def __init__(self):
        load_dotenv()
        # Centrala API key based on sfa_bbs.py
        self.centrala_api_key = os.getenv("AIDEVS_API_KEY")
        # AssemblyAI API key (not in sfa_bbs, keep existing)
        self.assembly_api_key = os.getenv("ASSEMBLY_API_KEY")
        # LLM API key based on sfa_bbs.py (prefers KLUSTER_API_KEY)
        self.openai_api_key = os.getenv("KLUSTER_API_KEY", os.getenv("OPENAI_API_KEY"))
        # LLM base URL based on sfa_bbs.py (prefers KLUSTER_BASE_URL)
        self.openai_api_base = os.getenv("KLUSTER_BASE_URL", os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1"))
        # LLM model based on sfa_bbs.py (prefers KLUSTER_LLM_MODEL)
        self.llm_model = os.getenv("KLUSTER_LLM_MODEL", os.getenv("OPENAI_LLM_MODEL", "gpt-3.5-turbo"))

        self.transcriptions_file = "adw/S02/S02E01/KILO/transcriptions.json"
        self.audio_dir = "adw/S02/S02E01/ZZZ_TRIALS"
        self.system_prompt_file = "adw/S02/S02E01/KILO/KILO_sys_prompt.txt"
        self.centrala_report_url = "https://c3ntrala.ag3nts.org/report" # Hardcoded as per plan, derived from API_BASE_URL/REPORT_ENDPOINT in sfa_bbs
        self.task_name = "mp3" # Specific task name

class LLMSettings:
     # This class can be extended if more LLM specific settings are needed
     pass

# --- Logging Setup ---
def setup_logging():
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Transcription Handling ---
def load_transcriptions(filepath):
    if not os.path.exists(filepath):
        return []
    with open(filepath, 'r', encoding='utf-8') as f:
        try:
            return json.load(f)
        except json.JSONDecodeError:
            logging.warning(f"Could not decode JSON from {filepath}. Starting with empty transcriptions.")
            return []

def save_transcriptions(filepath, transcriptions):
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(transcriptions, f, indent=4, ensure_ascii=False)

def get_transcribed_titles(transcriptions):
    return {t['title'] for t in transcriptions}

def transcribe_audio_files(settings: AppSettings) -> List[Dict]:
    """
    Transcribes audio files in a directory using AssemblyAI, caching results.

    Args:
        settings: AppSettings object containing configuration.

    Returns:
        List[Dict]: List of all available transcriptions (loaded and newly created).
    """
    audio_dir = settings.audio_dir
    transcriptions_file = settings.transcriptions_file
    aai_api_key = settings.assembly_api_key

    if not os.path.exists(audio_dir):
        logging.error(f"Audio directory not found: {audio_dir}")
        return []

    aai.settings.api_key = aai_api_key
    transcriber = aai.Transcriber(config=aai.TranscriptionConfig(language_code="pl")) # Set language to Polish

    existing_transcriptions = load_transcriptions(transcriptions_file)
    existing_transcriptions_map = {t.get('title'): t.get('content', '') for t in existing_transcriptions if t and t.get('title')}

    all_transcriptions = []
    newly_transcribed = False # Flag to indicate if any new transcriptions were made

    try:
        # List audio files (m4a, mp3, etc.)
        audio_files = [f for f in os.listdir(audio_dir) if f.lower().endswith(('.m4a', '.mp3', '.wav', '.flac'))]
        audio_files.sort() # Sort for consistent processing order

        if not audio_files:
            logging.warning(f"No supported audio files found in {audio_dir}.")
            # Add existing transcriptions to the list even if no new files are found
            all_transcriptions.extend(existing_transcriptions)
            return all_transcriptions


        for file_name in audio_files:
            if file_name in existing_transcriptions_map:
                logging.info(f"Transcription for '{file_name}' already exists. Using cached version.")
                all_transcriptions.append({"title": file_name, "content": existing_transcriptions_map[file_name]})
            else:
                audio_path = os.path.join(audio_dir, file_name)
                logging.info(f"Starting transcription for '{file_name}'...")
                try:
                    # Using default config for now, can be customized if needed
                    transcript = transcriber.transcribe(audio_path)

                    if transcript.status == aai.TranscriptStatus.error:
                        logging.error(f"Transcription failed for '{file_name}': {transcript.error}")
                        # Decide whether to add an entry for failed transcription or skip
                        # Skipping as per plan decision
                        pass
                    else: # Assuming status is completed
                        logging.info(f"Transcription completed for '{file_name}'.")
                        all_transcriptions.append({"title": file_name, "content": transcript.text})
                        newly_transcribed = True

                except Exception as e:
                    logging.error(f"An error occurred during AssemblyAI transcription for '{file_name}': {e}")
                    # Skipping as per plan decision
                    pass

        # Save transcriptions if any new ones were added or if the cache file didn't exist initially
        # This ensures the cache is updated with newly transcribed files and includes old ones.
        # It also handles the case where the file didn't exist and we just loaded old ones.
        if newly_transcribed or not os.path.exists(transcriptions_file):
             # Merge newly transcribed with existing ones to ensure the saved file is complete
             # This is important if some files were already cached and others were new.
             # A more robust approach might merge based on title, but appending new ones
             # and saving the combined list works if we trust the 'all_transcriptions' list.
             # Let's rebuild the map and then the list to be safe.
             final_transcription_map = {t.get('title'): t.get('content', '') for t in existing_transcriptions if t and t.get('title')}
             for t in all_transcriptions:
                 if t.get('title'):
                     final_transcription_map[t['title']] = t.get('content', '') # Corrected assignment

             final_transcription_list = [{"title": title, "content": content} for title, content in final_transcription_map.items()]

             save_transcriptions(transcriptions_file, final_transcription_list)
             # Update all_transcriptions list to reflect the saved state (including old ones)
             all_transcriptions = final_transcription_list


    except Exception as e:
        logging.error(f"An unexpected error occurred during audio transcription process: {e}")
        # Return whatever transcriptions were successfully loaded or created before the error
        # Ensure existing ones are included if the error happened during iteration
        if not all_transcriptions and existing_transcriptions:
             all_transcriptions.extend(existing_transcriptions)
        pass # Continue to return the list even if incomplete

    return all_transcriptions


# --- LLM Interaction ---
def load_system_prompt(filepath):
    if not os.path.exists(filepath):
        logging.error(f"System prompt file not found: {filepath}")
        return ""
    with open(filepath, 'r', encoding='utf-8') as f:
        return f.read()

def create_llm_context(transcriptions):
    context = ""
    for t in transcriptions:
        context += f"## Przesłuchanie: {t['title']}\n\n{t['content']}\n\n---\n\n"
    return context.strip()

def get_street_name_from_llm(transcription_context, settings: AppSettings):
    logging.info("Sending context to LLM to find street name...")
    system_prompt = load_system_prompt(settings.system_prompt_file)

    if not system_prompt:
        logging.error("System prompt is empty or could not be loaded. Cannot proceed with LLM.")
        return None

    client = OpenAI(
        api_key=settings.openai_api_key,
        base_url=settings.openai_api_base
    )

    try:
        response = client.chat.completions.create(
            model=settings.llm_model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Oto transkrypcje przesłuchań:\n\n{transcription_context}\n\nNa podstawie powyższych transkrypcji i swojej wiedzy, podaj nazwę ulicy, na której znajduje się konkretny instytut uczelni, gdzie wykłada profesor Andrzej Maj. Podaj TYLKO nazwę ulicy."}
            ],
            temperature=0.1 # Keep temperature low for more deterministic results
        )
        street_name = response.choices[0].message.content.strip()
        logging.info(f"LLM returned: {street_name}")
        return street_name
    except Exception as e:
        logging.error(f"An error occurred during LLM interaction: {e}")
        return None

# --- Reporting to Centrala ---
def report_to_centrala(street_name, settings: AppSettings):
    logging.info(f"Reporting street name '{street_name}' to Centrala...")
    payload = {
        "task": settings.task_name,
        "apikey": settings.centrala_api_key,
        "answer": street_name
    }

    try:
        response = requests.post(settings.centrala_report_url, json=payload)
        response.raise_for_status() # Raise an HTTPError for bad responses (4xx or 5xx)
        logging.info(f"Centrala response status code: {response.status_code}")
        logging.info(f"Centrala response body: {response.text}")
        return response.text
    except requests.exceptions.RequestException as e:
        logging.error(f"An error occurred while reporting to Centrala: {e}")
        return None

# --- Flag Checking ---
def check_for_flag(response_text):
    if response_text and "FLG" in response_text:
        logging.info("Flag found in Centrala response!")
        # Assuming the flag is clearly present in the response text
        # More sophisticated parsing might be needed depending on the exact format
        print(f"FLAG: {response_text}")
        return True
    return False

# --- Main Execution ---
def main():
    setup_logging()
    settings = AppSettings()

    if not settings.assembly_api_key or not settings.openai_api_key or not settings.centrala_api_key:
        logging.error("Missing one or more API keys. Please set ASSEMBLY_API_KEY, AIDEVS_API_KEY, and either KLUSTER_API_KEY/OPENAI_API_KEY environment variables.")
        return

    # 1. Process Transcriptions
    transcriptions = transcribe_audio_files(settings)
    if not transcriptions:
        logging.error("No transcriptions available. Cannot proceed.")
        return

    # 2. Prepare LLM Context
    transcription_context = create_llm_context(transcriptions)
    if not transcription_context:
        logging.error("Failed to create transcription context for LLM.")
        return

    # 3. Get Street Name from LLM
    street_name = get_street_name_from_llm(transcription_context, settings)
    if not street_name:
        logging.error("Failed to get street name from LLM.")
        return

    # 4. Report to Centrala and Check Flag
    centrala_response = report_to_centrala(street_name, settings)
    if centrala_response:
        check_for_flag(centrala_response)
    else:
        logging.error("Failed to get response from Centrala.")

if __name__ == "__main__":
    main()