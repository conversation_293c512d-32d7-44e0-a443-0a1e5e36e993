# Plan Implementacji Single File Agent (SFA) - Zadanie S02E01

## 1. Cel Implementacji

Stworzenie pojedynczego skryptu w języku Python (SFA) zdolnego do automatycznego pobrania, przetworzenia i analizy nagrań audio świadków w celu ustalenia nazwy ulicy konkretnego instytutu uczelni, gdzie wykłada profesor Andrzej Maj, a następnie wysłania tej informacji do Centrali w wymaganym formacie.

## 2. Workflow Aplikacji SFA

Aplikacja SFA będzie działać zgodnie z następującym przepływem:

```mermaid
graph TD
    A[Start SFA] --> B{Sprawdź istniejące transkrypcje};
    B --> |Brak transkrypcji lub nowe pliki| C[Iteruj przez pliki .m4a w ZZZ_TRIALS];
    C --> D[Transkrybuj plik za pomocą AssemblyAI];
    D --> E[Zap<PERSON>z transkrypcję do transcriptions.json];
    E --> C;
    C --> F{Wszystkie pliki przetworzone?};
    F --> |Tak| G[Wczytaj wszystkie transkrypcje z transcriptions.json];
    B --> |Wszystkie transkrypcje istnieją| G;
    G --> H[Połącz transkrypcje w jeden kontekst];
    H --> I[Wczytaj system prompt];
    I --> J[Sformułuj prompt dla LLM];
    J --> K[Wyślij prompt do LLM (OpenAI API)];
    K --> L[Wyodrębnij nazwę ulicy z odpowiedzi LLM];
    L --> M[Przygotuj payload JSON dla Centrali];
    M --> N[Wyślij odpowiedź do Centrali];
    N --> O{Sprawdź odpowiedź Centrali na "FLG"};
    O --> |Znaleziono "FLG"| P[Wypisz flagę do konsoli];
    O --> |Nie znaleziono "FLG"| Q[Zakończ (lub loguj błąd/brak flagi)];
    P --> R[Stop];
    Q --> R[Stop];
```

## 3. Struktura Kodu (Single File Agent)

Kod zostanie zaimplementowany w pojedynczym pliku Python (`adw/S02/S02E01/KILO/kilo_sfa.py` - nazwa pliku zgodna z konwencją SFA). Struktura będzie oparta o przykład z `adw/sfa_bbs.py`, z podziałem na logiczne funkcje/metody zgodnie z zasadami SOLID, DRY, KISS, YAGNI.

Główne sekcje/funkcje:

* **Konfiguracja:** Wczytywanie kluczy API i ustawień zgodnie ze strukturą `sfa_bbs.py` (sekcje `APPLICATION SETTINGS`, `LLM SETTINGS`).
* **Logowanie:** Implementacja logowania błędów zgodnie z przykładem z `sfa_bbs.py`.
* **Pobieranie/Rozpakowywanie (usunięte zgodnie z wytycznymi):** Ta część nie będzie potrzebna, ponieważ pliki audio są już dostępne lokalnie.
* **Transkrypcja Audio:**
  * Funkcja do sprawdzania, czy transkrypcja dla danego pliku audio już istnieje w `transcriptions.json`.
  * Funkcja do wykonywania transkrypcji pojedynczego pliku `.m4a` przy użyciu AssemblyAI API, zgodnie z podanym przykładem kodu i ustawieniami dla języka polskiego.
  * Funkcja do zarządzania plikiem `transcriptions.json` (wczytywanie, dodawanie nowych transkrypcji, zapisywanie). Format danych: lista obiektów `{"title": "nazwa_pliku", "content": "transkrypcja"}`.
  * Główna funkcja orkiestrująca proces transkrypcji dla wszystkich plików w folderze `adw/S02/S02E01/ZZZ_TRIALS`, z pominięciem już przetworzonych plików.
* **Przygotowanie Kontekstu dla LLM:** Funkcja do wczytania wszystkich transkrypcji z `transcriptions.json` i połączenia ich w jeden ciąg tekstowy.
* **Interakcja z LLM:**
  * Funkcja do wczytania system promptu z [`adw/S02/S02E01/KILO/KILO_sys_prompt.txt`](adw/S02/S02E01/KILO/KILO_sys_prompt.txt).
  * Funkcja do sformułowania kompletnego promptu dla LLM (system prompt + kontekst transkrypcji + instrukcje).
  * Funkcja do wysłania zapytania do wybranego modelu LLM poprzez OpenAI API.
  * Funkcja do wyodrębnienia nazwy ulicy z odpowiedzi LLM. Zakładamy, że LLM zwróci tylko nazwę ulicy zgodnie z instrukcjami w system prompcie.
* **Raportowanie do Centrali:**
  * Funkcja do przygotowania payloadu JSON (`{"task": "mp3", "apikey": "YOUR_API_KEY", "answer": "Nazwa ulicy"}`). Klucz API Centrali będzie wczytywany z konfiguracji.
  * Funkcja do wysłania żądania HTTP POST do adresu raportowania Centrali (<https://c3ntrala.ag3nts.org/report>).
  * Funkcja do weryfikacji odpowiedzi z Centrali w poszukiwaniu stringa "FLG". Jeśli znaleziono, wypisanie flagi do konsoli.
* **Główna Logika (main):** Funkcja orkiestrująca wszystkie powyższe kroki w odpowiedniej kolejności, realizująca workflow aplikacji.

## 4. Stack Technologiczny

* **Język:** Python 3.x
* **Biblioteki:**
  * `requests`: Do wykonywania żądań HTTP (pobieranie, wysyłanie do Centrali).
  * `zipfile`: (Niepotrzebne, pliki są lokalnie).
  * `assemblyai`: Do transkrypcji audio.
  * `openai`: Do interakcji z modelem LLM (OpenAI API).
  * `json`: Do pracy z plikami JSON (transkrypcje).
  * `os`, `glob`: Do pracy z systemem plików (listowanie plików `.m4a`).
* **Modele AI:**
  * ASR: AssemblyAI API.
  * LLM: Wybrany model dostępny przez OpenAI API.

## 5. Zarządzanie Kluczami API i Konfiguracją

Klucze API (AssemblyAI, OpenAI, Centrala) oraz inne ustawienia aplikacji będą zarządzane zgodnie ze strukturą i podejściem zaprezentowanym w pliku `adw/sfa_bbs.py`, w szczególności w sekcjach `APPLICATION SETTINGS` i `LLM SETTINGS`. Zaleca się wczytywanie ich ze zmiennych środowiskowych lub dedykowanego pliku konfiguracyjnego (np. `.env`).

## 6. Obsługa Błędów i Logowanie

Obsługa błędów i logowanie zostaną zaimplementowane zgodnie z przykładem w `adw/sfa_bbs.py`. Kluczowe operacje (pobieranie, transkrypcja, interakcja z LLM, wysyłanie do Centrali) będą objęte mechanizmami `try...except` w celu logowania potencjalnych problemów i zapewnienia stabilności działania SFA.

## 7. Weryfikacja Flagi

Weryfikacja flagi będzie przeprowadzana WYŁĄCZNIE na odpowiedzi otrzymanej z serwera Centrali po wysłaniu rozwiązania. Aplikacja będzie szukać stringa "FLG" w treści odpowiedzi. W przypadku znalezienia, flaga zostanie wypisana do konsoli. Nie przewiduje się iteracji z LLM w celu poszukiwania flagi; LLM służy tylko do ustalenia nazwy ulicy. Bezpiecznik 5 kroków nie ma zastosowania w tym konkretnym workflow, ponieważ interakcja z Centralą jest jednorazowa po uzyskaniu odpowiedzi od LLM.

## 8. Lokalizacja Plików

* Pliki audio do transkrypcji: `/Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S02/S02E01/ZZZ_TRIALS/`
* Plik z transkrypcjami: `adw/S02/S02E01/KILO/transcriptions.json`
* Plik system promptu: [`adw/S02/S02E01/KILO/KILO_sys_prompt.txt`](adw/S02/S02E01/KILO/KILO_sys_prompt.txt)
* Plik SFA: `adw/S02/S02E01/KILO/kilo_sfa.py`
