"""
ZENCODER - Single File Agent (SFA)
Zadanie: <PERSON><PERSON><PERSON> trans<PERSON> przesłuchań w celu ustalenia nazwy ulicy, 
na której znajduje się instytut, gdzie wykłada profesor And<PERSON><PERSON>.

Author: AI Developer
Date: 2025-05-21
Version: 1.0.0
"""

# --- IMPORTS ---
import os
import re
import json
import logging
import time
import glob
from typing import Dict, List, Any, Tuple, Optional, Union
from dotenv import load_dotenv
from openai import OpenAI, APIError, APIConnectionError, RateLimitError
import assemblyai as aai
import requests

# --- CONFIGURATION ---
# Load environment variables
load_dotenv()

# --- LOGGING CONFIGURATION ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# --- APPLICATION SETTINGS ---
TASK_NAME = "mp3"
API_BASE_URL = os.getenv("API_BASE_URL", "https://c3ntrala.ag3nts.org")
REPORT_ENDPOINT = f"{API_BASE_URL}/report"
API_KEY = os.getenv("AIDEVS_API_KEY")

# Ścieżki do plików i katalogów
BASE_DIR = "/Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent"
AUDIO_DIR = f"{BASE_DIR}/adw/S02/S02E01/ZZZ_TRIALS"
TRANSCRIPTIONS_FILE = f"{BASE_DIR}/adw/S02/S02E01/ZENCODER/transcriptions.json"
SYSTEM_PROMPT_PATH = f"{BASE_DIR}/adw/S02/S02E01/ZENCODER/ZENCODER_sys_prompt.txt"

# AssemblyAI settings
AAI_API_KEY = os.getenv("ASSEMBLY_API_KEY")  # Domyślny klucz jako fallback

# Request settings
REQUEST_TIMEOUT = 30  # seconds
RETRY_ATTEMPTS = 2
RETRY_DELAY = 2  # seconds

# --- LLM SETTINGS ---
LLM_MODEL = "deepseek-ai/DeepSeek-V3-0324"
LLM_API_KEY = os.getenv("KLUSTER_API_KEY")
LLM_BASE_URL = "https://api.kluster.ai/v1"
LLM_MAX_TOKENS = 4000
LLM_TEMPERATURE = 0.6
LLM_TOP_P = 1.0

# Default system prompt (used if file not found)
DEFAULT_SYSTEM_PROMPT = """
Jesteś zaawansowanym agentem analitycznym specjalizującym się w analizie zeznań świadków.
Twoim zadaniem jest ustalenie nazwy ulicy, na której znajduje się konkretny instytut uczelni, 
gdzie wykłada profesor Andrzej Maj.
"""

# --- REGEX PATTERNS ---
FLAG_REGEX = r"FLG[a-zA-Z0-9_-]+"
FLAG_REGEX_CURLY = r"\{\{FLG:[a-zA-Z0-9_-]+\}\}"

# --- HELPER FUNCTIONS ---

def validate_configuration() -> bool:
    """
    Validates that all required configuration variables are set.

    Returns:
        bool: True if configuration is valid, False otherwise
    """
    required_env_vars = {
        "AIDEVS_API_KEY": "API key for Central API",
        "KLUSTER_API_KEY": "API key for Kluster.ai",
        "ASSEMBLY_API_KEY": "API key for AssemblyAI",
    }
    missing_vars = []

    for var, description in required_env_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"{var} ({description})")

    if missing_vars:
        logger.critical(f"Missing required environment variables: {', '.join(missing_vars)}")
        return False

    # Check if API_KEY is set
    if not API_KEY:
        logger.critical("API_KEY is not set, check AIDEVS_API_KEY environment variable")
        return False

    # Check if LLM_API_KEY is set
    if not LLM_API_KEY:
        logger.critical("LLM_API_KEY is not set, check KLUSTER_API_KEY environment variable")
        return False

    # Check if audio directory exists
    if not os.path.isdir(AUDIO_DIR):
        logger.critical(f"Audio directory not found: {AUDIO_DIR}")
        return False
        
    # Check if AssemblyAI API key is set
    if not AAI_API_KEY:
        logger.critical("AssemblyAI API key is not set, check ASSEMBLY_API_KEY environment variable")
        return False

    return True

def load_system_prompt() -> str:
    """
    Wczytuje system prompt z pliku.

    Returns:
        str: Zawartość pliku z system promptem
    """
    try:
        with open(SYSTEM_PROMPT_PATH, 'r', encoding='utf-8') as file:
            system_prompt = file.read()
        logger.info(f"Wczytano system prompt z pliku: {SYSTEM_PROMPT_PATH}")
        return system_prompt
    except Exception as e:
        logger.error(f"Błąd podczas wczytywania system promptu: {e}")
        logger.warning("Używanie domyślnego system promptu")
        return DEFAULT_SYSTEM_PROMPT

def check_for_flag(response) -> Optional[str]:
    """
    Sprawdza, czy odpowiedź zawiera flagę w formatach:
    - FLG[a-zA-Z0-9_-]+
    - {{FLG:[a-zA-Z0-9_-]+}}

    Args:
        response: Odpowiedź do sprawdzenia (może być string, dict lub inny typ)

    Returns:
        Optional[str]: Znaleziona flaga lub None, jeśli nie znaleziono
    """
    # Convert response to string for easier searching
    response_str = str(response)

    if not response_str:
        return None

    # Search for flag in {{FLG:XXXX}} format
    match_curly = re.search(FLAG_REGEX_CURLY, response_str)
    if match_curly:
        flag = match_curly.group(0)
        logger.info(f"Flag found in {{{{FLG:XXXX}}}} format: {flag}")
        print(f"\n{'='*50}\nFLAG: {flag}\n{'='*50}\n")
        return flag

    # Search for flag in FLG[a-zA-Z0-9_-]+ format
    match = re.search(FLAG_REGEX, response_str)
    if match:
        flag = match.group(0)
        logger.info(f"Flag found in FLG[a-zA-Z0-9_-]+ format: {flag}")
        print(f"\n{'='*50}\nFLAG: {flag}\n{'='*50}\n")
        return flag

    # Check if text contains the word "flag" or "FLG"
    if "flag" in response_str.lower() or "flg" in response_str.lower():
        logger.info(f"Text contains the word 'flag' or 'FLG': {response_str}")
        print(f"\n{'='*50}\nPOTENTIAL FLAG: {response_str}\n{'='*50}\n")
        return response_str

    logger.info("No flag found in response")
    return None

# --- TRANSCRIPTION FUNCTIONS ---

def load_transcriptions() -> List[Dict]:
    """
    Wczytuje istniejące transkrypcje z pliku JSON.
    Jeśli plik nie istnieje, zwraca pustą listę.

    Returns:
        List[Dict]: Lista transkrypcji w formacie [{"title": "nazwa_pliku", "content": "transkrypcja"}, ...]
    """
    try:
        if os.path.exists(TRANSCRIPTIONS_FILE):
            with open(TRANSCRIPTIONS_FILE, 'r', encoding='utf-8') as file:
                transcriptions = json.load(file)
            logger.info(f"Wczytano {len(transcriptions)} transkrypcji z pliku: {TRANSCRIPTIONS_FILE}")
            return transcriptions
        else:
            logger.info(f"Plik z transkrypcjami nie istnieje: {TRANSCRIPTIONS_FILE}")
            return []
    except Exception as e:
        logger.error(f"Błąd podczas wczytywania transkrypcji: {e}")
        return []

def save_transcriptions(transcriptions: List[Dict]) -> bool:
    """
    Zapisuje transkrypcje do pliku JSON.

    Args:
        transcriptions: Lista transkrypcji w formacie [{"title": "nazwa_pliku", "content": "transkrypcja"}, ...]

    Returns:
        bool: True jeśli zapis się powiódł, False w przeciwnym razie
    """
    try:
        # Ensure directory exists
        os.makedirs(os.path.dirname(TRANSCRIPTIONS_FILE), exist_ok=True)
        
        with open(TRANSCRIPTIONS_FILE, 'w', encoding='utf-8') as file:
            json.dump(transcriptions, file, ensure_ascii=False, indent=2)
        logger.info(f"Zapisano {len(transcriptions)} transkrypcji do pliku: {TRANSCRIPTIONS_FILE}")
        return True
    except Exception as e:
        logger.error(f"Błąd podczas zapisywania transkrypcji: {e}")
        return False

def transcribe_audio(file_path: str) -> Optional[str]:
    """
    Transkrybuje plik audio przy użyciu AssemblyAI.

    Args:
        file_path: Ścieżka do pliku audio

    Returns:
        Optional[str]: Tekst transkrypcji lub None w przypadku błędu
    """
    logger.info(f"Transkrybuję plik: {file_path}")
    
    try:
        # Configure AssemblyAI
        aai.settings.api_key = AAI_API_KEY
        
        # Create transcription config with Polish language setting and additional parameters for better accuracy
        config = aai.TranscriptionConfig(
            speech_model=aai.SpeechModel.best,  # Najlepszy model dla dokładności
            language_code="pl",  # Ustawienie języka polskiego
            punctuate=True,  # Dodawanie interpunkcji
            format_text=True,  # Formatowanie tekstu (np. liczby, daty)
            disfluencies=False  # Pomijanie wypełniaczy typu "um", "uh" itp.
        )
        
        # Transcribe audio
        transcript = aai.Transcriber(config=config).transcribe(file_path)
        
        # Check for errors
        if transcript.status == "error":
            logger.error(f"Transkrypcja nie powiodła się: {transcript.error}")
            return None
        
        logger.info(f"Transkrypcja zakończona pomyślnie dla pliku: {file_path}")
        return transcript.text
    
    except Exception as e:
        logger.error(f"Błąd podczas transkrypcji pliku {file_path}: {e}")
        return None

def process_audio_files() -> List[Dict]:
    """
    Przetwarza wszystkie pliki audio w katalogu AUDIO_DIR.
    Jeśli transkrypcja dla danego pliku już istnieje, pomija go.

    Returns:
        List[Dict]: Zaktualizowana lista transkrypcji
    """
    logger.info(f"Rozpoczynam przetwarzanie plików audio z katalogu: {AUDIO_DIR}")
    
    # Load existing transcriptions
    transcriptions = load_transcriptions()
    
    # Get list of existing transcription titles
    existing_titles = [t["title"] for t in transcriptions]
    
    # Get all audio files
    audio_files = glob.glob(os.path.join(AUDIO_DIR, "*.m4a"))
    logger.info(f"Znaleziono {len(audio_files)} plików audio")
    
    # Process each audio file
    for file_path in audio_files:
        file_name = os.path.basename(file_path)
        
        # Skip if already transcribed
        if file_name in existing_titles:
            logger.info(f"Pomijam plik {file_name} - transkrypcja już istnieje")
            continue
        
        # Transcribe audio
        transcription = transcribe_audio(file_path)
        
        # Add to transcriptions list if successful
        if transcription:
            transcriptions.append({
                "title": file_name,
                "content": transcription
            })
            
            # Save after each successful transcription to avoid data loss
            save_transcriptions(transcriptions)
    
    logger.info(f"Zakończono przetwarzanie plików audio. Łącznie {len(transcriptions)} transkrypcji")
    return transcriptions

# --- ANALYSIS FUNCTIONS ---

def prepare_prompt(transcriptions: List[Dict]) -> str:
    """
    Przygotowuje prompt dla modelu LLM na podstawie transkrypcji.

    Args:
        transcriptions: Lista transkrypcji

    Returns:
        str: Prompt dla modelu LLM
    """
    logger.info("Przygotowuję prompt dla modelu LLM")
    
    # Przygotuj nagłówek promptu
    prompt = "Twoim zadaniem jest ustalenie nazwy ulicy, na której znajduje się konkretny instytut uczelni, gdzie wykłada profesor Andrzej Maj.\n\n"
    prompt += "Poniżej znajdują się transkrypcje przesłuchań świadków, którzy mieli kontakt z profesorem Majem. "
    prompt += "Zeznania mogą zawierać sprzeczne informacje lub celowe wprowadzanie w błąd. "
    prompt += "Szczególnie ważne jest zeznanie Rafała, który utrzymywał bliskie kontakty z profesorem, choć jego stan psychiczny jest niestabilny.\n\n"
    prompt += "Przeanalizuj krok po kroku wszystkie transkrypcje i ustal nazwę ulicy, na której znajduje się instytut, gdzie wykłada profesor Maj.\n\n"
    
    # Dodaj transkrypcje
    for i, transcription in enumerate(transcriptions, 1):
        prompt += f"--- TRANSKRYPCJA {i}: {transcription['title']} ---\n\n"
        prompt += f"{transcription['content']}\n\n"
    
    # Dodaj instrukcje końcowe
    prompt += "Przeanalizuj powyższe transkrypcje krok po kroku:\n"
    prompt += "1. Zidentyfikuj wszystkie wzmianki o uczelni, instytucie i lokalizacji.\n"
    prompt += "2. Zwróć szczególną uwagę na zeznanie Rafała, który miał bliskie kontakty z profesorem.\n"
    prompt += "3. Porównaj informacje z różnych zeznań, aby zweryfikować ich wiarygodność.\n"
    prompt += "4. Wykorzystaj swoją wiedzę o polskich uczelniach, aby zweryfikować podane informacje.\n"
    prompt += "5. Ustal nazwę ulicy, na której znajduje się instytut, gdzie wykłada profesor Maj.\n\n"
    prompt += "Pamiętaj, że szukasz nazwy ulicy, na której znajduje się konkretny instytut, a nie główna siedziba uczelni."
    
    logger.info("Prompt przygotowany")
    return prompt

def call_llm(prompt: str, attempt: int = 1) -> Optional[str]:
    """
    Calls the LLM with the given prompt and handles errors with retry logic.

    Args:
        prompt: The user prompt to send to the LLM
        attempt: Current attempt number (for retry logic)

    Returns:
        Optional[str]: The LLM's response, or None if failed
    """
    logger.info(f"Calling LLM with prompt (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    try:
        # Load system prompt
        system_prompt = load_system_prompt()

        # Initialize OpenAI client with Kluster.ai settings
        client = OpenAI(
            api_key=LLM_API_KEY,
            base_url=LLM_BASE_URL
        )

        logger.info(f"Using model: {LLM_MODEL} via API: {LLM_BASE_URL}")

        # Call the LLM
        completion = client.chat.completions.create(
            model=LLM_MODEL,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            temperature=LLM_TEMPERATURE,
            max_completion_tokens=LLM_MAX_TOKENS,
            top_p=LLM_TOP_P
        )

        response = completion.choices[0].message.content.strip()
        logger.info("LLM call successful")
        
        # Check for flag in response
        flag = check_for_flag(response)
        if flag:
            logger.info(f"Flag found in LLM response: {flag}")
        
        return response

    except (APIError, APIConnectionError, RateLimitError) as e:
        logger.error(f"LLM API error: {e}")

        # Retry logic
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return call_llm(prompt, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except Exception as e:
        logger.error(f"Unexpected error during LLM call: {e}")
        return None

def extract_street_name(response: str) -> str:
    """
    Ekstrahuje nazwę ulicy z odpowiedzi modelu LLM.
    
    Args:
        response: Odpowiedź modelu LLM
        
    Returns:
        str: Nazwa ulicy
    """
    logger.info("Ekstrahuję nazwę ulicy z odpowiedzi modelu")
    
    # Look for explicit mentions of the street
    street_patterns = [
        r"ulica\s+([A-ZŻŹĆĄŚĘŁÓŃ][a-zżźćńółęąś]+(?:\s+[A-ZŻŹĆĄŚĘŁÓŃ][a-zżźćńółęąś]+)*)",
        r"ul\.\s+([A-ZŻŹĆĄŚĘŁÓŃ][a-zżźćńółęąś]+(?:\s+[A-ZŻŹĆĄŚĘŁÓŃ][a-zżźćńółęąś]+)*)",
        r"Ulica\s+([A-ZŻŹĆĄŚĘŁÓŃ][a-zżźćńółęąś]+(?:\s+[A-ZŻŹĆĄŚĘŁÓŃ][a-zżźćńółęąś]+)*)",
        r"Ul\.\s+([A-ZŻŹĆĄŚĘŁÓŃ][a-zżźćńółęąś]+(?:\s+[A-ZŻŹĆĄŚĘŁÓŃ][a-zżźćńółęąś]+)*)",
        r"ulicy\s+([A-ZŻŹĆĄŚĘŁÓŃ][a-zżźćńółęąś]+(?:\s+[A-ZŻŹĆĄŚĘŁÓŃ][a-zżźćńółęąś]+)*)",
        r"ULICA\s+([A-ZŻŹĆĄŚĘŁÓŃ][a-zżźćńółęąś]+(?:\s+[A-ZŻŹĆĄŚĘŁÓŃ][a-zżźćńółęąś]+)*)"
    ]
    
    for pattern in street_patterns:
        match = re.search(pattern, response)
        if match:
            street_name = match.group(1)
            logger.info(f"Znaleziono nazwę ulicy za pomocą wzorca: {street_name}")
            return f"ul. {street_name}"
    
    # If no explicit mention, look for a conclusion section
    conclusion_patterns = [
        r"Główny Wniosek:?\s*([^\n.]+)",
        r"Odpowiedź:?\s*([^\n.]+)",
        r"Nazwa ulicy:?\s*([^\n.]+)",
        r"Ulica:?\s*([^\n.]+)"
    ]
    
    for pattern in conclusion_patterns:
        match = re.search(pattern, response)
        if match:
            conclusion = match.group(1).strip()
            logger.info(f"Znaleziono wniosek: {conclusion}")
            
            # Check if the conclusion contains a street name
            for street_pattern in street_patterns:
                street_match = re.search(street_pattern, conclusion)
                if street_match:
                    street_name = street_match.group(1)
                    logger.info(f"Znaleziono nazwę ulicy w konkluzji: {street_name}")
                    return f"ul. {street_name}"
            
            # If no explicit street pattern in conclusion, return the whole conclusion
            # This assumes the model directly answered with the street name
            return conclusion
    
    # If all else fails, return the last non-empty line as a best guess
    lines = [line.strip() for line in response.split('\n') if line.strip()]
    if lines:
        last_line = lines[-1]
        logger.warning(f"Nie znaleziono wyraźnej nazwy ulicy, zwracam ostatnią linię: {last_line}")
        return last_line
    
    logger.error("Nie udało się wyekstrahować nazwy ulicy z odpowiedzi")
    return "Nie znaleziono nazwy ulicy"

def analyze_transcriptions(transcriptions: List[Dict]) -> Optional[str]:
    """
    Analizuje transkrypcje przy użyciu modelu LLM w celu ustalenia nazwy ulicy.
    
    Args:
        transcriptions: Lista transkrypcji
        
    Returns:
        Optional[str]: Nazwa ulicy lub None w przypadku błędu
    """
    logger.info("Rozpoczynam analizę transkrypcji")
    
    # Prepare prompt
    prompt = prepare_prompt(transcriptions)
    
    # Call LLM
    response = call_llm(prompt)
    if not response:
        logger.error("Nie udało się uzyskać odpowiedzi od modelu LLM")
        return None
    
    # Extract street name from response
    street_name = extract_street_name(response)
    
    logger.info(f"Analiza zakończona. Znaleziona ulica: {street_name}")
    return street_name

# --- REPORTING FUNCTIONS ---

def send_report(answer: str, attempt: int = 1) -> Optional[Dict]:
    """
    Sends the final report/answer to the specified endpoint.

    Args:
        answer: The answer to send (street name)
        attempt: Current attempt number (for retry logic)

    Returns:
        Optional[Dict]: The response from the server, or None if failed
    """
    logger.info(f"Sending report to {REPORT_ENDPOINT} (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    # Ensure the answer is properly formatted
    clean_answer = answer.strip()
    
    # Remove quotes if present
    if (clean_answer.startswith('"') and clean_answer.endswith('"')) or \
       (clean_answer.startswith("'") and clean_answer.endswith("'")):
        clean_answer = clean_answer[1:-1]

    logger.info(f"Original answer: '{answer}'")
    logger.info(f"Cleaned answer: '{clean_answer}'")

    payload = {
        "task": TASK_NAME,
        "apikey": API_KEY,
        "answer": clean_answer
    }

    headers = {
        "Content-Type": "application/json; charset=utf-8"
    }

    # Log full payload before sending
    logger.info(f"Sending payload: {json.dumps(payload, ensure_ascii=False)}")

    try:
        response = requests.post(
            REPORT_ENDPOINT,
            json=payload,  # Use json instead of data to let requests handle serialization
            headers=headers,
            timeout=REQUEST_TIMEOUT
        )

        # Log full server response
        logger.info(f"Response code: {response.status_code}")
        logger.info(f"Response headers: {dict(response.headers)}")
        logger.info(f"Response content: {response.text}")

        # Display full response in console
        print(f"\n{'='*50}")
        print(f"RESPONSE FROM SERVER:")
        print(f"Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Content: {response.text}")
        print(f"{'='*50}\n")

        response.raise_for_status()

        result = response.json()
        logger.info("Report sent successfully")
        return result

    except requests.exceptions.RequestException as e:
        logger.error(f"Error sending report: {e}")

        if hasattr(e, 'response') and hasattr(e.response, 'text'):
            logger.error(f"Server response: {e.response.text}")
            logger.error(f"Response headers: {dict(e.response.headers) if hasattr(e.response, 'headers') else 'No headers'}")

            # Display full error response in console
            print(f"\n{'='*50}")
            print(f"ERROR FROM SERVER:")
            print(f"Code: {e.response.status_code if hasattr(e.response, 'status_code') else 'No code'}")
            print(f"Headers: {dict(e.response.headers) if hasattr(e.response, 'headers') else 'No headers'}")
            print(f"Content: {e.response.text}")
            print(f"{'='*50}\n")

        # Retry logic
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return send_report(answer, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response: {e}")
        return None

# --- MAIN FUNCTION ---

def main():
    """
    Main function that orchestrates the agent's workflow.
    """
    logger.info(f"--- Starting {TASK_NAME} agent ---")

    # Validate configuration
    if not validate_configuration():
        logger.critical("Invalid configuration. Exiting.")
        return

    try:
        # Step 1: Process audio files and generate transcriptions
        transcriptions = process_audio_files()
        if not transcriptions:
            logger.error("No transcriptions available. Exiting.")
            return

        # Step 2: Analyze transcriptions to find the street name
        street_name = analyze_transcriptions(transcriptions)
        if not street_name:
            logger.error("Failed to determine street name. Exiting.")
            return

        logger.info(f"Found street name: {street_name}")

        # Step 3: Send report to Central API
        report_response = send_report(street_name)
        if not report_response:
            logger.error("Failed to send report. Exiting.")
            return

        # Step 4: Check for flag in response
        flag = check_for_flag(report_response)

        # Step 5: Display results
        if flag:
            logger.info(f"SUCCESS! Flag: {flag}")
            print(f"\n{'='*50}\nFLAG: {flag}\n{'='*50}\n")
        else:
            logger.info("Task completed, but no flag found in response")
            print(f"\n{'='*50}\nRESPONSE: {json.dumps(report_response, indent=2)}\n{'='*50}\n")

    except Exception as e:
        logger.critical(f"Unexpected error: {e}", exc_info=True)

    logger.info(f"--- {TASK_NAME} agent finished ---")

# --- SCRIPT ENTRY POINT ---

if __name__ == "__main__":
    main()