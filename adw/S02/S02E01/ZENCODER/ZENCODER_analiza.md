# Analiza zadania S02E01 - Przesłuchania

## O co chodzi w zadaniu?

Zadanie polega na ustaleniu nazwy ulicy, na której znajduje się konkretny instytut uczelni, gdzie wykłada profesor Andrzej Maj. Informacje potrzebne do rozwiązania zadania znajdują się w nagraniach przesłuchań świadków, które są dostępne do pobrania jako archiwum ZIP.

Kluczowe elementy zadania:

1. Pobranie i rozpakowanie archiwum z nagraniami przesłuchań
2. Wygenerowanie transkrypcji nagrań audio (pliki m4a)
3. Zbudowanie kontekstu dla modelu LLM na podstawie transkrypcji
4. Sformułowanie odpowiedniego promptu dla LLM, który pomoże znaleźć nazwę ulicy
5. <PERSON><PERSON><PERSON><PERSON> odpowiedzi (nazwy ulicy) do Centrali w określonym formacie JSON

Warto zauważyć, że jedno z nagrań (Rafała) jest opisane jako chaotyczne i może wprowadzać w błąd, ale jest to jedyna osoba, co do której jesteśmy pewni, że utrzymywała bliskie kontakty z profesorem.

## Plan działania

### 1. Pobranie i przygotowanie danych

- Pobranie archiwum `przesluchania.zip` z podanego adresu
- Rozpakowanie archiwum i identyfikacja plików audio

### 2. Transkrypcja nagrań

- Wykorzystanie modelu do transkrypcji audio (np. Whisper od OpenAI)
- Zapisanie transkrypcji każdego nagrania do plików tekstowych
- Opcjonalnie: wstępne przetworzenie transkrypcji (usunięcie szumów, poprawienie błędów)

### 3. Analiza transkrypcji

- Połączenie wszystkich transkrypcji w jeden kontekst
- Sformułowanie odpowiedniego promptu dla LLM, który:
  - Jasno określi cel (znalezienie nazwy ulicy, gdzie znajduje się instytut)
  - Zawierać będzie transkrypcje jako kontekst
  - Poprosi model o krokową analizę i wyciąganie wniosków
  - Zachęci model do wykorzystania swojej wiedzy o uczelniach w Polsce

### 4. Uzyskanie odpowiedzi

- Wysłanie promptu do wybranego modelu LLM
- Analiza odpowiedzi i wyodrębnienie nazwy ulicy

### 5. Wysłanie odpowiedzi

- Przygotowanie odpowiedzi w formacie JSON zgodnie z wymaganiami
- Wysłanie odpowiedzi do Centrali pod wskazany adres

## Proponowany stack technologiczny

### Języki programowania

- **Python** - idealny do tego typu zadań ze względu na bogaty ekosystem bibliotek do przetwarzania danych, AI i integracji z API

### Biblioteki i narzędzia do transkrypcji audio

- **OpenAI Whisper** - model do transkrypcji audio na tekst (dostępny przez API OpenAI lub jako model open-source)
- Alternatywnie: **Faster-Whisper** - szybsza implementacja Whisper
- Dla lokalnego przetwarzania: **whisper.cpp** - lekka implementacja Whisper

### Modele LLM do analizy transkrypcji

- **OpenAI GPT-4** - dobry wybór ze względu na zaawansowane możliwości rozumienia kontekstu
- **Anthropic Claude** - dobra alternatywa, szczególnie dla długich kontekstów
- Lokalnie: **Bielik** - jak sugerowano w zadaniu, ma dobrą wiedzę o Polsce
- Alternatywnie: **Llama 3** lub **Mistral** - dobre modele open-source

### Biblioteki pomocnicze

- **requests** - do pobierania plików i komunikacji z API
- **pydub** lub **librosa** - do przetwarzania plików audio (jeśli potrzebne)
- **json** - do formatowania odpowiedzi w JSON
- **langchain** lub **llama_index** - opcjonalnie, do łatwiejszego zarządzania kontekstem i promptami

### Narzędzia do zarządzania projektem

- **Jupyter Notebook** - do eksploracyjnej analizy danych i testowania różnych podejść
- **Git** - do kontroli wersji kodu

## Uwagi i rekomendacje

1. **Transkrypcja**: Warto rozważyć użycie modelu Whisper w wersji "large" dla najlepszej jakości transkrypcji, szczególnie dla nagrania Rafała, które może być chaotyczne.

2. **Prompt Engineering**: Kluczowe będzie odpowiednie sformułowanie promptu. Warto zastosować technikę "myślenia na głos" (Chain of Thought), aby model krok po kroku analizował informacje i dochodził do wniosków.

3. **Weryfikacja**: Dobrą praktyką będzie weryfikacja odpowiedzi przez porównanie wyników z kilku różnych modeli lub podejść.

4. **Lokalne vs. Chmurowe**: Zadanie sugeruje eksperymentowanie z lokalnymi modelami, co może być dobrym ćwiczeniem, ale dla najlepszych wyników warto rozważyć modele chmurowe (OpenAI, Anthropic).

5. **Język promptu**: Warto przetestować zarówno prompty po polsku, jak i po angielsku, aby sprawdzić, który podejście daje lepsze rezultaty.
