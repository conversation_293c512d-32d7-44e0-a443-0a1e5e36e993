# Task: Extract Street Name from Witness Testimonies

## Objective:
Your task is to determine the name of the street where the specific institute of the university, where Professor <PERSON><PERSON><PERSON> lectures, is located. The necessary information can be found in the audio recordings of witness testimonies.

## Steps to Follow:

1. **Download and Extract Audio Files:**
   - Download the archive `przesluchania.zip` from the provided URL.
   - Extract the M4A audio files from the archive.

2. **Transcribe Audio Recordings:**
   - Use a speech-to-text model (e.g., OpenAI Whisper) to transcribe each audio file.
   - Store the transcriptions in a format that is easy to process (e.g., text files or a database).

3. **Combine Transcriptions:**
   - Merge all transcriptions into a single text document. This document will serve as the context for the prompt.

4. **Formulate the Prompt for LLM:**
   - Prepare a prompt for the Large Language Model (LLM) that includes the following elements:
     - A clear instruction that the model needs to determine the street name where the specific institute of the university, where Professor <PERSON><PERSON><PERSON> lectures, is located.
     - The entire text from the transcriptions as context.
     - A request for the model to analyze the transcriptions step by step and draw conclusions.
     - An instruction for the model to use its knowledge about Polish universities to identify the street name.

5. **Submit the Result:**
   - Format the street name as a JSON payload:
     ```json
     {
       "task": "mp3",
       "apikey": "YOUR_API_KEY",
       "answer": "Street Name"
     }
     ```
   - Replace `YOUR_API_KEY` with your actual API key.
   - Ensure the data is encoded in UTF-8.
   - Send the JSON payload to the specified API endpoint.

## Important Considerations:
- Focus on crafting a clear and precise prompt. The model should understand that it needs to analyze the transcriptions and use its internal knowledge about universities.
- Be aware that one of the recordings is more chaotic. The model may have difficulty interpreting it. Some recordings may be misleading—include this information in the prompt.
- Remember that the model does not know anything about Professor Maj. All knowledge must be extracted from the transcriptions and the model's knowledge about universities in Poland.

## Tools and Technologies:
- **Programming Language:** Python
- **Audio Processing:** FFmpeg
- **Speech-to-Text:** OpenAI Whisper or Mozilla DeepSpeech
- **Text Processing:** NLTK or spaCy
- **Large Language Model:** OpenAI GPT-4 or GPT-3.5, Hugging Face Transformers
- **API Interaction:** Requests library
- **Data Handling:** Pandas, JSON
- **Development Tools:** Jupyter Notebook, Git
- **Local Model (Optional):** Bielik

By following these steps and using the suggested tools, you will be able to efficiently process the audio recordings, extract the necessary information, and submit the result to the central system.

Good luck!
