# Analiza Zadania: Ustalenie Lokalizacji Instytutu Profesora Maja

## 1. Co mam tutaj zbudować? (Cel Zadania)

Głównym celem zadania jest **ustalenie dokładnej nazwy ulicy, na której znajduje się konkretny instytut (lub wydział/katedra) polskiej uczelni, gdzie wykłada profesor Andrzej Maj**. Kluczowe jest znalezienie adresu *konkretnego miejsca pracy* profesora, a nie ogólnego adresu głównej siedziby uczelni, chyba że instytut mieści się właśnie tam.

Informacje niezbędne do rozwiązania tego zadania są ukryte w dostarczonych nagraniach audio z przesłuchań świadków. Po znalezieniu poprawnej nazwy ulicy, odpowiedź musi zostać wysłana w formacie JSON na wskazany adres API, używając dedykowanego klucza API. Nazwa taska to `mp3`.

## 2. W jaki sposób mam to zrealizować? (Kroki do Wykonania)

Zadanie sugeruje następujący, wieloetapowy proces:

1. **Pobranie i Przygotowanie Danych:**
   Pobrać archiwum `przesluchania.zip` z podanego linku.
   Rozpakować archiwum, które zawiera pliki audio w formacie M4A.

2. **Transkrypcja Nagrań Audio:**
   Każdy plik audio (M4A) musi zostać przekształcony na formę tekstową.
   Zadanie rekomenduje użycie modelu **Whisper od OpenAI**, ale dopuszcza również inne modele ASR (Automatic Speech Recognition).

3. **Zbudowanie Kontekstu dla LLM:**
   Wszystkie uzyskane transkrypcje należy połączyć w jeden spójny blok tekstu.
   Ten połączony tekst posłuży jako kontekst, który zostanie przekazany do Wielkiego Modelu Językowego (LLM).

4. **Sformułowanie Odpowiedniego Promptu dla LLM:**
   Należy stworzyć precyzyjny i efektywny prompt (polecenie/pytanie) dla LLM. Prompt powinien:
    * Jasno definiować zadanie: znalezienie ulicy konkretnego instytutu profesora Andrzeja Maja.
    * Zawierać cały połączony tekst transkrypcji jako kontekst.
    * Zachęcić model do "myślenia na głos" (tzw. chain-of-thought), aby umożliwić śledzenie jego procesu rozumowania i ewentualne korekty promptu.
    * Poinstruować model, aby wykorzystał swoją wewnętrzną wiedzę na temat uczelni w Polsce do ustalenia adresu instytutu (po tym, jak zidentyfikuje uczelnię i instytut na podstawie transkrypcji).
    * Zadanie sugeruje rozważenie sformułowania promptu w języku polskim, co może być korzystne, biorąc pod uwagę polskojęzyczne dane i nazwy własne.
    * Wskazówki sugerują, aby w prompcie uwzględnić, że jedno z nagrań (Rafała) jest chaotyczne, a inne mogą wprowadzać w błąd.

5. **Wysłanie Odpowiedzi:**
   Po uzyskaniu nazwy ulicy od LLM, należy ją wysłać na adres `https://c3ntrala.ag3nts.org/report`.
   Odpowiedź musi być w formacie JSON, zawierać klucz API i być zakodowana w UTF-8. Nazwa taska to `mp3`.

## 3. Propozycja Optymalnego Planu Działania

1. **Konfiguracja Środowiska Projektowego:**
   Utworzenie dedykowanego folderu dla projektu.
   Zainicjowanie wirtualnego środowiska Python (np. `venv` lub `conda`).
   Instalacja niezbędnych bibliotek (zgodnie ze stackiem technologicznym).

2. **Pobieranie i Rozpakowywanie Danych:**
   Implementacja skryptu w Pythonie do pobrania pliku ZIP z podanego URL.
   Dodanie funkcji do rozpakowania archiwum do wyznaczonego podkatalogu (np. `audio_files`).

3. **Implementacja Modułu Transkrypcji Audio:**
   Wybór i konfiguracja narzędzia ASR (np. lokalny Whisper, API Whisper, inny model).
   Napisanie skryptu, który iteruje po plikach M4A w katalogu `audio_files` i dokonuje ich transkrypcji.
   Zapisywanie transkrypcji (np. do plików tekstowych lub przechowywanie w strukturze danych w pamięci).

4. **Agregacja Transkrypcji:**
   Stworzenie fragmentu kodu, który łączy wszystkie indywidualne transkrypcje w jeden ciąg tekstowy.

5. **Projektowanie i Testowanie Promptu dla LLM:**
   Rozpoczęcie od bazowej wersji promptu, uwzględniając wszystkie wytyczne z opisu zadania.
   Iteracyjne testowanie promptu z zagregowanymi transkrypcjami, używając wybranego LLM.
   Analiza odpowiedzi modelu, szczególnie jego "myślenia krok po kroku", w celu dostrojenia i optymalizacji promptu.
   Eksperymentowanie z językiem promptu (polski vs. angielski), jeśli model na to pozwala i jest to uzasadnione specyfiką danych.

6. **Ekstrakcja Finalnej Odpowiedzi z LLM:**
   Po uzyskaniu satysfakcjonującej odpowiedzi od LLM, implementacja logiki do precyzyjnego wyodrębnienia samej nazwy ulicy.

7. **Implementacja Modułu Wysyłania Rozwiązania:**
   Przygotowanie funkcji wysyłającej dane w formacie JSON (zawierające klucz API i uzyskaną nazwę ulicy) na wskazany endpoint API.
   Obsługa odpowiedzi serwera i ewentualnych błędów.

## 4. Propozycja Stacku Technologicznego

***Język Programowania:** **Python** (ze względu na wszechstronność i bogactwo bibliotek do AI/ML, przetwarzania danych i web).

* **Pobieranie Plików:** Biblioteka `requests` (Python).
* **Obsługa Archiwów ZIP:** Wbudowany moduł `zipfile` (Python).

* **Transkrypcja Audio (ASR):**
  **OpenAI Whisper:**
  * Przez API OpenAI (wymaga klucza API, płatne).
  * Lokalnie: biblioteka `openai-whisper` (Python) – preferowane dla większej kontroli i potencjalnie niższych kosztów, wymaga odpowiedniego sprzętu (GPU rekomendowane).
  **Alternatywy:** Modele z Hugging Face Transformers (np. Wav2Vec2), potencjalnie modele lepiej dostosowane do języka polskiego (np. wspomniany w zadaniu "Bielik", jeśli jest dostępny i odpowiedni).

* **Model Językowy (LLM):**
  **Modele OpenAI (GPT-3.5-turbo, GPT-4, GPT-4o):** Dostępne przez API, znane z wysokiej kompetencji. GPT-4/4o zazwyczaj oferują lepszą jakość, ale są droższe.
  **Modele Google Gemini:** Dostępne przez API, konkurencyjne dla modeli OpenAI.
  **Lokalne LLM:** (np. przez `ollama`, `LM Studio`, `llama.cpp` z modelami jak Llama, Mixtral, czy polskojęzyczne jak "Bielik"). Wymagają odpowiednich zasobów sprzętowych (dużo RAM, mocny CPU/GPU).
* **Interakcja z API (LLM i wysyłanie odpowiedzi):** Biblioteka `requests` (Python).

* **Zarządzanie Zmiennymi Środowiskowymi (np. klucze API):** Biblioteka `python-dotenv` (Python) do przechowywania wrażliwych danych w pliku `.env`.

## 5. Dodatkowe Uwagi i Rekomendacje

***Priorytet dla Języka Polskiego:** Biorąc pod uwagę, że materiał źródłowy (nagrania) jest prawdopodobnie w języku polskim, a zadanie dotyczy polskich realiów (uczelnie, lokalizacje), użycie modeli ASR i LLM, które dobrze radzą sobie z językiem polskim, oraz formułowanie promptu po polsku, jest wysoce zalecane dla uzyskania dokładnych wyników.

* **Iteracyjne Podejście:** Należy być przygotowanym na iteracyjny proces, zwłaszcza przy tworzeniu i dostrajaniu promptu dla LLM. Rzadko udaje się uzyskać idealne rozwiązanie za pierwszym podejściem. Analiza "myślenia krok po kroku" modelu będzie tu kluczowa.
* **Obsługa Niepewności i Sprzeczności:** W prompcie dla LLM warto wyraźnie zaznaczyć, że niektóre informacje w transkrypcjach mogą być mniej wiarygodne, chaotyczne (jak w przypadku nagrania Rafała) lub nawet sprzeczne. Model powinien być poinstruowany, jak sobie z tym radzić lub jak to zasygnalizować.

* **Struktura Projektu:** Zaleca się utrzymanie przejrzystej struktury katalogów i plików w projekcie, co ułatwi zarządzanie kodem i danymi.

* **Logowanie:** Warto dodać logowanie kluczowych kroków procesu (np. pobieranie danych, wyniki transkrypcji, zapytania do LLM, odpowiedzi LLM), co ułatwi debugowanie i monitorowanie działania agenta.
