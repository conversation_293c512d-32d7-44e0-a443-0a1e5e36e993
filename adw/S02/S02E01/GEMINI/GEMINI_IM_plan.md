# Plan Implementacji Agenta SFA: Ustalenie Lokalizacji Instytutu (S02E01)

## 1. Wprowadzenie

Celem tego dokumentu jest przedstawienie szczegółowego planu implementacji agenta typu Single File Agent (SFA) dla zadania S02E01. Agent będzie odpowiedzialny za:

1. Transkrypcję plików audio z przesłuchań świadków przy użyciu AssemblyAI, z uwzględnieniem języka polskiego.
2. Buforowanie (cachowanie) transkrypcji w lokalnym pliku JSON, aby unikać powtarzania operacji.
3. Przygotowanie zagregowanego kontekstu z transkrypcji.
4. Wykorzystanie Wielkiego Modelu Językowego (LLM) do analizy kontekstu i zidentyfikowania nazwy ulicy, przy której znajduje się instytut profesora Andrzeja Maja, na podstawie systemowego promptu z pliku `GEMINI_sys_prompt.txt`.
5. Wysłanie uzyskanej nazwy ulicy do centralnego API.
6. Sprawdzenie odpowiedzi serwera pod kątem obecności flagi.

Implementacja będzie bazować na dostarczonym schemacie `sfa_bbs.py` oraz ustaleniach z pliku `GEMINI_analiza.md`.

## 2. Wymagania Wstępne i Konfiguracja

### 2.1. Środowisko

* Python 3.8+
* Wirtualne środowisko (zalecane, np. `venv`)

### 2.2. Biblioteki

Należy zainstalować następujące biblioteki Python:

```bash
pip install requests python-dotenv openai assemblyai
```

### 2.3. Zmienne Środowiskowe

W pliku `.env` w głównym katalogu projektu (lub tam, skąd uruchamiany będzie skrypt) muszą znaleźć się następujące zmienne:

```env
AIDEVS_API_KEY="Twój_klucz_API_do_AI_Devs"
LLM_API_KEY="Twój_klucz_API_do_LLM_np_Kluster_lub_OpenAI" # Może być też KLUSTER_API_KEY lub OPENAI_API_KEY
ASSEMBLY_API_KEY="Twój_klucz_API_do_AssemblyAI"

# Opcjonalne, jeśli używasz innego endpointu LLM niż domyślny OpenAI/Kluster
# LLM_BASE_URL="https://api.example.com/v1"
# LLM_MODEL="nazwa_modelu_llm"
```

### 2.4. Wymagane Pliki Lokalne

* **Skrypt SFA:** Główny plik Pythona z logiką agenta (np. `s02e01_agent.py`).
* **System Prompt LLM:** Plik `GEMINI_sys_prompt.txt` musi znajdować się w tym samym katalogu co skrypt SFA. Jego treść została zdefiniowana wcześniej.
* **Pliki Audio:** Pliki audio do transkrypcji muszą znajdować się w katalogu: `/Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S02/S02E01/ZZZ_TRIALS`. Ścieżka ta będzie skonfigurowana w skrypcie.

### 2.5. Pliki Generowane przez Agenta

* **Cache Transkrypcji:** Plik `transcriptions.json` będzie tworzony/aktualizowany w tym samym katalogu co skrypt SFA. Będzie przechowywał zbuforowane transkrypcje.

## 3. Struktura Skryptu SFA

Skrypt będzie pojedynczym plikiem Python, zorganizowanym w następujący sposób, bazując na `sfa_bbs.py`:

1. **Sekcja Importów:** Standardowe i zewnętrzne biblioteki.
2. **Sekcja Konfiguracji:**
    * Ładowanie zmiennych środowiskowych (`load_dotenv()`).
    * Definicja stałych: `TASK_NAME = "mp3"`, `API_BASE_URL`, `REPORT_ENDPOINT`, `API_KEY`, `ASSEMBLY_API_KEY`.
    * Ustawienia żądań HTTP (`REQUEST_TIMEOUT`, `RETRY_ATTEMPTS`, `RETRY_DELAY`).
    * Ustawienia LLM (`LLM_MODEL`, `LLM_API_KEY`, `LLM_BASE_URL`, `LLM_MAX_TOKENS`, `LLM_TEMPERATURE`, `LLM_TOP_P`).
    * Ścieżki do plików: `SYSTEM_PROMPT_PATH`, `AUDIO_DIR`, `TRANSCRIPTIONS_FILE`.
3. **Sekcja Konfiguracji Logowania:** Standardowa konfiguracja `logging`.
4. **Sekcja Funkcji Pomocniczych (adaptacja z `sfa_bbs.py`):**
    * `validate_configuration()`: Rozszerzona o walidację `ASSEMBLY_API_KEY` oraz istnienia pliku `SYSTEM_PROMPT_PATH`.
    * `load_system_prompt(system_prompt_path: str) -> str`: Wczytuje system prompt z podanej ścieżki.
    * `call_llm(prompt: str, system_prompt_path: str, attempt: int = 1) -> Optional[str]`: Wywołuje LLM.
    * `send_report(url: str, api_key: str, task_name: str, answer: Any, attempt: int = 1) -> Optional[Dict]`: Wysyła raport.
    * `check_for_flag(response: Any) -> Optional[str]`: Sprawdza flagę w odpowiedzi serwera.
5. **Sekcja Funkcji Specyficznych dla Zadania:**
    * `load_transcriptions(file_path: str) -> List[Dict]`: Wczytuje zbuforowane transkrypcje z pliku JSON.
    * `save_transcriptions(file_path: str, transcriptions: List[Dict])`: Zapisuje transkrypcje do pliku JSON.
    * `transcribe_audio_files(audio_dir: str, transcriptions_cache_file: str, aai_api_key: str) -> List[Dict]`: Główna funkcja do transkrypcji plików audio z użyciem AssemblyAI i mechanizmem cachowania.
    * `prepare_llm_context(transcriptions: List[Dict]) -> str`: Formatuje listę transkrypcji w pojedynczy ciąg tekstowy dla LLM.
6. **Główna Funkcja `main()`:** Orchestruje cały przepływ pracy agenta.
7. **Punkt Wejścia Skryptu:** `if __name__ == "__main__": main()`.

## 4. Szczegółowy Opis Funkcji Specyficznych dla Zadania

### 4.1. `load_transcriptions(file_path: str) -> List[Dict]`

* Sprawdza, czy plik pod `file_path` istnieje.
* Jeśli nie, loguje informację i zwraca pustą listę.
* Jeśli istnieje, próbuje wczytać i zdeserializować zawartość JSON.
* W przypadku błędu (np. niepoprawny JSON), loguje błąd i zwraca pustą listę.
* Zwraca listę słowników (transkrypcji).

### 4.2. `save_transcriptions(file_path: str, transcriptions: List[Dict])`

* Otwiera plik pod `file_path` w trybie zapisu (`'w'`).
* Serializuje listę `transcriptions` do formatu JSON (z wcięciami dla czytelności i `ensure_ascii=False`).
* Zapisuje JSON do pliku.
* Loguje informację o zapisie.
* Obsługuje ewentualne błędy zapisu.

### 4.3. `transcribe_audio_files(audio_dir: str, transcriptions_cache_file: str, aai_api_key: str) -> List[Dict]`

* **Inicjalizacja:**
  * Ustawia klucz API dla AssemblyAI: `aai.settings.api_key = aai_api_key`.
  * Tworzy konfigurację transkrypcji dla języka polskiego: `config = aai.TranscriptionConfig(language_code="pl")`. Można rozważyć dodanie `speech_model=aai.SpeechModel.best` dla potencjalnie lepszej jakości, jeśli budżet i czas na to pozwalają.
  * Tworzy obiekt `transcriber = aai.Transcriber(config=config)`.
* **Cachowanie:**
  * Wczytuje istniejące transkrypcje za pomocą `load_transcriptions(transcriptions_cache_file)`.
  * Tworzy mapę (słownik) z istniejących transkrypcji dla szybkiego dostępu: `cached_map = {t['title']: t['content'] for t in existing_transcriptions}`.
* **Iteracja po Plikach Audio:**
  * Pobiera listę plików z `audio_dir` (np. za pomocą `os.listdir()`). Filtruje tylko pliki z odpowiednimi rozszerzeniami (np. `.m4a`, `.mp3`, `.wav`).
  * Sortuje listę plików dla spójności przetwarzania.
  * Dla każdego pliku audio:
    * Pobiera nazwę pliku.
    * Sprawdza, czy transkrypcja dla `file_name` istnieje w `cached_map`.
      * Jeśli tak: loguje użycie wersji z cache i dodaje do wynikowej listy transkrypcji.
      * Jeśli nie:
        * Loguje rozpoczęcie nowej transkrypcji.
        * Konstruuje pełną ścieżkę do pliku audio.
        * Wywołuje `transcript = transcriber.transcribe(audio_path)`.
        * Sprawdza `transcript.status`:
          * Jeśli `aai.TranscriptStatus.error`: loguje błąd (`transcript.error`) i pomija ten plik.
          * Jeśli `aai.TranscriptStatus.completed` (lub inny status sukcesu): loguje sukces, pobiera tekst (`transcript.text`), tworzy obiekt `{"title": file_name, "content": transcript.text}` i dodaje do wynikowej listy transkrypcji. Zaznacza, że dokonano nowej transkrypcji.
        * Obsługuje ewentualne wyjątki podczas wywołania API AssemblyAI.
* **Zapis Cache:**
  * Jeśli dokonano przynajmniej jednej nowej transkrypcji (lub jeśli plik cache nie istniał), zapisuje całą (zaktualizowaną) listę transkrypcji do `transcriptions_cache_file` za pomocą `save_transcriptions()`.
* Zwraca pełną listę transkrypcji (połączone z cache i nowo utworzone).

### 4.4. `prepare_llm_context(transcriptions: List[Dict]) -> str`

* Inicjalizuje pustą listę `context_parts`.
* Iteruje przez dostarczoną listę `transcriptions` (każdy element to słownik `{"title": "...", "content": "..."}`).
* Dla każdej transkrypcji, formatuje ją jako ciąg znaków, np.:

    ```
    --- Transkrypcja z pliku: {title} ---
    {content}

    ```

* Dodaje sformatowany ciąg do listy `context_parts`.
* Łączy wszystkie elementy z `context_parts` w jeden długi ciąg znaków, używając `"".join(context_parts)`.
* Usuwa ewentualne białe znaki z początku/końca wynikowego kontekstu.
* Loguje informację o przygotowaniu kontekstu (np. jego długość lub początkowy fragment).
* Zwraca przygotowany kontekst.

## 5. Główna Funkcja `main()` - Przepływ Pracy Agenta

```python
def main():
    logger.info(f"--- Starting {TASK_NAME} agent ---")

    # 1. Walidacja konfiguracji
    if not validate_configuration():
        logger.critical("Configuration validation failed. Exiting.")
        return

    try:
        # 2. Transkrypcja plików audio (z cachowaniem)
        # AUDIO_DIR i TRANSCRIPTIONS_FILE będą stałymi zdefiniowanymi w sekcji konfiguracji
        transcriptions_list = transcribe_audio_files(AUDIO_DIR, TRANSCRIPTIONS_FILE, ASSEMBLY_API_KEY)
        if not transcriptions_list:
            logger.critical("No transcriptions available (failed to transcribe or load from cache). Exiting.")
            return
        logger.info(f"Successfully obtained/loaded {len(transcriptions_list)} transcriptions.")

        # 3. Przygotowanie kontekstu dla LLM
        llm_user_prompt_context = prepare_llm_context(transcriptions_list)
        if not llm_user_prompt_context.strip():
             logger.critical("Prepared LLM context is empty. Exiting.")
             return
        logger.info("LLM context prepared.")

        # 4. Wywołanie LLM
        # SYSTEM_PROMPT_PATH będzie stałą
        # Prompt użytkownika to zagregowany kontekst z transkrypcji
        llm_answer = call_llm(prompt=llm_user_prompt_context, system_prompt_path=SYSTEM_PROMPT_PATH)
        if not llm_answer:
            logger.critical("Failed to get answer from LLM. Exiting.")
            return
        logger.info(f"Received answer from LLM (potential street name): '{llm_answer}'")

        # 5. Przygotowanie finalnej odpowiedzi (bezpośrednio z LLM, zgodnie z promptem systemowym)
        final_answer_for_report = llm_answer.strip() # LLM powinien zwrócić tylko nazwę ulicy

        # 6. Wysłanie raportu
        report_response = send_report(REPORT_ENDPOINT, API_KEY, TASK_NAME, final_answer_for_report)
        if not report_response:
            logger.critical("Failed to send report. Exiting.")
            return
        logger.info("Report sent successfully.")

        # 7. Sprawdzenie flagi w odpowiedzi serwera
        flag = check_for_flag(report_response)

        # 8. Wyświetlenie wyników
        if flag:
            logger.info(f"SUCCESS! Flag found: {flag}")
            # check_for_flag już drukuje flagę
        else:
            logger.info("Task completed, but no flag found in the server response.")
            print(f"\n{'='*50}\nTASK COMPLETED - NO FLAG FOUND\nFinal Answer Sent: '{final_answer_for_report}'\nServer Response: {json.dumps(report_response, indent=2)}\n{'='*50}\n")

    except Exception as e:
        logger.critical(f"An unexpected error occurred in the main workflow: {e}", exc_info=True)

    logger.info(f"--- {TASK_NAME} agent finished ---")
```

## 6. Stałe i Konfiguracja w Skrypcie (Przykład)

```python
# --- APPLICATION SETTINGS ---
TASK_NAME = "mp3"
API_BASE_URL = os.getenv("API_BASE_URL", "https://c3ntrala.ag3nts.org")
REPORT_ENDPOINT = f"{API_BASE_URL}/report"
API_KEY = os.getenv("AIDEVS_API_KEY")
ASSEMBLY_API_KEY = os.getenv("ASSEMBLY_API_KEY")

# --- LLM SETTINGS ---
LLM_MODEL = os.getenv("LLM_MODEL", "gpt-4-turbo-preview") # lub inny model
LLM_API_KEY = os.getenv("LLM_API_KEY", os.getenv("OPENAI_API_KEY")) # lub KLUSTER_API_KEY
LLM_BASE_URL = os.getenv("LLM_BASE_URL") # np. "https://api.openai.com/v1" lub "https://api.kluster.ai/v1"

# --- TASK-SPECIFIC PATHS ---
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
SYSTEM_PROMPT_PATH = os.path.join(BASE_DIR, "GEMINI_sys_prompt.txt")
AUDIO_DIR = "/Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S02/S02E01/ZZZ_TRIALS" # Zgodnie z Twoim wskazaniem
TRANSCRIPTIONS_FILE = os.path.join(BASE_DIR, "transcriptions.json")
```

## 7. Obsługa Błędów i Logowanie

* Logowanie będzie realizowane zgodnie ze standardami z `sfa_bbs.py` (poziom INFO, format, handler).
* Każda kluczowa operacja (API call, operacja plikowa) będzie opakowana w bloki `try-except` w celu przechwytywania i logowania błędów.
* Funkcja `validate_configuration()` zapewni, że wszystkie niezbędne klucze API i ścieżki są dostępne przed rozpoczęciem głównej logiki.
* Błędy transkrypcji pojedynczych plików nie powinny przerywać całego procesu; agent powinien próbować przetworzyć pozostałe pliki.
* Krytyczne błędy (np. brak kluczy API, niemożność połączenia z LLM po kilku próbach) zakończą działanie agenta z odpowiednim komunikatem.

## 8. Finalny Produkt

Wynikiem będzie pojedynczy skrypt Python, który po uruchomieniu wykona wszystkie opisane kroki, a na końcu wyświetli znalezioną flagę lub status operacji.

Ten plan powinien stanowić solidną podstawę do implementacji agenta.
