"""
Single File Agent (SFA) for S02E01 - Ustalenie Lokalizacji Instytutu

This agent transcribes audio files, uses an LLM to determine a street name
based on the transcriptions, and reports the answer to retrieve a flag.
"""

# --- IMPORTS ---
import os
import re
import json
import logging
import time
import requests
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv
from openai import OpenAI, APIError, APIConnectionError, RateLimitError
import assemblyai as aai

# --- CONFIGURATION ---
# Load environment variables
load_dotenv()

# --- APPLICATION SETTINGS ---
TASK_NAME = "mp3"
API_BASE_URL = os.getenv("API_BASE_URL", "https://c3ntrala.ag3nts.org")
REPORT_ENDPOINT = f"{API_BASE_URL}/report"
API_KEY = os.getenv("AIDEVS_API_KEY")
ASSEMBLY_API_KEY = os.getenv("ASSEMBLY_API_KEY")

# Request settings
REQUEST_TIMEOUT = 30  # Increased timeout for potentially larger AssemblyAI uploads/downloads
RETRY_ATTEMPTS = 0
RETRY_DELAY = 5  # seconds

# --- LLM SETTINGS ---
LLM_MODEL = os.getenv("KLUSTER_LLM_MODEL", os.getenv("OPENAI_LLM_MODEL"))
LLM_API_KEY = os.getenv("KLUSTER_API_KEY", os.getenv("OPENAI_API_KEY"))
LLM_BASE_URL = os.getenv("KLUSTER_BASE_URL", os.getenv("OPENAI_BASE_URL"))
LLM_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", "4000"))
LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.3"))
LLM_TOP_P = float(os.getenv("LLM_TOP_P", "1.0"))

# --- TASK-SPECIFIC PATHS ---
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
SYSTEM_PROMPT_PATH = os.path.join(BASE_DIR, "GEMINI_sys_prompt.txt")
AUDIO_DIR = "/Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S02/S02E01/ZZZ_TRIALS"
TRANSCRIPTIONS_FILE = os.path.join(BASE_DIR, "transcriptions.json")

# Default system prompt (used if file not found)
DEFAULT_SYSTEM_PROMPT = """
You are an AI assistant. Your task is to find a specific street name based on provided text.
Respond ONLY with the street name.
"""

# --- REGEX PATTERNS ---
FLAG_REGEX = r"FLG[a-zA-Z0-9_-]+"
FLAG_REGEX_CURLY = r"\{\{FLG:[a-zA-Z0-9_-]+\}\}"

# --- LOGGING CONFIGURATION ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# --- HELPER FUNCTIONS (adapted from sfa_bbs.py) ---

def validate_configuration() -> bool:
    """
    Validates that all required configuration variables are set.
    """
    required_env_vars = {
        "AIDEVS_API_KEY": "API key for Central API",
        "ASSEMBLY_API_KEY": "API key for AssemblyAI",
    }
    # LLM_API_KEY is also crucial, check if it's derived from OPENAI_API_KEY or KLUSTER_API_KEY
    if not LLM_API_KEY:
        required_env_vars["LLM_API_KEY (or OPENAI_API_KEY/KLUSTER_API_KEY)"] = "API key for LLM"

    missing_vars = []

    for var_name, description in required_env_vars.items():
        # Special handling for LLM_API_KEY as it's derived
        if var_name.startswith("LLM_API_KEY") and LLM_API_KEY:
            continue
        if not globals().get(var_name.split(" ")[0]) and not os.getenv(var_name.split(" ")[0]): # Check global then env
             missing_vars.append(f"{var_name} ({description})")

    if not os.path.exists(SYSTEM_PROMPT_PATH):
        logger.warning(f"System prompt file not found at {SYSTEM_PROMPT_PATH}. Will use default or fail if LLM call needs it specifically.")
        # This might not be critical if default prompt is acceptable or LLM call handles it.

    if not os.path.isdir(AUDIO_DIR):
        logger.critical(f"Audio directory not found at {AUDIO_DIR}")
        missing_vars.append(f"AUDIO_DIR ({AUDIO_DIR})")

    if missing_vars:
        logger.critical(f"Missing required environment variables or paths: {', '.join(missing_vars)}")
        return False

    logger.info("Configuration validated successfully.")
    return True

def load_system_prompt(system_prompt_path: str) -> str:
    """
    Loads system prompt from file.
    """
    try:
        with open(system_prompt_path, 'r', encoding='utf-8') as file:
            system_prompt = file.read()
        logger.info(f"Successfully loaded system prompt from: {system_prompt_path}")
        return system_prompt
    except FileNotFoundError:
        logger.error(f"System prompt file not found at {system_prompt_path}. Using default system prompt.")
        return DEFAULT_SYSTEM_PROMPT
    except Exception as e:
        logger.error(f"Error loading system prompt from {system_prompt_path}: {e}. Using default system prompt.")
        return DEFAULT_SYSTEM_PROMPT

def call_llm(prompt: str, system_prompt_content: str, attempt: int = 1) -> Optional[str]:
    """
    Calls the LLM with the given prompt and system prompt content.
    """
    logger.info(f"Calling LLM (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    if not LLM_API_KEY:
        logger.critical("LLM_API_KEY is not set. Cannot call LLM.")
        return None

    try:
        client_params = {"api_key": LLM_API_KEY}
        if LLM_BASE_URL: # Only add base_url if it's explicitly set
            client_params["base_url"] = LLM_BASE_URL
            logger.info(f"Using LLM base URL: {LLM_BASE_URL}")
        else:
            logger.info("Using default OpenAI base URL.")


        client = OpenAI(**client_params)

        logger.info(f"Using model: {LLM_MODEL}")
        logger.debug(f"System Prompt for LLM: \n{system_prompt_content[:500]}...") # Log beginning of system prompt
        logger.debug(f"User Prompt for LLM (context): \n{prompt[:500]}...") # Log beginning of user prompt

        completion = client.chat.completions.create(
            model=LLM_MODEL,
            messages=[
                {"role": "system", "content": system_prompt_content},
                {"role": "user", "content": prompt}
            ],
            temperature=LLM_TEMPERATURE,
            max_tokens=LLM_MAX_TOKENS, # Corrected parameter name
            top_p=LLM_TOP_P
        )

        response_content = completion.choices[0].message.content
        if response_content is None:
            logger.warning("LLM returned None content.")
            return None
        response = response_content.strip()
        logger.info("LLM call successful.")
        logger.debug(f"LLM Raw Response: {response}")
        return response

    except (APIError, APIConnectionError, RateLimitError) as e:
        logger.error(f"LLM API error: {e}")
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying LLM call in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return call_llm(prompt, system_prompt_content, attempt + 1)
        else:
            logger.error("Maximum LLM retry attempts reached.")
            return None
    except Exception as e:
        logger.error(f"Unexpected error during LLM call: {e}", exc_info=True)
        return None

def send_report(url: str, api_key: str, task_name: str, answer: Any, attempt: int = 1) -> Optional[Dict]:
    """
    Sends the final report/answer to the specified endpoint.
    """
    logger.info(f"Sending report to {url} (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    clean_answer = answer.strip() if isinstance(answer, str) else answer
    
    payload = {
        "task": task_name,
        "apikey": api_key,
        "answer": clean_answer
    }
    headers = {"Content-Type": "application/json; charset=utf-8"}

    logger.info(f"Sending payload: {json.dumps(payload, ensure_ascii=False)}")

    try:
        response = requests.post(url, json=payload, headers=headers, timeout=REQUEST_TIMEOUT)
        logger.info(f"Response code: {response.status_code}")
        logger.info(f"Response content: {response.text}")
        response.raise_for_status()
        result = response.json()
        logger.info("Report sent successfully.")
        return result
    except requests.exceptions.RequestException as e:
        logger.error(f"Error sending report: {e}")
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"Server response: {e.response.text}")
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying report submission in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return send_report(url, api_key, task_name, answer, attempt + 1)
        else:
            logger.error("Maximum report submission retry attempts reached.")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response from report server: {e}")
        return None

def check_for_flag(response: Any) -> Optional[str]:
    """
    Checks if the response contains a flag.
    """
    response_str = str(response)
    if not response_str:
        return None

    match_curly = re.search(FLAG_REGEX_CURLY, response_str)
    if match_curly:
        flag = match_curly.group(0)
        logger.info(f"Flag found (curly format): {flag}")
        return flag

    match = re.search(FLAG_REGEX, response_str)
    if match:
        flag = match.group(0)
        logger.info(f"Flag found (standard format): {flag}")
        return flag

    if "flag" in response_str.lower() or "flg" in response_str.lower():
        logger.info(f"Text may contain a flag: {response_str}")
        # To avoid returning the whole server message if it just mentions "flag"
        # without the specific FLG pattern, we might want to be more restrictive here
        # For now, returning if "flag" or "FLG" is present.
        # Consider if this is too broad.
        return response_str # Potentially too broad, might return entire message

    logger.info("No flag found in response.")
    return None

# --- TASK-SPECIFIC FUNCTIONS ---

def load_transcriptions(file_path: str) -> List[Dict]:
    """Loads cached transcriptions from a JSON file."""
    if not os.path.exists(file_path):
        logger.info(f"Transcription cache file not found: {file_path}. Returning empty list.")
        return []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            transcriptions = json.load(f)
        logger.info(f"Loaded {len(transcriptions)} transcriptions from cache: {file_path}")
        return transcriptions
    except json.JSONDecodeError:
        logger.error(f"Error decoding JSON from {file_path}. Returning empty list.")
        return []
    except Exception as e:
        logger.error(f"Error loading transcriptions from {file_path}: {e}. Returning empty list.")
        return []

def save_transcriptions(file_path: str, transcriptions: List[Dict]):
    """Saves transcriptions to a JSON file."""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(transcriptions, f, ensure_ascii=False, indent=2)
        logger.info(f"Saved {len(transcriptions)} transcriptions to cache: {file_path}")
    except Exception as e:
        logger.error(f"Error saving transcriptions to {file_path}: {e}")

def transcribe_audio_files(audio_dir: str, transcriptions_cache_file: str, aai_api_key: str) -> List[Dict]:
    """
    Transcribes audio files from the specified directory using AssemblyAI,
    with caching mechanism.
    """
    logger.info("Starting audio transcription process...")
    aai.settings.api_key = aai_api_key
    
    # Using language_code="pl" as specified.
    # speech_model=aai.SpeechModel.best can be used for higher accuracy if needed.
    config = aai.TranscriptionConfig(language_code="pl")
    transcriber = aai.Transcriber(config=config)

    existing_transcriptions = load_transcriptions(transcriptions_cache_file)
    cached_map = {t['title']: t['content'] for t in existing_transcriptions}
    
    all_transcriptions_list = [] # This will hold all transcriptions, from cache or new
    new_transcriptions_made = False

    if not os.path.isdir(audio_dir):
        logger.error(f"Audio directory not found: {audio_dir}")
        return existing_transcriptions # Return what we have from cache if dir is missing

    audio_file_names = sorted([f for f in os.listdir(audio_dir) if os.path.isfile(os.path.join(audio_dir, f)) and (f.endswith(".m4a") or f.endswith(".mp3") or f.endswith(".wav"))])
    logger.info(f"Found {len(audio_file_names)} audio files in {audio_dir}")

    for file_name in audio_file_names:
        if file_name in cached_map:
            logger.info(f"Using cached transcription for: {file_name}")
            all_transcriptions_list.append({"title": file_name, "content": cached_map[file_name]})
        else:
            logger.info(f"Starting new transcription for: {file_name}")
            audio_path = os.path.join(audio_dir, file_name)
            try:
                transcript = transcriber.transcribe(audio_path)
                if transcript.status == aai.TranscriptStatus.error:
                    logger.error(f"Transcription failed for {file_name}: {transcript.error}")
                    # Optionally, add a placeholder or skip
                elif transcript.text:
                    logger.info(f"Successfully transcribed: {file_name}")
                    transcription_data = {"title": file_name, "content": transcript.text}
                    all_transcriptions_list.append(transcription_data)
                    new_transcriptions_made = True
                else:
                    logger.warning(f"Transcription for {file_name} completed but no text was returned.")
            except Exception as e:
                logger.error(f"Error during transcription of {file_name}: {e}", exc_info=True)

    if new_transcriptions_made:
        # Update the cache file with all transcriptions (old and new)
        # To do this correctly, we need to rebuild the list for saving
        # by ensuring all items in all_transcriptions_list are unique by title
        # and then save this comprehensive list.
        # A simpler approach for now: if new ones were made, save the current all_transcriptions_list
        # This assumes all_transcriptions_list is now the most up-to-date complete list.
        logger.info("New transcriptions were made, updating cache file.")
        save_transcriptions(transcriptions_cache_file, all_transcriptions_list)
    
    logger.info(f"Transcription process completed. Total transcriptions: {len(all_transcriptions_list)}")
    return all_transcriptions_list

def prepare_llm_context(transcriptions: List[Dict]) -> str:
    """Formats a list of transcriptions into a single string context for the LLM."""
    context_parts = []
    if not transcriptions:
        logger.warning("No transcriptions provided to prepare LLM context.")
        return ""

    for trans_item in transcriptions:
        title = trans_item.get("title", "N/A")
        content = trans_item.get("content", "")
        context_parts.append(f"--- Transkrypcja z pliku: {title} ---\n{content}\n\n")
    
    full_context = "".join(context_parts).strip()
    logger.info(f"Prepared LLM context. Length: {len(full_context)} characters.")
    logger.debug(f"LLM Context (first 500 chars): {full_context[:500]}")
    return full_context

# --- MAIN FUNCTION ---

def main():
    logger.info(f"--- Starting {TASK_NAME} agent ---")

    if not validate_configuration():
        logger.critical("Configuration validation failed. Exiting.")
        return

    try:
        # 1. Transcribe audio files (with caching)
        transcriptions_list = transcribe_audio_files(AUDIO_DIR, TRANSCRIPTIONS_FILE, ASSEMBLY_API_KEY)
        if not transcriptions_list:
            logger.critical("No transcriptions available. Exiting.")
            return

        # 2. Prepare context for LLM
        llm_user_prompt_context = prepare_llm_context(transcriptions_list)
        if not llm_user_prompt_context.strip():
             logger.critical("Prepared LLM context is empty. Exiting.")
             return

        # 3. Load system prompt for LLM
        system_prompt_content = load_system_prompt(SYSTEM_PROMPT_PATH)
        if not system_prompt_content.strip(): # Check if system prompt is empty after loading
            logger.critical("System prompt is empty. Exiting.")
            return

        # 4. Call LLM
        llm_answer = call_llm(prompt=llm_user_prompt_context, system_prompt_content=system_prompt_content)
        if not llm_answer:
            logger.critical("Failed to get answer from LLM. Exiting.")
            return
        
        # As per GEMINI_sys_prompt.txt, LLM might provide "chain of thought" before the final answer.
        # The prompt asks for "OSTATNIĄ I JEDYNĄ LINIĘ odpowiedzi podaj TYLKO I WYŁĄCZNIE NAZWĘ ULICY."
        # We should extract the last line as the potential street name.
        lines = llm_answer.strip().split('\n')
        final_answer_for_report = lines[-1].strip()
        logger.info(f"Extracted street name from LLM response: '{final_answer_for_report}' (Full LLM response had {len(lines)} lines)")


        # 5. Send report
        report_response = send_report(REPORT_ENDPOINT, API_KEY, TASK_NAME, final_answer_for_report)
        if not report_response:
            logger.critical("Failed to send report. Exiting.")
            return

        # 6. Check for flag in response
        flag = check_for_flag(report_response)

        # 7. Display results
        if flag:
            # check_for_flag already prints the flag if found in specific formats
            # but we can add a clear success message here
            logger.info(f"SUCCESS! Flag obtained: {flag}")
            print(f"\n{'='*50}\nFLAG: {flag}\n{'='*50}\n")
        else:
            logger.info("Task completed, but no specific FLG pattern found in the server response.")
            print(f"\n{'='*50}\nTASK COMPLETED - NO FLG PATTERN FOUND\nFinal Answer Sent: '{final_answer_for_report}'\nServer Response: {json.dumps(report_response, indent=2)}\n{'='*50}\n")

    except Exception as e:
        logger.critical(f"An unexpected error occurred in the main workflow: {e}", exc_info=True)

    logger.info(f"--- {TASK_NAME} agent finished ---")

# --- SCRIPT ENTRY POINT ---

if __name__ == "__main__":
    main()