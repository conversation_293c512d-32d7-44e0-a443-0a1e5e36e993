**Plan Implementacji SFA S02E01: Lokalizacja Instytutu**

**Nazwa pliku docelowego:** `adw/S02/S02E01/sfa_s02e01.py` (zamiast `sfa_bbs.py`, dostosujemy szablon)

**1. Konfiguracja i Ustawienia (`adw/S02/S02E01/sfa_s02e01.py`)**

* Sko<PERSON><PERSON><PERSON> `adw/sfa_bbs.py` do nowego pliku `adw/S02/S02E01/sfa_s02e01.py`.
* Zaktualizuj zmienną `TASK_NAME` na `"mp3"`.
* Dodaj nową sekcję konfiguracji dla transkrypcji:
  * Zmienna środowiskowa `ASSEMBLYAI_API_KEY`.
  * Konfiguracja ścieżek lokalnych:
    * `AUDIO_ZIP_URL`: URL do pliku `przesluchania.zip`.
    * `AUDIO_DIR`: Ścieżka do lokalnego folderu docelowego po rozpakowaniu (np. `adw/S02/S02E01/przesluchania`).
    * `TRANSCRIPTIONS_FILE`: Ścieżka do pliku JSON z transkrypcjami (np. `adw/S02/S02E01/transcriptions.json`).
* Zaktualizuj `SYSTEM_PROMPT_PATH` tak, aby wskazywał na `adw/S02/S02E01/CONTINUE/CONTINUE_sys_prompt.txt`.
* Zaktualizuj funkcję `validate_configuration()` o sprawdzenie, czy `ASSEMBLYAI_API_KEY` jest ustawiony. Dodaj import `assemblyai as aai`.

**2. Funkcja Pobierania i Rozpakowywania Audio (`adw/S02/S02E01/sfa_s02e01.py`)**

* Zaimplementuj nową funkcję `download_and_unzip(url: str, destination_dir: str)`.
  * Użyj bibliotek `requests` i `zipfile`.
  * Sprawdź, czy folder `destination_dir` istnieje; jeśli nie, utwórz go używając `os.makedirs`.
  * Pobierz plik ZIP z `url` przy użyciu `requests.get`.
  * Zapisz pobraną zawartość do tymczasowego pliku ZIP.
  * Otwórz tymczasowy plik ZIP używając `zipfile.ZipFile` i rozpakuj go do `destination_dir`.
  * Usuń tymczasowy plik ZIP.
  * Obsłuż błędy pobierania lub rozpakowywania.
  * Funkcja powinna logować postęp (pobieranie, rozpakowywanie, miejsce zapisu).

**3. Funkcje Zarządzania Transkrypcjami (`adw/S02/S02E01/sfa_s02e01.py`)**

* **`load_transcriptions(file_path: str) -> List[Dict]`**:
  * Wczytaj dane z pliku JSON pod `file_path`. Użyj `json.load`.
  * Obsłuż przypadki, gdy plik nie istnieje (`FileNotFoundError`) lub jest pusty/niepoprawny JSON (`json.JSONDecodeError`) – w tych przypadkach zwróć pustą listę `[]`.
  * Zwróć listę słowników.
* **`save_transcriptions(file_path: str, transcriptions: List[Dict])`**:
  * Zapisz listę słowników `transcriptions` do pliku JSON pod `file_path`.
  * Użyj `json.dump` z `indent=4` dla czytelności.
  * Upewnij się, że plik jest zapisywany w kodowaniu UTF-8 (`ensure_ascii=False`).
  * Obsłuż potencjalne błędy zapisu.
* **`transcribe_audio_files(audio_dir: str, transcriptions_file: str, aai_api_key: str) -> List[Dict]`**:
  * Ustaw klucz API AssemblyAI: `aai.settings.api_key = aai_api_key`.
  * Wczytaj istniejące transkrypcje za pomocą `load_transcriptions(transcriptions_file)`.
  * Utwórz słownik `existing_transcriptions_map = {t.get('title'): t.get('content', '') for t in existing_transcriptions if t and t.get('title')}` dla szybkiego sprawdzania, ignorując potencjalnie wadliwe wpisy i używając `.get()`.
  * Pobierz listę wszystkich plików `.m4a` (i innych audio, np. `.mp3` jeśli występują) z `audio_dir` używając `os.listdir` i filtrując rozszerzenia. Upewnij się, że lista plików jest posortowana dla powtarzalności.
  * Utwórz pustą listę `all_transcriptions`. Ta lista będzie zawierać wszystkie transkrypcje (istniejące i nowo utworzone).
  * Utwórz pustą listę `newly_transcribed_files` do śledzenia plików, które *właśnie* zostały przetranskrybowane (do ewentualnego incrementalnego zapisu, choć plan zakłada pełny zapis na końcu).
  * Iteruj przez listę plików audio (`file_name`):
    * Sprawdź, czy `file_name` (lub jakaś ustandaryzowana wersja nazwy pliku) jest kluczem w `existing_transcriptions_map`. Użyj normalizacji nazw plików jeśli to konieczne.
    * Jeśli `file_name` jest kluczem w `existing_transcriptions_map`:
      * Dodaj istniejący wpis `{"title": file_name, "content": existing_transcriptions_map[file_name]}` do `all_transcriptions`.
      * Zaloguj informację, że transkrypcja dla `{file_name}` została pominięta, ponieważ już istnieje.
    * Jeśli `file_name` nie jest kluczem w `existing_transcriptions_map`:
      * Zaloguj rozpoczęcie transkrypcji dla `{file_name}`.
      * Utwórz pełną ścieżkę do pliku audio: `audio_path = os.path.join(audio_dir, file_name)`.
      * Użyj bloku `try...except` do obsługi potencjalnych błędów AssemblyAI.
      * Wywołaj `aai.Transcriber().transcribe(audio_path)`.
      * Sprawdź `transcript.status`. Jeśli `"error"`:
        * Zaloguj błąd transkrypcji dla `{file_name}` wraz z `transcript.error`.
        * **Decyzja:** Zgodnie z planem, pomijamy pliki z błędem i nie dodajemy ich do listy `all_transcriptions`. Możesz zmienić tę decyzję, aby dodać wpis z błędem lub pustym `content`, jeśli chcesz śledzić nieudane transkrypcje.
      * Jeśli transkrypcja udana (`"completed"`):
        * Utwórz słownik: `new_transcription = {"title": file_name, "content": transcript.text}`.
        * Dodaj `new_transcription` do listy `all_transcriptions`.
        * Zaloguj sukces transkrypcji dla `{file_name}`.
      * W bloku `except` obsłuż ogólne wyjątki podczas transkrypcji i loguj je.
  * Po zakończeniu iteracji przez wszystkie pliki:
    * **Ważne:** Upewnij się, że lista `all_transcriptions` zawiera wszystkie transkrypcje (zarówno wczytane, jak i nowo utworzone). Jeśli pominięto pliki z błędem, lista `all_transcriptions` będzie zawierać tylko udane transkrypcje (wczytane i nowo zakończone).
    * Zapisz całą listę `all_transcriptions` do pliku `transcriptions_file` za pomocą `save_transcriptions`. To zapewnia, że plik JSON jest aktualizowany o wszystkie przetworzone (lub wczytane) transkrypcje.
  * Zwróć listę `all_transcriptions`.

**4. Przygotowanie Kontekstu dla LLM (`adw/S02/S02E01/sfa_s02e01.py`)**

* Zaimplementuj nową funkcję `prepare_llm_context(transcriptions: List[Dict]) -> str`.
  * Upewnij się, że `transcriptions` jest listą słowników o strukturze `{"title": ..., "content": ...}`.
  * Iteruj przez listę `transcriptions`.
  * Dla każdego elementu, sformatuj tekst w czytelny sposób dla LLM, np.:

        ```markdown
        --- Transkrypcja z pliku: {element["title"]} ---
        {element["content"]}

        ```

  * Połącz wszystkie sformatowane fragmenty w jeden duży string `llm_context_string`.
  * Zwróć `llm_context_string`.

**5. Implementacja Logiki Głównej w `main()` (`adw/S02/S02E01/sfa_s02e01.py`)**

* Zmodyfikuj funkcję `main()` ze schematu `sfa_bbs.py`.
* Po walidacji konfiguracji:
  * **Krok 1: Pobierz i rozpakuj archiwum audio.**
    * Wywołaj `download_and_unzip(AUDIO_ZIP_URL, AUDIO_DIR)`. Sprawdź, czy operacja zakończyła się sukcesem (np. sprawdzając istnienie `AUDIO_DIR`). Jeśli niepowodzenie, zaloguj i zakończ.
  * **Krok 2: Transkrybuj pliki audio (lub wczytaj z cache).**
    * Wywołaj `transcribe_audio_files(AUDIO_DIR, TRANSCRIPTIONS_FILE, ASSEMBLYAI_API_KEY)`. Przekaż ścieżki i klucz. Zapisz zwróconą listę do zmiennej `transcriptions_list`.
    * Sprawdź, czy `transcriptions_list` nie jest pusta. Jeśli jest pusta (nie znaleziono ani nie udało się przetranskrybować żadnego pliku), zaloguj błąd i zakończ.
  * **Krok 3: Przygotuj kontekst dla LLM.**
    * Wywołaj `prepare_llm_context(transcriptions_list)`. Zapisz wynik do zmiennej `llm_context`.
  * **Krok 4: Sformułuj pełny prompt dla LLM.**
    * System prompt jest wczytywany wewnątrz funkcji `call_llm` przy użyciu `system_prompt_path`.
  * **Krok 5: Wywołaj LLM.**
    * Wywołaj `llm_answer = call_llm(prompt=llm_context, system_prompt_path=SYSTEM_PROMPT_PATH)`. Pamiętaj, że `call_llm` ma wbudowane retry logic.
    * Sprawdź, czy `llm_answer` nie jest `None`. Jeśli jest, zaloguj błąd i zakończ.
  * **Krok 6: Przygotuj finalną odpowiedź do Centrali.**
    * Zgodnie z promptem, LLM powinien zwrócić tylko nazwę ulicy jako surowy tekst.
    * Przypisz `llm_answer` do zmiennej `final_answer`. Funkcja `send_report` ma logikę czyszczenia odpowiedzi (usuwanie cudzysłowów, stripowanie whitespace), co powinno pomóc w przypadku, gdy LLM doda niechciane znaki.
  * **Krok 7: Wyślij raport.**
    * Wywołaj `report_response = send_report(REPORT_ENDPOINT, API_KEY, TASK_NAME, final_answer)`. Pamiętaj, że `send_report` ma wbudowane retry logic i logowanie odpowiedzi serwera oraz potencjalnych błędów.
    * Sprawdź, czy `report_response` nie jest `None`. Jeśli jest, zaloguj błąd (send_report już loguje szczegóły) i zakończ.
  * **Krok 8: Sprawdź odpowiedź Centrali pod kątem flagi.**
    * Wywołaj `flag = check_for_flag(report_response)`. Funkcja ta już loguje i wyświetla flagę, jeśli ją znajdzie.
    * Możesz dodać dodatkowe logowanie na podstawie wartości zwracanej przez `check_for_flag` (np. informację o braku flagi, jeśli zwróci `None`).

**6. Integracja z `sfa_bbs.py`**

* Funkcje `fetch_data`, `call_llm`, `send_report`, `check_for_flag`, `validate_configuration`, `load_system_prompt` i struktura `main` z `sfa_bbs.py` zostaną zaadaptowane do nowego pliku i uzupełnione o specyficzną logikę zadania.
* Funkcja `process_data` z `sfa_bbs.py` nie będzie używana w obecnej formie dla tego zadania.

**Struktura Pliku `adw/S02/S02E01/sfa_s02e01.py`:**

* Importy (rozszerzone o `zipfile`, `assemblyai`, `os`, `requests`, `json`).
* Konfiguracja (rozszerzona o ścieżki i klucz AssemblyAI, zaktualizowane `TASK_NAME`, `SYSTEM_PROMPT_PATH`).
* Funkcje pomocnicze z `sfa_bbs.py` (dostosowane).
* Nowe funkcje: `download_and_unzip`, `load_transcriptions`, `save_transcriptions`, `transcribe_audio_files`, `prepare_llm_context`.
* Zaktualizowana funkcja `main` implementująca workflow zadania.
* Blok `if __name__ == "__main__": main()`.

Ten plan zapewnia kompleksowe podejście, uwzględniające pobieranie danych, transkrypcję z cache'owaniem, przygotowanie kontekstu, interakcję z LLM i raportowanie, wszystko w ramach struktury Single File Agent opartej na schemacie `sfa_bbs.py`.
