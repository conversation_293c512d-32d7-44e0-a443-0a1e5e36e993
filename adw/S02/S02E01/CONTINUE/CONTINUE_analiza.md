**Analiza Zadania S02E01: Lokalizacja Instytutu Prof. Andrzeja Maja**

**1. Cel <PERSON>:**
Głównym celem jest ustalenie dokładnej nazwy ulicy instytutu na konkretnej uczelni, gdzie wykłada profesor Andrzej Maj. Informacje są ukryte w nagraniach z przesłuchań świadków.

**2. Kluczowe Informacje i Wyzwania:**

* Informacje pochodzą z nagrań audio (.m4a).
* Nagrania wymagają transkrypcji (Audio -> Tekst).
* Nagrania mogą zawierać sprzeczne informacje.
* Zeznanie Rafała jest szczególnie ważne jako jedyne potwierdzone źródło bliskiego kontaktu z profesorem, ale może być chaotyczne.
* Należy odróżnić adres instytutu od adresu głównej siedziby uczelni.
* Ostateczną odpowiedzią jest sama nazwa ulicy, w formacie JSON, wysłana do Centrali.

**3. Proponowany Plan Działania:**

1. **Pobranie danych:** Ściągnąć plik `przesluchania.zip` z podanego URL.
2. **Rozpakowanie archiwum:** Rozpakować plik ZIP, aby uzyskać dostęp do plików `.m4a`.
3. **Transkrypcja nagrań:** Przetworzyć każdy plik `.m4a` na tekst. Można użyć narzędzi lokalnych (np. Whisper, Bielik) lub API (np. OpenAI Whisper).
4. **Przygotowanie kontekstu dla LLM:** Zgromadzić transkrybowane teksty, jasno wskazując, która transkrypcja pochodzi od kogo (np. dodając nagłówki). Uformować spójny kontekst dla modelu LLM.
5. **Sformułowanie promptu dla LLM:** Stworzyć precyzyjny system prompt, który:
    * Określi rolę Agenta (analityk z wiedzą o polskich uczelniach).
    * Jasno sprecyzuje cel (nazwa ulicy instytutu).
    * Wskaże źródła danych (wyłącznie transkrypcje).
    * Zdefiniuje metodę analizy (krok po kroku, myślenie na głos, analiza sprzeczności, priorytet zeznania Rafała).
    * Zaznaczy konieczność wykorzystania wewnętrznej wiedzy o uczelniach.
    * Określi format odpowiedzi (sama nazwa ulicy).
6. **Uruchomienie LLM:** Przekazać przygotowany prompt wraz z kontekstem (transkrypcjami) do wybranego modelu językowego.
7. **Ekstrakcja odpowiedzi:** Wyodrębnić ustaloną nazwę ulicy z odpowiedzi modelu.
8. **Przygotowanie odpowiedzi do Centrali:** Sformatować odpowiedź w wymagany format JSON: `{"task": "mp3", "apikey": "TWOJ_KLUCZ_API", "answer": "Nazwa Ulicy"}`.
9. **Wysłanie odpowiedzi:** Wysłać dane JSON metodą POST do endpointu `/report` na wskazanej platformie Centrali.

**4. Proponowany Stack Technologiczny:**

* **Język:** Python (bogactwo bibliotek).
* **Pobieranie/Rozpakowywanie/JSON/Wysłka:** Standardowe biblioteki Pythona (`urllib`, `zipfile`, `json`) oraz `requests`.
* **Transkrypcja (Audio -> Tekst):** Lokalnie (Whisper, Bielik) lub API (OpenAI Whisper, Google Cloud Speech-to-Text).
* **Analiza Tekstu (LLM):** Lokalnie (Bielik, modele z Hugging Face) lub API (OpenAI GPT, Google Gemini).

Plan ten zakłada sekwencyjne wykonanie kroków od pozyskania danych po finalną wysyłkę odpowiedzi, z kluczowym etapem analizy danych tekstowych przez model LLM.
