#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Single File Agent S02E01: Location of Professor <PERSON>'s Institute

This script downloads audio interview files, transcribes them using AssemblyAI,
caches the transcriptions, uses an LLM (via OpenAI API compatible endpoint)
to identify the street address of a specific institute based on the
transcriptions, and reports the answer to a central API.

Based on SFA-BBS template (adw/sfa_bbs.py) and implementation plan
(adw/S02/S02E01/CONTINUE/CONTINUE_IM_plan.md).

Author: Augment Agent
Date: 2024-05-24
Version: 1.0.0
"""

# --- IMPORTS ---
import os
import re
import json
import logging
import time
import requests
import zipfile
import assemblyai as aai # Import AssemblyAI
from typing import Dict, List, Any, Tuple, Optional, Union
from dotenv import load_dotenv
from openai import OpenAI, APIError, APIConnectionError, RateLimitError

# --- CONFIGURATION ---
# Load environment variables
load_dotenv()

# --- APPLICATION SETTINGS ---
TASK_NAME = "mp3" # Updated task name
API_BASE_URL = os.getenv("API_BASE_URL", "https://c3ntrala.ag3nts.org")
REPORT_ENDPOINT = f"{API_BASE_URL}/report"
API_KEY = os.getenv("AIDEVS_API_KEY")
ASSEMBLY_API_KEY = os.getenv("ASSEMBLY_API_KEY") # Added AssemblyAI key setting here

# Request settings
REQUEST_TIMEOUT = 60  # Increased timeout for transcription API calls
RETRY_ATTEMPTS = 0 # Increased retry attempts
RETRY_DELAY = 5  # Increased retry delay

# --- LLM SETTINGS ---
LLM_MODEL = os.getenv("KLUSTER_LLM_MODEL", os.getenv("OPENAI_LLM_MODEL"))
LLM_API_KEY = os.getenv("KLUSTER_API_KEY", os.getenv("OPENAI_API_KEY"))
LLM_BASE_URL = os.getenv("KLUSTER_BASE_URL", os.getenv("OPENAI_BASE_URL"))
LLM_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", "4000"))
LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.3"))
LLM_TOP_P = float(os.getenv("LLM_TOP_P", "1.0"))

# System prompt path - load from file instead of hardcoding
# Adjust path to point to the correct location relative to the script
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
SYSTEM_PROMPT_PATH = os.path.join(BASE_DIR, "CONTINUE_sys_prompt.txt")

# Default system prompt (used if file not found) - less relevant with file loading
DEFAULT_SYSTEM_PROMPT = """
You are an AI assistant helping with a specific task.
Provide clear, concise, and accurate responses.
"""

# --- TASK-SPECIFIC SETTINGS ---
# ASSEMBLY_API_KEY is now defined under APPLICATION SETTINGS
AUDIO_ZIP_URL = "https://c3ntrala.ag3nts.org/dane/przesluchania.zip" # URL from task description
AUDIO_DIR = os.path.join(BASE_DIR, "przesluchania") # Local directory for audio files
TRANSCRIPTIONS_FILE = os.path.join(BASE_DIR, "transcriptions.json") # Local file for caching transcriptions

# --- REGEX PATTERNS ---
# Common regex patterns for flag extraction - customize as needed
FLAG_REGEX = r"FLG[a-zA-Z0-9_-]+"
FLAG_REGEX_CURLY = r"\{\{FLG:[a-zA-Z0-9_-]+\}\}"

# --- LOGGING CONFIGURATION ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# --- HELPER FUNCTIONS (from sfa_bbs.py, adapted) ---

def validate_configuration() -> bool:
    """
    Validates that all required configuration variables are set.
    Returns:
        bool: True if configuration is valid, False otherwise
    """
    required_env_vars = {
        "AIDEVS_API_KEY": "API key for Central API",
        "KLUSTER_API_KEY": "API key for LLM (Kluster.ai/OpenAI)",
        "ASSEMBLY_API_KEY": "API key for AssemblyAI transcription", # Added AssemblyAI key check
    }
    missing_vars = []

    for var, description in required_env_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"{var} ({description})")

    if missing_vars:
        logger.critical(f"Missing required environment variables: {', '.join(missing_vars)}")
        return False

    # Check if API_KEY is set
    if not API_KEY:
        logger.critical("API_KEY is not set, check AIDEVS_API_KEY environment variable")
        return False

    # Check if LLM_API_KEY is set (can fall back to OPENAI_API_KEY)
    if not LLM_API_KEY:
        logger.warning("LLM_API_KEY is not set, using OPENAI_API_KEY fallback")
        if not os.getenv("OPENAI_API_KEY"):
             logger.critical("Neither KLUSTER_API_KEY nor OPENAI_API_KEY are set for LLM")
             return False
    # Check if ASSEMBLY_API_KEY is set
    if not ASSEMBLY_API_KEY:
        logger.critical("ASSEMBLY_API_KEY is not set")
        return False

    # Check if system prompt file exists
    if not os.path.exists(SYSTEM_PROMPT_PATH):
        logger.critical(f"System prompt file not found at: {SYSTEM_PROMPT_PATH}")
        return False


    logger.info("Configuration validated successfully.")
    return True

# fetch_data is not needed for this specific task as data comes from a zip file
# kept here for completeness as per base schema, but not called in main
def fetch_data(url: str, api_key: str, attempt: int = 1) -> Optional[Dict]:
    """
    Fetches data from the specified URL with retry logic.
    (Placeholder from base schema, not used in this task's main workflow)
    """
    logger.info(f"Attempting to fetch data from {url} (attempt {attempt}/{RETRY_ATTEMPTS + 1})")
    # This function would typically interact with the AIDEVS API's /data endpoint
    # For this task, data is from a direct URL download.
    logger.warning("fetch_data function is a placeholder and not used in this task's main workflow.")
    return None # Return None as it's not used to get task data


def load_system_prompt(system_prompt_path: str) -> str:
    """
    Wczytuje system prompt z pliku.

    Args:
        system_prompt_path: Ścieżka do pliku z system promptem.

    Returns:
        str: Zawartość pliku z system promptem lub domyślny prompt w przypadku błędu.
    """
    try:
        with open(system_prompt_path, 'r', encoding='utf-8') as file:
            system_prompt = file.read()
        # logger.info(f"Wczytano system prompt z pliku: {system_prompt_path}") # Avoid excessive logging on every LLM call
        return system_prompt
    except Exception as e:
        logger.error(f"Błąd podczas wczytywania system promptu z pliku {system_prompt_path}: {e}")
        logger.warning("Używanie domyślnego system promptu")
        return DEFAULT_SYSTEM_PROMPT


def call_llm(prompt: str, system_prompt_path: str, attempt: int = 1) -> Optional[str]:
    """
    Calls the LLM with the given prompt and handles errors with retry logic.
    Uses system prompt from file specified by system_prompt_path.

    Args:
        prompt: The user prompt to send to the LLM
        system_prompt_path: Path to system prompt file.
        attempt: Current attempt number (for retry logic)

    Returns:
        Optional[str]: The LLM's response, or None if failed
    """
    logger.info(f"Calling LLM (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    try:
        system_prompt = load_system_prompt(system_prompt_path)
        if not system_prompt and system_prompt_path != SYSTEM_PROMPT_PATH:
             # Fallback to default path if specific path failed and wasn't the default config path
             system_prompt = load_system_prompt(SYSTEM_PROMPT_PATH)
             if not system_prompt:
                  logger.critical("Failed to load system prompt from both specified path and default config path.")
                  return None
             else:
                  logger.warning(f"Using system prompt from default config path: {SYSTEM_PROMPT_PATH}")
        elif not system_prompt:
             logger.critical(f"Failed to load system prompt from configured path: {SYSTEM_PROMPT_PATH}")
             return None


        client_params = {"api_key": LLM_API_KEY}
        if LLM_BASE_URL:
            client_params["base_url"] = LLM_BASE_URL

        client = OpenAI(**client_params)

        logger.info(f"Using model: {LLM_MODEL} via API: {LLM_BASE_URL}")

        completion = client.chat.completions.create(
            model=LLM_MODEL,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            temperature=LLM_TEMPERATURE,
            max_completion_tokens=LLM_MAX_TOKENS, # Use max_completion_tokens for newer OpenAI lib
            top_p=LLM_TOP_P
        )

        # Apply the suggested fix here:
        response = completion.choices[0].message.content.strip()

        logger.info("LLM call successful")
        return response

    except (APIError, APIConnectionError, RateLimitError) as e:
        logger.error(f"LLM API error: {e}")

        # Retry logic
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return call_llm(prompt, system_prompt_path, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except Exception as e:
        logger.error(f"Unexpected error during LLM call: {e}", exc_info=True)
        return None

def send_report(url: str, api_key: str, task_name: str, answer: Any, attempt: int = 1) -> Optional[Dict]:
    """
    Sends the final report/answer to the specified endpoint.

    Args:
        url: The URL to send the report to
        api_key: API key for authentication
        task_name: Name of the task being performed
        answer: The answer/data to send
        attempt: Current attempt number (for retry logic)

    Returns:
        Optional[Dict]: The response from the server, or None if failed
    """
    logger.info(f"Sending report to {url} (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    # Ensure the answer is properly formatted
    # Remove whitespace from beginning and end
    if isinstance(answer, str):
        clean_answer = answer.strip()

        # Remove quotes if present
        if (clean_answer.startswith('"') and clean_answer.endswith('"')) or \
           (clean_answer.startswith("'") and clean_answer.endswith("'")):
            clean_answer = clean_answer[1:-1]

        logger.info(f"Original answer: '{answer}'")
        logger.info(f"Cleaned answer: '{clean_answer}'")
    else:
        clean_answer = answer

    payload = {
        "task": task_name,
        "apikey": api_key,
        "answer": clean_answer
    }

    headers = {
        "Content-Type": "application/json; charset=utf-8"
    }

    # Log full payload before sending
    logger.info(f"Sending payload: {json.dumps(payload, ensure_ascii=False)}")

    try:
        response = requests.post(
            url,
            json=payload,  # Use json instead of data to let requests handle serialization
            headers=headers,
            timeout=REQUEST_TIMEOUT
        )

        # Log full server response
        logger.info(f"Response code: {response.status_code}")
        logger.info(f"Response headers: {dict(response.headers)}")
        logger.info(f"Response content: {response.text}")

        # Display full response in console
        print(f"\n{'='*50}")
        print(f"RESPONSE FROM SERVER:")
        print(f"Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Content: {response.text}")
        print(f"{'='*50}\n")

        response.raise_for_status()

        result = response.json()
        logger.info("Report sent successfully")
        return result

    except requests.exceptions.RequestException as e:
        logger.error(f"Error sending report: {e}")

        if hasattr(e, 'response') and hasattr(e.response, 'text'):
            logger.error(f"Server response: {e.response.text}")
            logger.error(f"Response headers: {dict(e.response.headers) if hasattr(e.response, 'headers') else 'No headers'}")

            # Display full error response in console
            print(f"\n{'='*50}")
            print(f"ERROR FROM SERVER:")
            print(f"Code: {e.response.status_code if hasattr(e.response, 'status_code') else 'No code'}")
            print(f"Headers: {dict(e.response.headers) if hasattr(e.response, 'headers') else 'No headers'}")
            print(f"Content: {e.response.text}")
            print(f"{'='*50}\n")

        # Retry logic
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return send_report(url, api_key, task_name, answer, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response: {e}")
        return None


def check_for_flag(response: Any) -> Optional[str]:
    """
    Sprawdza, czy odpowiedź zawiera flagę w formatach:
    - FLG[a-zA-Z0-9_-]+
    - {{FLG:[a-zA-Z0-9_-]+}}
    Zwraca pierwszą znalezioną flagę lub None.

    Args:
        response: Odpowiedź do sprawdzenia (może być string, dict lub inny typ)

    Returns:
        Optional[str]: Znaleziona flaga lub None, jeśli nie znaleziono
    """
    # Convert response to string for easier searching
    response_str = str(response)

    if not response_str:
        logger.info("Response is empty, no flag to check.")
        return None

    # Search for flag in {{FLG:XXXX}} format
    match_curly = re.search(FLAG_REGEX_CURLY, response_str)
    if match_curly:
        flag = match_curly.group(0)
        logger.info(f"Flag found in {{{{FLG:XXXX}}}} format: {flag}")
        print(f"\n{'='*50}\nFLAG: {flag}\n{'='*50}\n")
        return flag

    # Search for flag in FLG[a-zA-Z0-9_-]+ format
    match = re.search(FLAG_REGEX, response_str)
    if match:
        flag = match.group(0)
        logger.info(f"Flag found in FLG[a-zA-Z0-9_-]+ format: {flag}")
        print(f"\n{'='*50}\nFLAG: {flag}\n{'='*50}\n")
        return flag

    # Check if text contains the word "flag" or "FLG" (case-insensitive)
    if "flag" in response_str.lower() or "flg" in response_str.lower():
        logger.info(f"Text contains the word 'flag' or 'FLG': {response_str}")
        # We don't return the whole response here, just log the potential
        # The regex matches are more precise for the actual flag format.
        # print(f"\n{'='*50}\nPOTENTIAL FLAG: {response_str}\n{'='*50}\n") # Avoid printing potentially large responses
        pass # Just log, don't return the whole string as a potential flag

    logger.info("No flag found in response using standard patterns.")
    return None

# process_data is a placeholder from base schema, not used in this task's main workflow
def process_data(data: Dict) -> Dict:
    """
    Processes the fetched data according to task requirements.
    This is a placeholder function that should be customized for each specific agent.

    Args:
        data: The data to process

    Returns:
        Dict: The processed data
    """
    logger.info("Processing data (placeholder function)")
    # This function is not used in this task's main workflow.
    return data # Return input data as is


# --- TASK-SPECIFIC FUNCTIONS ---

def download_and_unzip(url: str, destination_dir: str) -> bool:
    """
    Downloads a zip file from a URL and extracts its contents.

    Args:
        url: The URL of the zip file.
        destination_dir: The directory to extract files to.

    Returns:
        bool: True if successful, False otherwise.
    """
    logger.info(f"Attempting to download and unzip from {url} to {destination_dir}")
    try:
        # Create destination directory if it doesn't exist
        os.makedirs(destination_dir, exist_ok=True)
        logger.info(f"Ensured directory {destination_dir} exists.")

        # Download the zip file
        response = requests.get(url, stream=True, timeout=REQUEST_TIMEOUT)
        response.raise_for_status() # Raise an HTTPError for bad responses (4xx or 5xx)

        # Save the zip file temporarily
        temp_zip_path = os.path.join(destination_dir, "temp_audio.zip")
        with open(temp_zip_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        logger.info(f"Downloaded zip file to {temp_zip_path}")

        # Extract the zip file
        with zipfile.ZipFile(temp_zip_path, 'r') as zip_ref:
            zip_ref.extractall(destination_dir)
        logger.info(f"Extracted zip file contents to {destination_dir}")

        # Remove the temporary zip file
        os.remove(temp_zip_path)
        logger.info(f"Removed temporary zip file {temp_zip_path}")

        return True

    except requests.exceptions.RequestException as e:
        logger.error(f"Error downloading zip file: {e}")

    except zipfile.BadZipFile:
        logger.error(f"Downloaded file is not a valid zip file.")

    except Exception as e:
        logger.error(f"An unexpected error occurred during download or unzip: {e}", exc_info=True)

    return False # Indicate failure


def load_transcriptions(file_path: str) -> List[Dict]:
    """
    Loads transcriptions from a JSON file.

    Args:
        file_path: Path to the JSON file.

    Returns:
        List[Dict]: List of transcription dictionaries, or empty list if file
                    doesn't exist or is invalid.
    """
    if not os.path.exists(file_path):
        logger.info(f"Transcription file not found at {file_path}. Starting with empty cache.")
        return []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            transcriptions = json.load(f)
            if not isinstance(transcriptions, list):
                 logger.warning(f"Transcription file {file_path} does not contain a list. Starting with empty cache.")
                 return []
            logger.info(f"Loaded {len(transcriptions)} transcriptions from {file_path}.")
            return transcriptions
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON from {file_path}: {e}. Starting with empty cache.")
        return []
    except Exception as e:
        logger.error(f"An unexpected error occurred while loading transcriptions from {file_path}: {e}", exc_info=True)
        return []


def save_transcriptions(file_path: str, transcriptions: List[Dict]):
    """
    Saves transcriptions to a JSON file.

    Args:
        file_path: Path to the JSON file.
        transcriptions: List of transcription dictionaries to save.
    """
    try:
        # Ensure directory exists before saving
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(transcriptions, f, indent=4, ensure_ascii=False)
        logger.info(f"Saved {len(transcriptions)} transcriptions to {file_path}.")
    except Exception as e:
        logger.error(f"Error saving transcriptions to {file_path}: {e}", exc_info=True)


def transcribe_audio_files(audio_dir: str, transcriptions_file: str, aai_api_key: str) -> List[Dict]:
    """
    Transcribes audio files in a directory using AssemblyAI, caching results.

    Args:
        audio_dir: Directory containing audio files.
        transcriptions_file: JSON file path for caching transcriptions.
        aai_api_key: AssemblyAI API key.

    Returns:
        List[Dict]: List of all available transcriptions (loaded and newly created).
    """
    if not os.path.exists(audio_dir):
        logger.error(f"Audio directory not found: {audio_dir}")
        return []

    aai.settings.api_key = aai_api_key
    transcriber = aai.Transcriber(config=aai.TranscriptionConfig(language_code="pl")) # Dodano konfigurację języka polskiego

    existing_transcriptions = load_transcriptions(transcriptions_file)
    existing_transcriptions_map = {t.get('title'): t.get('content', '') for t in existing_transcriptions if t and t.get('title')}

    all_transcriptions = []
    newly_transcribed = False # Flag to indicate if any new transcriptions were made

    try:
        # List audio files (m4a, mp3, etc.)
        audio_files = [f for f in os.listdir(audio_dir) if f.lower().endswith(('.m4a', '.mp3', '.wav', '.flac'))]
        audio_files.sort() # Sort for consistent processing order

        if not audio_files:
            logger.warning(f"No supported audio files found in {audio_dir}.")
            # Add existing transcriptions to the list even if no new files are found
            all_transcriptions.extend(existing_transcriptions)
            return all_transcriptions


        for file_name in audio_files:
            if file_name in existing_transcriptions_map:
                logger.info(f"Transcription for '{file_name}' already exists. Using cached version.")
                all_transcriptions.append({"title": file_name, "content": existing_transcriptions_map[file_name]})
            else:
                audio_path = os.path.join(audio_dir, file_name)
                logger.info(f"Starting transcription for '{file_name}'...")
                try:
                    # Using default config for now, can be customized if needed
                    transcript = transcriber.transcribe(audio_path)

                    if transcript.status == aai.TranscriptStatus.error:
                        logger.error(f"Transcription failed for '{file_name}': {transcript.error}")
                        # Decide whether to add an entry for failed transcription or skip
                        # Skipping as per plan decision
                        pass
                    else: # Assuming status is completed
                        logger.info(f"Transcription completed for '{file_name}'.")
                        all_transcriptions.append({"title": file_name, "content": transcript.text})
                        newly_transcribed = True

                except Exception as e:
                    logger.error(f"An error occurred during AssemblyAI transcription for '{file_name}': {e}", exc_info=True)
                    # Skipping as per plan decision
                    pass

        # Save transcriptions if any new ones were added or if the cache file didn't exist initially
        # This ensures the cache is updated with newly transcribed files and includes old ones.
        # It also handles the case where the file didn't exist and we just loaded old ones.
        if newly_transcribed or not os.path.exists(transcriptions_file):
             # Merge newly transcribed with existing ones to ensure the saved file is complete
             # This is important if some files were already cached and others were new.
             # A more robust approach might merge based on title, but appending new ones
             # and saving the combined list works if we trust the 'all_transcriptions' list.
             # Let's rebuild the map and then the list to be safe.
             final_transcription_map = {t.get('title'): t.get('content', '') for t in existing_transcriptions if t and t.get('title')}
             for t in all_transcriptions:
                 if t.get('title'):
                     final_transcription_map[t['title']] = t.get('content', '') # Corrected assignment

             final_transcription_list = [{"title": title, "content": content} for title, content in final_transcription_map.items()]

             save_transcriptions(transcriptions_file, final_transcription_list)
             # Update all_transcriptions list to reflect the saved state (including old ones)
             all_transcriptions = final_transcription_list


    except Exception as e:
        logger.error(f"An unexpected error occurred during audio transcription process: {e}", exc_info=True)
        # Return whatever transcriptions were successfully loaded or created before the error
        # Ensure existing ones are included if the error happened during iteration
        if not all_transcriptions and existing_transcriptions:
             all_transcriptions.extend(existing_transcriptions)
        pass # Continue to return the list even if incomplete

    return all_transcriptions


def prepare_llm_context(transcriptions: List[Dict]) -> str:
    """
    Formats a list of transcriptions into a single string for the LLM context.

    Args:
        transcriptions: List of transcription dictionaries.

    Returns:
        str: Formatted string containing all transcriptions.
    """
    logger.info(f"Preparing LLM context from {len(transcriptions)} transcriptions.")
    context_parts = []
    for transcription in transcriptions:
        title = transcription.get("title", "Unknown File")
        content = transcription.get("content", "No transcription content.")
        context_parts.append(f"--- Transkrypcja z pliku: {title} ---\n{content}\n\n")

    llm_context_string = "".join(context_parts).strip()
    # logger.debug(f"Prepared LLM context:\n{llm_context_string[:500}...") # Log start of context
    return llm_context_string

# process_data is a placeholder from base schema, not used in this task's main workflow
# def process_data(data: Dict) -> Dict:
#     """
#     Processes the fetched data according to task requirements.
#     This is a placeholder function that should be customized for each specific agent.
#
#     Args:
#         data: The data to process
#
#     Returns:
#         Dict: The processed data
#     """
#     logger.info("Processing data (placeholder function)")
#     # This function is not used in this task's main workflow.
#     return data # Return input data as is


# --- MAIN FUNCTION ---

def main():
    """
    Main function that orchestrates the agent's workflow for S02E01.
    """
    logger.info(f"--- Starting {TASK_NAME} agent ---")

    # Validate configuration
    if not validate_configuration():
        logger.critical("Configuration validation failed. Exiting.")
        return

    try:
        # Step 1: Download and unzip audio archive
        # In this task, we don't fetch data from the /data endpoint,
        # but download the zip file directly.
        logger.info("Skipping fetch_data from Central API for this task.")
        # data = fetch_data(DATA_ENDPOINT, API_KEY) # Not used

        if not download_and_unzip(AUDIO_ZIP_URL, AUDIO_DIR):
            logger.critical("Failed to download and unzip audio files. Exiting.")
            return
        logger.info("Audio files ready.")

        # Step 2: Transcribe audio files (or load from cache)
        transcriptions_list = transcribe_audio_files(AUDIO_DIR, TRANSCRIPTIONS_FILE, ASSEMBLY_API_KEY)
        if not transcriptions_list:
            logger.critical("No transcriptions available. Exiting.")
            return
        logger.info(f"Successfully obtained {len(transcriptions_list)} transcriptions.")


        # Step 3: Prepare context for LLM
        llm_context = prepare_llm_context(transcriptions_list)
        if not llm_context:
             logger.critical("Prepared LLM context is empty. Exiting.")
             return
        logger.info("LLM context prepared.")

        # Step 4 & 5: Call LLM with the context and system prompt
        # The system prompt is loaded within call_llm using SYSTEM_PROMPT_PATH
        llm_answer = call_llm(prompt=llm_context, system_prompt_path=SYSTEM_PROMPT_PATH)
        if not llm_answer:
            logger.critical("Failed to get answer from LLM. Exiting.")
            return
        logger.info(f"Received answer from LLM: '{llm_answer}'")

        # Step 6: Prepare final answer (LLM answer is the final answer for this task)
        final_answer = llm_answer
        logger.info(f"Final answer prepared: '{final_answer}'")

        # Step 7: Send report
        report_response = send_report(REPORT_ENDPOINT, API_KEY, TASK_NAME, final_answer)
        if not report_response:
            logger.critical("Failed to send report. Exiting.")
            return
        logger.info("Report sent successfully.")

        # Step 8: Check for flag in response
        flag = check_for_flag(report_response)

        # Step 9: Display final result
        if flag:
            logger.info(f"Task completed successfully. Found flag: {flag}")
            # check_for_flag already prints the flag prominently
        else:
            logger.warning("Task completed, but no flag found in the final report response.")
            print(f"\n{'='*50}\nTASK COMPLETED - NO FLAG FOUND\n{'='*50}\n")
            print(f"Final Answer Sent: '{final_answer}'")
            print(f"Report Response: {json.dumps(report_response, indent=2)}")
            print(f"{'='*50}\n")

    except Exception as e:
        logger.critical(f"An unexpected error occurred during the agent's execution: {e}", exc_info=True)

    logger.info(f"--- {TASK_NAME} agent finished ---")

# --- SCRIPT ENTRY POINT ---

if __name__ == "__main__":
    main()