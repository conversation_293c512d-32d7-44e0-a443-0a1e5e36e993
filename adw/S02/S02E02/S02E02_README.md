# City Identification Agent (S02E02)

**Version:** 1.0.0  
**Author:** Augment Agent  
**Date:** 2024-07-30

---

This project, `S02E02_sfa.py`, is a Single File Agent (SFA) designed to identify a city based on a provided map image (`fragmenty.jpeg`). It utilizes a Vision Large Language Model (LLM) through an OpenAI-compatible API (such as Kluster.ai) to analyze the image and determine the city's name.

This agent is based on the SFA-BBS template and task analysis from S02E02.

---

## Overview

The primary goal of this agent is to process a local image file containing fragments of a map, send it to a Vision LLM along with specific instructions, and retrieve the identified city name. The agent is configured to expect one of the map fragments to be from a different city, requiring the LLM to discern the correct location from the consistent pieces of information.

---

## Functionality

The agent performs the following steps:

1. **Load Configuration:** Reads environment variables for LLM API keys, model names, and other settings.
2. **Validate Configuration:** Ensures essential configurations like the API key are set.
3. **Read and Encode Image:** Loads the `fragmenty.jpeg` image file from the local filesystem and encodes it into a Base64 string.
4. **Load System Prompt:** Reads detailed instructions for the LLM from the `S02E02_sys_prompt.txt` file. This prompt guides the LLM on how to analyze map fragments, identify features, detect an erroneous fragment, and verify its findings.
5. **Formulate User Prompt:** A specific user prompt is defined in the script, instructing the LLM to analyze the provided map image according to system instructions and return *only* the city name.
6. **Call LLM:** Sends the encoded image, system prompt, and user prompt to the configured Vision LLM. The agent includes retry logic for API errors.
7. **Process Response:** Receives the response from the LLM. The script expects the LLM's response to be the direct name of the identified city.
8. **Display Result:** Prints the identified city name to the console.

---

## Key Components

- **`S02E02_sfa.py`**: The main Python script orchestrating the city identification task.
- **`fragmenty.jpeg`**: The input image file. This file should contain various map fragments, one of which is intentionally misleading (from a different city). It must be located in the same directory as the script.
- **`S02E02_sys_prompt.txt`**: A text file containing the system prompt for the LLM. This prompt provides detailed instructions on how to perform the analysis. It must be located in the same directory as the script.
- **`.env` file (not provided, user-created):** Used to store sensitive information and configuration parameters like API keys.

---

## Configuration

### Environment Variables

Create a `.env` file in the same directory as the script with the following variables:

- **LLM Provider (choose one prefix: `KLUSTER_` or `OPENAI_`)**:
  - `KLUSTER_MULTIMODAL_MODEL` or `OPENAI_MULTIMODAL_MODEL`: The identifier for the multimodal LLM you want to use (e.g., `gpt-4-vision-preview`).
  - `KLUSTER_API_KEY` or `OPENAI_API_KEY`: Your API key for the LLM service.
  - `KLUSTER_BASE_URL` or `OPENAI_BASE_URL`: The base URL for the LLM API. For OpenAI, this can often be omitted to use the default. For Kluster.ai or other providers, this is typically required.
- **LLM Parameters (Optional):**
  - `LLM_MAX_TOKENS`: Maximum number of tokens for the LLM response (default: `4000`).
  - `LLM_TEMPERATURE`: Controls the randomness of the LLM's output (default: `0.3`).
  - `LLM_TOP_P`: Controls nucleus sampling (default: `1.0`).

**Example `.env` file:**

```env
# For Kluster.ai
# KLUSTER_API_KEY="your_kluster_api_key"
# KLUSTER_BASE_URL="your_kluster_base_url"
# KLUSTER_MULTIMODAL_MODEL="model_name"

# For OpenAI
OPENAI_API_KEY="your_openai_api_key"
OPENAI_MULTIMODAL_MODEL="gpt-4-vision-preview"
# OPENAI_BASE_URL="your_openai_compatible_base_url_if_not_default"

LLM_MAX_TOKENS=150
LLM_TEMPERATURE=0.2
```

### Files

- Ensure `fragmenty.jpeg` is present in the same directory as `S02E02_sfa.py`.
- Ensure `S02E02_sys_prompt.txt` is present in the same directory as `S02E02_sfa.py`.

---

## Prerequisites

- Python 3.7+
- The following Python packages:
  - `openai`
  - `python-dotenv`

You can install these dependencies using pip:

```bash
pip install openai python-dotenv
```

---

## How to Run

1. **Set up Environment:**
   - Create the `.env` file in the project directory and populate it with your API keys and desired model configurations (see Environment Variables).
2. **Place Required Files:**
   - Ensure `fragmenty.jpeg` (the map image) is in the same directory as the script.
   - Ensure `S02E02_sys_prompt.txt` (the system prompt) is in the same directory as the script.
3. **Execute the Script:**
   - Navigate to the directory containing the script in your terminal.
   - Run the script using Python:

     ```bash
     python S02E02_sfa.py
     ```

---

## Output

The script will log its progress to the console, including:

- Configuration validation status.
- Image processing status.
- LLM call attempts and status.

Upon successful execution, the script will print the identified city name in the following format:

```text
==================================================
IDENTIFIED CITY (FLAG): [Identified City Name]
==================================================
```

---

## System Prompt (`S02E02_sys_prompt.txt`) Details

The `S02E02_sys_prompt.txt` file provides comprehensive instructions to the Vision LLM. Key aspects include:

- **Task Specialization:** The LLM is designated as an agent specialized in analyzing map images.
- **Image Analysis:** It must carefully analyze visual elements from the provided map fragments.
- **Element Identification:** Focus on street names, landmarks (churches, cemeteries, schools, parks, rivers), urban layout, and specific clues like the presence of granaries or a fortress.
- **Inconsistency Detection:** Crucially, the LLM is informed that one of the map fragments is from a different city and is erroneous. It must identify this inconsistent fragment and focus on the coherent ones.
- **Verification:** After a potential identification, the LLM should (conceptually) verify if the features match the city.
- **City Determination:** Based on consistent fragments and verification, it must determine the city name.
- **Output Formatting (as per System Prompt):** The system prompt requests a two-part response:
  1. Detailed reasoning of the analysis process.
  2. The complete city name enclosed in `<city>` tags (e.g., `<city>Swinoujscie</city>`).

**Note on LLM Output Handling:**

While the `S02E02_sys_prompt.txt` asks for detailed reasoning followed by the city in tags, the `S02E02_sfa.py` script's **user prompt** specifically requests: *"Proszę podać wyłącznie nazwę miasta."* (Please provide only the city name.). The script then takes the LLM's entire response and uses `.strip()` on it, expecting it to be the city name directly. This implies that the agent is currently set up to prioritize the user prompt's request for a concise answer, and might not process or display the detailed reasoning if the LLM provides it.

---

This README should provide a good overview for anyone looking to understand or use your City Identification Agent.
