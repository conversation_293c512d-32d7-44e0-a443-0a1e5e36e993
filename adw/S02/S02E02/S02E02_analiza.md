## Analiza Zadania: Identyfikacja Miasta na Podstawie Fragmentów Mapy przy Użyciu Vision LLM

**Cel Zadania:** Głównym celem jest zidentyfikowanie nazwy miasta na podstawie dostarczonych fragmentów mapy, wykorzy<PERSON><PERSON>j<PERSON><PERSON> model językowy zdolny do analizy obrazu (Vision LLM). Kluczowym elementem jest świadomość, że jeden z fragmentów mapy może być błędny i pochodzić z innej lokalizacji. Zidentyfikowana nazwa miasta ma zostać przesłana jako "flaga".

**Kontekst Szkolenia:** Zadanie jest częścią szkolenia z budowy oprogramowania agentowego opartego na LLM. Podkreślono, że celem jest nauka pracy z modelami Vision, a nie stosowanie technik OSINT (choć w realnych scenariuszach OSINT byłby również cennym narzędziem).

**Kluczowe Wymagania i Wskazówki:**

1.  **Wykorzystanie Vision LLM:** Należy użyć modelu zdolnego do analizy obrazów (np. GPT-4o, Gemini Vision).
2.  **Fragmenty Mapy:** Przygotować 4 fragmenty mapy (skany lub zrzuty ekranu). Ważna jest czytelność nazw ulic i charakterystycznych obiektów. Sugerowane jest czarno-białe zdjęcie o wysokim kontraście.
3.  **Potencjalny Błąd:** Jeden z fragmentów mapy może być błędny. Model powinien być w stanie to zidentyfikować lub przynajmniej skupić się na spójnych fragmentach.
4.  **Formułowanie Promptu:** Prompt powinien być jasny i precyzyjny, zawierający:
    *   Prośbę o identyfikację miasta.
    *   Informację o potencjalnie błędnym fragmencie.
    *   Prośbę o analizę nazw ulic, obiektów (cmentarze, kościoły, szkoły) i układu urbanistyczny.
    *   Wskazówkę, aby model weryfikował, czy rozpoznane elementy faktycznie znajdują się w proponowanym mieście.
    *   Możliwość uwzględnienia dodatkowych informacji z filmu (jeśli dostępne i przydatne).
5.  **Wysyłka Fragmentów:** Można wysłać wszystkie 4 fragmenty w jednym zapytaniu jako osobne obrazy.
6.  **Weryfikacja:** Model powinien upewnić się, że rozpoznane lokacje pasują do proponowanego miasta.
7.  **Format Flagi:** Nazwa miasta bez dodatkowych znaczników, formatowanie (wielkość liter, polskie znaki) nie ma znaczenia.
8.  **Optymalizacja Promptu:** Wskazówki dotyczące dostrajania promptu (jasność, precyzja, obsługa błędów, negatywne wskazówki) oraz parametrów modelu (temperatura).

**Proponowany Plan Działania:**

1.  **Przygotowanie Danych:** Zeskanować lub zrobić zrzuty ekranu 4 fragmentów mapy. Upewnić się, że są czytelne. Rozważyć konwersję do czerni i bieli dla lepszego kontrastu.
2.  **Wybór Narzędzia LLM:** Wybrać odpowiedni model Vision LLM (np. GPT-4o lub inny dostępny w ramach szkolenia/platformy).
3.  **Formułowanie Promptu:** Opracować szczegółowy prompt w języku polskim (lub angielskim, jeśli model lepiej działa w tym języku, z prośbą o odpowiedź po polsku), uwzględniając wszystkie kluczowe elementy z wymagań zadania (identyfikacja miasta, potencjalny błąd, analiza elementów mapy, weryfikacja).
4.  **Implementacja Kodu:** Napisać skrypt lub użyć narzędzia do interakcji z API wybranego LLM. Kod powinien:
    *   Wczytać przygotowane obrazy fragmentów mapy.
    *   Przygotować zapytanie do API, zawierające prompt tekstowy i dane obrazów.
    *   Wysłać zapytanie do API LLM.
    *   Odebrać i przetworzyć odpowiedź od modelu.
5.  **Analiza Odpowiedzi LLM:** Przeanalizować odpowiedź modelu. Model powinien wskazać miasto i potencjalnie zidentyfikować fragment, który nie pasuje.
6.  **Weryfikacja (Opcjonalna, ale Zalecana):** Jeśli odpowiedź modelu nie jest pewna lub wydaje się błędna, można spróbować:
    *   Dostosować prompt (np. dodać negatywną wskazówkę, jeśli model uparcie podaje błędne miasto).
    *   Zmienić parametry modelu (np. zmniejszyć temperaturę).
    *   Wykonać dodatkowe zapytania z innymi kombinacjami fragmentów (np. tylko 3 fragmenty, jeśli jeden wydaje się błędny).
7.  **Wyodrębnienie Flagi:** Z odpowiedzi modelu wyodrębnić nazwę miasta.
8.  **Przesłanie Flagi:** Wpisać nazwę miasta do pola "Znaleziona Flaga" w interfejsie użytkownika.

**Sugerowany Stos Technologiczny:**

1.  **Model LLM:** API modelu Vision, np. OpenAI GPT-4o, Google Gemini Vision, lub inny dostępny w ramach platformy szkoleniowej.
2.  **Język Programowania:** Python jest wysoce rekomendowany ze względu na bogactwo bibliotek do interakcji z API (np. `requests`, oficjalne SDK dostawców LLM) oraz łatwość przetwarzania danych.
3.  **Biblioteki Python:**
    *   `requests` lub oficjalne SDK dostawcy LLM (np. `openai`, `google-generativeai`) do komunikacji z API.
    *   `Pillow` (PIL) lub `opencv-python` do podstawowej obróbki obrazów (np. konwersja do formatu akceptowanego przez API, ewentualna zmiana kontrastu, choć często API akceptują popularne formaty obrazów bezpośrednio).
    *   Standardowe biblioteki do obsługi plików.

**Podsumowanie:**

Zadanie polega na zastosowaniu Vision LLM do rozwiązania problemu identyfikacji lokalizacji na podstawie obrazów mapy, z dodatkowym wyzwaniem w postaci potencjalnie błędnego fragmentu. Kluczem do sukcesu będzie odpowiednie przygotowanie danych obrazowych oraz precyzyjne sformułowanie promptu, kierującego model do analizy konkretnych elementów mapy i weryfikacji spójności fragmentów. Implementacja w Pythonie z wykorzystaniem odpowiednich bibliotek do komunikacji z API LLM wydaje się optymalnym podejściem.