## Plan Implementacji Single File Agent (SFA) dla Zadania Identyfikacji Miasta

Ten dokument przedstawia plan implementacji Single File Agent (SFA) w języku Python, oparty na dostarczonym schemacie `adw/sfa_bbs.py`, analizie zadania z `S02E02_analiza.md` oraz dodatkowych wyjaśnieniach dotyczących wymagań. Celem jest stworzenie skryptu, kt<PERSON>ry wykorzysta model Vision LLM do identyfikacji miasta na podstawie pojedynczego pliku obrazu mapy.

**Plik Docelowy:** `adw/sfa_bbs.py` (będziemy modyfikować ten plik, dostosowując go do specyfiki zadania)

**Kroki Implementacji:**

**1. Modyfikacja Konfiguracji i Usunięcie Niepotrzebnych Elementów**

* **Cel:** Uproszczenie skryptu poprzez usunięcie logiki związanej z Central API, która nie jest wymagana w tym zadaniu, oraz dostosowanie konfiguracji do używanego modelu LLM i ścieżki obrazu.
* **Działania:**
  * Usunąć lub zakomentować następujące stałe związane z Central API: `API_BASE_URL`, `DATA_ENDPOINT`, `REPORT_ENDPOINT`, `API_KEY`, `TASK_NAME`.
  * Usunąć lub zakomentować funkcje związane z Central API: `fetch_data`, `send_report`, `process_data`, `check_for_flag`.
  * Usunąć wywołania powyższych funkcji z bloku `main`.
  * Ustawić stałą `LLM_MODEL = "google/gemma-3-27b-it"`.
  * Dodać nową stałą definiującą ścieżkę do pliku obrazu mapy: `IMAGE_FILE_PATH = "fragmenty.jpeg"`.
  * Zmodyfikować funkcję `validate_configuration`, aby sprawdzała jedynie dostępność klucza API dla LLM (zmienne środowiskowe `LLM_API_KEY` lub `KLUSTER_API_KEY`).

**2. Implementacja Funkcji do Wczytywania i Kodowania Obrazu**

* **Cel:** Stworzenie dedykowanej funkcji do bezpiecznego wczytywania pliku obrazu i konwersji jego zawartości do formatu Base64, który jest wymagany przez API modeli Vision.
* **Działania:**
  * Dodać nową funkcję `read_and_encode_image(filepath: str) -> Optional[str]`.
  * Wewnątrz funkcji zaimplementować blok `try...except` do obsługi potencjalnych błędów odczytu pliku.
  * Otworzyć plik o podanej `filepath` w trybie binarnym (`'rb'`).
  * Odczytać całą zawartość pliku.
  * Użyć modułu `base64` do zakodowania odczytanych danych binarnych do stringu Base64.
  * Zwrócić zakodowany string Base64 w przypadku sukcesu.
  * W przypadku wystąpienia `FileNotFoundError`, `IOError` lub innego wyjątku podczas odczytu/kodowania, zalogować błąd informujący o problemie z plikiem i zwrócić `None`.

**3. Modyfikacja Funkcji `call_llm` do Obsługi Obrazów**

* **Cel:** Dostosowanie funkcji odpowiedzialnej za komunikację z API LLM, aby mogła przyjmować i poprawnie formatować dane obrazowe w zapytaniu, zgodnie ze standardem OpenAI API dla modeli Vision.
* **Działania:**
  * Zmodyfikować sygnaturę funkcji `call_llm`, dodając parametr `image_data_base64: Optional[str]`. Sygnatura powinna wyglądać np. tak: `call_llm(user_prompt: str, image_data_base64: Optional[str] = None, system_prompt_path: str = None, attempt: int = 1) -> Optional[str]`. Użycie `Optional` i wartości domyślnej `None` pozwoli na opcjonalne przekazywanie obrazu.
  * Wewnątrz funkcji, zmodyfikować sposób budowania listy `messages` dla roli 'user'. Zamiast prostego stringu, `content` powinien być listą elementów.
  * Jeśli `image_data_base64` nie jest `None`, dodać do listy `content` obiekt typu `image_url` z danymi obrazu w formacie `data:image/jpeg;base64,...`.
  * Zawsze dodać do listy `content` obiekt typu `text` z treścią `user_prompt`.
  * Przykład struktury `messages` z obrazem:

        ```python
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": [
                {"type": "text", "text": user_prompt},
                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_data_base64}"}}
            ]}
        ]
        ```

  * Zachować istniejącą logikę obsługi błędów API i ponawiania prób.

**4. Uproszczenie i Implementacja Logiki w Funkcji `main`**

* **Cel:** Zaimplementowanie głównego przepływu działania agenta, który będzie polegał na wczytaniu obrazu, wywołaniu modelu LLM z odpowiednim promptem i obrazem, a następnie zalogowaniu i wyświetleniu otrzymanej nazwy miasta.
* **Działania:**
  * W bloku `main`, po sprawdzeniu konfiguracji (`validate_configuration`), wywołać funkcję `read_and_encode_image`, przekazując `IMAGE_FILE_PATH`.
  * Sprawdzić wynik `read_and_encode_image`. Jeśli jest `None`, zalogować błąd i zakończyć działanie (`return`).
  * Zdefiniować zmienną `user_prompt` z krótką instrukcją dla modelu, np. `"Proszę przeanalizować załączony fragment mapy i zidentyfikować miasto zgodnie z instrukcjami systemowymi. Proszę podać wyłącznie nazwę miasta."`.
  * Wywołać zmodyfikowaną funkcję `call_llm`, przekazując `user_prompt`, zakodowany obraz Base64 oraz `SYSTEM_PROMPT_PATH`.
  * Sprawdzić wynik `call_llm`. Jeśli jest `None`, zalogować błąd i zakończyć działanie.
  * Jeśli odpowiedź od LLM (`llm_response`) została otrzymana, zalogować ją na poziomie INFO (np. `logger.info(f"Odpowiedź od LLM (nazwa miasta): {llm_response}")`).
  * Wypisać `llm_response` do standardowego wyjścia (konsoli), aby użytkownik mógł łatwo zobaczyć wynik.
  * Usunąć wszelką logikę związaną z iteracją zapytań do LLM i poszukiwaniem flagi w specyficznych formatach (`check_for_flag`). Odpowiedź LLM jest traktowana bezpośrednio jako nazwa miasta.

**5. Czyszczenie Kodu**

* **Cel:** Upewnienie się, że kod jest czysty, czytelny i pozbawiony nieużywanych elementów.
* **Działania:**
  * Przejrzeć cały plik `adw/sfa_bbs.py`.
  * Usunąć wszelkie importy, stałe i funkcje, które nie są już używane po modyfikacjach.
  * Dodać komentarze tam, gdzie logika może być mniej oczywista.
  * Upewnić się, że formatowanie kodu jest spójne.

**6. Weryfikacja System Promptu (`S02E02_sys_prompt.txt`)**

* **Cel:** Potwierdzenie, że system prompt jasno instruuje model, aby zwrócił wyłącznie nazwę miasta.
* **Działania:** Upewnić się, że treść pliku `S02E02_sys_prompt.txt` zawiera instrukcję typu "Twoją odpowiedzią powinna być wyłącznie nazwa zidentyfikowanego miasta, bez żadnych dodatkowych komentarzy, znaczników czy formatowania". (Ten krok został już wykonany i potwierdzony).

Ten plan zapewnia, że finalny skrypt SFA będzie minimalistyczny, skupiony na zadaniu, wykorzystujący Vision LLM do analizy obrazu mapy i zwracający oczekiwaną nazwę miasta jako wynik.
