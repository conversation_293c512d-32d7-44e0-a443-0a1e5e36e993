"""
Single File Agent (SFA) - City Identification Task

This script identifies a city based on a map image using a Vision LLM.
It reads a local image file, encodes it to Base64, sends it to the LLM
via the OpenAI API standard (compatible with Kluster.ai), and prints
the identified city name.

Based on SFA-BBS template (adw/sfa_bbs.py) and task analysis (S02E02_analiza.md, S02E02_IM_plan.md).

Author: Augment Agent
Date: 2024-07-30
Version: 1.0.0
"""

# --- IMPORTS ---
import os
import json
import logging
import time
import requests
import base64 # Import base64 for image encoding
from typing import Dict, List, Any, Tuple, Optional, Union
from dotenv import load_dotenv
from openai import OpenAI, APIError, APIConnectionError, RateLimitError

# --- CONFIGURATION ---
# Load environment variables
load_dotenv()

# --- APPLICATION SETTINGS ---
# Task-specific settings
IMAGE_FILE_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fragmenty.jpeg") # Path to the map image file

# Request settings (primarily for LLM calls now)
REQUEST_TIMEOUT = 30  # Increased timeout for LLM calls with images
RETRY_ATTEMPTS = 2
RETRY_DELAY = 5  # Increased delay

# --- LLM SETTINGS ---
# Use KLUSTER_ or OPENAI_ env vars
LLM_MODEL = os.getenv("KLUSTER_MULTIMODAL_MODEL", os.getenv("OPENAI_MULTIMODAL_MODEL")) # Default to specified model
LLM_API_KEY = os.getenv("KLUSTER_API_KEY", os.getenv("OPENAI_API_KEY"))
LLM_BASE_URL = os.getenv("KLUSTER_BASE_URL", os.getenv("OPENAI_BASE_URL"))
LLM_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", "4000"))
LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.3"))
LLM_TOP_P = float(os.getenv("LLM_TOP_P", "1.0"))

# System prompt path - load from file
SYSTEM_PROMPT_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "S02E02_sys_prompt.txt")

# Default system prompt (used if file not found)
DEFAULT_SYSTEM_PROMPT = """
You are an AI assistant helping with a specific task.
Provide clear, concise, and accurate responses.
"""

# --- LOGGING CONFIGURATION ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# --- HELPER FUNCTIONS ---

def validate_configuration() -> bool:
    """
    Validates that required configuration variables for LLM are set.

    Returns:
        bool: True if configuration is valid, False otherwise
    """
    # Check if LLM_API_KEY is set (either via KLUSTER_API_KEY or OPENAI_API_KEY)
    if not LLM_API_KEY:
        logger.critical("LLM_API_KEY is not set. Please set KLUSTER_API_KEY or OPENAI_API_KEY environment variable.")
        return False

    # LLM_BASE_URL is optional for some providers, but good to warn if not set when KLUSTER is implied
    if "kluster" in (LLM_BASE_URL or "").lower():
         logger.info(f"Using LLM base URL: {LLM_BASE_URL}")
    elif LLM_BASE_URL:
         logger.info(f"Using LLM base URL: {LLM_BASE_URL}")
    else:
         logger.warning("LLM_BASE_URL is not set. Using default OpenAI base URL.")


    logger.info("Configuration validated successfully.")
    return True

def read_and_encode_image(filepath: str) -> Optional[str]:
    """
    Reads an image file and encodes its content to a Base64 string.

    Args:
        filepath: The path to the image file.

    Returns:
        Optional[str]: The Base64 encoded image string, or None if an error occurred.
    """
    logger.info(f"Attempting to read and encode image file: {filepath}")
    try:
        with open(filepath, 'rb') as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
        logger.info(f"Successfully read and encoded image: {filepath}")
        return encoded_string
    except FileNotFoundError:
        logger.critical(f"Error: Image file not found at {filepath}")
        return None
    except IOError as e:
        logger.critical(f"Error reading image file {filepath}: {e}")
        return None
    except Exception as e:
        logger.critical(f"An unexpected error occurred while processing image {filepath}: {e}")
        return None


def load_system_prompt(filepath: str) -> str:
    """
    Wczytuje system prompt z pliku.

    Args:
        filepath: The path to the system prompt file.

    Returns:
        str: Zawartość pliku z system promptem.
    """
    try:
        with open(filepath, 'r', encoding='utf-8') as file:
            system_prompt = file.read()
        logger.info(f"Wczytano system prompt z pliku: {filepath}")
        return system_prompt
    except FileNotFoundError:
        logger.error(f"Błąd: Plik system promptu nie znaleziony pod ścieżką: {filepath}")
        logger.warning("Używanie domyślnego system promptu")
        return DEFAULT_SYSTEM_PROMPT
    except Exception as e:
        logger.error(f"Błąd podczas wczytywania system promptu z pliku {filepath}: {e}")
        logger.warning("Używanie domyślnego system promptu")
        return DEFAULT_SYSTEM_PROMPT

def call_llm(user_prompt: str, image_data_base64: Optional[str] = None, system_prompt_filepath: str = None, attempt: int = 1) -> Optional[str]:
    """
    Calls the LLM with the given prompt and optional image data, handles errors with retry logic.
    Uses system prompt from file if provided, otherwise loads from SYSTEM_PROMPT_PATH.

    Args:
        user_prompt: The user prompt to send to the LLM.
        image_data_base64: Optional Base64 encoded image data string.
        system_prompt_filepath: Path to system prompt file (optional, overrides default SYSTEM_PROMPT_PATH).
        attempt: Current attempt number (for retry logic).

    Returns:
        Optional[str]: The LLM's response text, or None if failed.
    """
    logger.info(f"Calling LLM (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    try:
        # Load system prompt from specified file or default path
        system_prompt = load_system_prompt(system_prompt_filepath if system_prompt_filepath else SYSTEM_PROMPT_PATH)

        client_params = {"api_key": LLM_API_KEY}
        if LLM_BASE_URL:
            client_params["base_url"] = LLM_BASE_URL

        client = OpenAI(**client_params)

        logger.info(f"Using model: {LLM_MODEL} via API: {LLM_BASE_URL if LLM_BASE_URL else 'default OpenAI'}")

        # Construct messages content, including image if provided
        messages_content = [{"type": "text", "text": user_prompt}]
        if image_data_base64:
             # Assuming JPEG format based on task description (fragmenty.jpeg)
            messages_content.append({"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_data_base64}"}})
            logger.info("Including image data in LLM request.")
        else:
            logger.warning("No image data provided for LLM call.")


        completion = client.chat.completions.create(
            model=LLM_MODEL,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": messages_content}
            ],
            temperature=LLM_TEMPERATURE,
            max_tokens=LLM_MAX_TOKENS, # Use max_tokens for completion limit
            top_p=LLM_TOP_P
        )

        response = completion.choices[0].message.content.strip()
        logger.info("LLM call successful")
        return response

    except (APIError, APIConnectionError, RateLimitError) as e:
        logger.error(f"LLM API error: {e}")

        # Retry logic
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return call_llm(user_prompt, image_data_base64, system_prompt_filepath, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except Exception as e:
        logger.error(f"Unexpected error during LLM call: {e}")
        return None

# --- MAIN FUNCTION ---

def main():
    """
    Main function that orchestrates the agent's workflow for city identification.
    """
    logger.info("--- Starting City Identification Agent ---")

    # Validate configuration
    if not validate_configuration():
        logger.critical("Invalid configuration. Exiting.")
        return

    try:
        # Step 1: Read and encode the image file
        image_data_base64 = read_and_encode_image(IMAGE_FILE_PATH)
        if not image_data_base64:
            logger.critical(f"Failed to process image file: {IMAGE_FILE_PATH}. Exiting.")
            return

        # Step 2: Define the user prompt
        # This prompt complements the system prompt by providing immediate task context
        user_prompt = "Proszę przeanalizować załączony fragment mapy i zidentyfikować miasto zgodnie z instrukcjami systemowymi. Proszę podać wyłącznie nazwę miasta."
        logger.info(f"User prompt: {user_prompt}")

        # Step 3: Call LLM with image and prompts
        llm_response = call_llm(
            user_prompt=user_prompt,
            image_data_base64=image_data_base64,
            system_prompt_filepath=SYSTEM_PROMPT_PATH # Explicitly pass the system prompt path
        )

        # Step 4: Process and display the LLM response
        if not llm_response:
            logger.error("Failed to get a valid response from LLM. Exiting.")
            return

        # According to task requirements, the LLM response IS the city name (the flag)
        identified_city = llm_response.strip() # Ensure no leading/trailing whitespace

        logger.info(f"Identified City (Flag): {identified_city}")

        # Display the identified city clearly in the console
        print(f"\n{'='*50}")
        print(f"IDENTIFIED CITY (FLAG): {identified_city}")
        print(f"{'='*50}\n")

    except Exception as e:
        logger.critical(f"An unexpected error occurred during agent execution: {e}", exc_info=True)

    logger.info("--- City Identification Agent finished ---")

# --- SCRIPT ENTRY POINT ---

if __name__ == "__main__":
    main()
