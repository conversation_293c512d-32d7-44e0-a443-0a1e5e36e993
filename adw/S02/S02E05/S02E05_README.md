# S02E05: Arxiv Article Analysis and Question Answering Agent

**Author:** Augment Agent  
**Date:** May 25, 2025  
**Version:** 1.0.0

---

## 🚀 Overview

This Python-based Single File Agent (SFA) performs multimodal analysis of an Arxiv-like article. It fetches an article from a given URL, processes its text, images, and audio components, consolidates this information into a comprehensive context, and then uses this context to answer a series of questions. The final answers are submitted to a central API.

The agent is modular, allowing for individual processing steps (e.g., image description, audio transcription) to be run independently. It also features caching mechanisms to save intermediate results and avoid redundant processing.

---

## ✨ Features

- **Multimodal Article Processing:**
  - Fetches HTML articles.
  - Extracts and cleans textual content (optionally using an LLM).
  - Downloads images and generates detailed descriptions using a vision LLM (Google Gemma via Kluster.ai).
  - Downloads MP3 audio files and transcribes them using AssemblyAI.
- **Context Consolidation:** Merges processed text, image descriptions, and audio transcriptions into a single Markdown file for comprehensive understanding.
- **Intelligent Question Answering:**
  - Fetches questions from a specified API endpoint.
  - Utilizes a powerful LLM (DeepSeek via Kluster.ai) with the consolidated context to provide accurate answers.
  - Answers are formatted as concise, single sentences.
- **API Interaction:**
  - Submits final answers to a reporting endpoint.
  - Checks for a "flag" in the server's response.
- **Caching System:** Caches intermediate results (text, image descriptions, audio transcriptions, questions, answers, consolidated context) in the `arxiv_cache` directory to optimize performance and allow for step-wise execution.
- **Modular Execution:** Supports command-line arguments to run specific parts of the workflow (e.g., `--image` for image processing only, `--answer` for question answering only).
- **Configuration via Environment Variables:** Securely manages API keys and other settings.
- **Robust Error Handling:** Implements retry logic for API calls and detailed logging.

---

## ⚙️ Workflow

The agent can operate in a full workflow mode or execute specific tasks:

### 1. Full Workflow

```bash
python S02E05_sfa.py
```

- Fetches the HTML article.
- Processes text content:
  - Parses HTML to Markdown.
  - (Optionally) Cleans text using an LLM.
  - Saves to `arxiv_cache/text1.md` and potentially `arxiv_cache/text1formatted.txt`.
- Parses HTML for image and audio URLs.
- Downloads images to `arxiv_cache/temp_media/images/`.
- Generates descriptions for each image using a vision LLM and caches them in `arxiv_cache/images.json`.
- Downloads audio files to `arxiv_cache/temp_media/audio/`.
- Transcribes each audio file using AssemblyAI and caches transcriptions in `arxiv_cache/audio.json`.
- Consolidates all processed information into `arxiv_cache/consolidated_context.md`.
- Fetches questions from the API and caches them in `arxiv_cache/questions.json` (raw in `questions.txt`).
- Answers the questions using the consolidated context and an LLM, caching answers in `arxiv_cache/answers.json`.
- Formats the final answers.
- Submits the answers to the central reporting API and prints the server's response, checking for a flag.

### 2. Modular Workflows (via command-line arguments)

- `--image`: Downloads and describes images, saving to `arxiv_cache/images.json`.
- `--sound`: Downloads and transcribes audio, saving to `arxiv_cache/audio.json`.
- `--text`: Fetches, cleans, and formats the article text, saving to `arxiv_cache/text1.md` (and `arxiv_cache/text1formatted.txt` if LLM cleaning is performed).
- `--context`: Consolidates context from existing cached text, image, and audio data into `arxiv_cache/consolidated_context.md`.
- `--answer`: Fetches questions and answers them using the cached `consolidated_context.md`, saving to `arxiv_cache/answers.json`.
- `--central`: Submits answers from the cached `arxiv_cache/answers.json` to the central API.

---

## 📋 Prerequisites

- Python 3.8+
- Access to the internet for fetching articles, media, and interacting with APIs.
- API Keys for:
  - AIDEVS (Central API)
  - Kluster.ai (for LLM and Vision Models)
  - AssemblyAI (for Audio Transcription)

---

## 🛠️ Setup

1. **Clone the repository (or download the files):**

    ```bash
    # If it's part of a larger project
    # git clone <repository_url>
    # cd <project_directory>/S02E05
    ```

2. **Install dependencies:**
    Create a `requirements.txt` file with the following content:

    ```text
    python-dotenv
    requests
    assemblyai
    openai
    Pillow
    beautifulsoup4
    html2text
    ```

    Then install them:

    ```bash
    pip install -r requirements.txt
    ```

3. **Set up Environment Variables:**
    Create a `.env` file in the same directory as `S02E05_sfa.py` with your API keys and other configurations:

    ```env
    AIDEVS_API_KEY="your_aidevs_api_key"
    KLUSTER_API_KEY="your_kluster_api_key"
    ASSEMBLY_API_KEY="your_assemblyai_api_key"

    # Optional: Override default API base URLs or settings
    # API_BASE_URL="https://c3ntrala.ag3nts.org"
    # KLUSTER_BASE_URL="https://api.kluster.ai/v1"
    # REQUEST_TIMEOUT="30"
    # RETRY_ATTEMPTS="2"
    # RETRY_DELAY="2"
    # LLM_MAX_TOKENS="4000"
    # LLM_TEMPERATURE="0.1"
    ```

    Replace `your_..._api_key` with your actual API keys.

---

## 🚀 Usage

### Running the Full Workflow

```bash
python S02E05_sfa.py
```

### Running Specific Parts of the Workflow

You can run individual stages using command-line flags. This is useful for debugging or re-running specific parts if others have completed successfully (leveraging cached data).

- **Process Images Only:**

    ```bash
    python S02E05_sfa.py --image
    ```

- **Process Audio Only:**

    ```bash
    python S02E05_sfa.py --sound
    ```

- **Process Text Only:**  
  (This will create `text1.md` and potentially `text1formatted.txt` if LLM cleaning is triggered)

    ```bash
    python S02E05_sfa.py --text
    ```

- **Consolidate Context from Cache:**  
  (Requires `text1.md`, `images.json`, `audio.json` to be present in `arxiv_cache/`)

    ```bash
    python S02E05_sfa.py --context
    ```

- **Answer Questions using Cached Context:**  
  (Requires `consolidated_context.md` in `arxiv_cache/`; will fetch questions if `questions.json` is not present)

    ```bash
    python S02E05_sfa.py --answer
    ```

- **Submit Cached Answers to Central API:**  
  (Requires `answers.json` to be present in `arxiv_cache/`)

    ```bash
    python S02E05_sfa.py --central
    ```

---

## ⚙️ Configuration Details

### Environment Variables

The script relies on the following environment variables, typically set in a `.env` file:

- `AIDEVS_API_KEY` (**Required**): API key for the AIDEVS central server.
- `KLUSTER_API_KEY` (**Required**): API key for Kluster.ai (provides Gemma and DeepSeek models).
- `ASSEMBLY_API_KEY` (**Required**): API key for AssemblyAI (audio transcription).
- `API_BASE_URL` (Optional): Base URL for the AIDEVS central server. Defaults to `https://c3ntrala.ag3nts.org`.
- `REQUEST_TIMEOUT` (Optional): Timeout for HTTP requests in seconds. Defaults to 30.
- `RETRY_ATTEMPTS` (Optional): Number of retry attempts for failed API calls. Defaults to 2.
- `RETRY_DELAY` (Optional): Delay between retry attempts in seconds. Defaults to 2.
- `KLUSTER_BASE_URL` (Optional): Base URL for the Kluster.ai API. Defaults to `https://api.kluster.ai/v1`.
- `LLM_MAX_TOKENS` (Optional): Maximum tokens for LLM responses. Defaults to 4000.
- `LLM_TEMPERATURE` (Optional): Temperature for LLM response generation (lower values like 0.1 produce more deterministic/factual answers). Defaults to 0.1.

### Hardcoded Configuration (Notable Constants in the Script)

- `TASK_NAME`: Set to `arxiv`.
- `ARTICLE_URL`: URL of the Arxiv-like article to process. Defaults to `https://c3ntrala.ag3nts.org/dane/arxiv-draft.html`.
- `IMAGE_MODEL`: LLM used for image description via Kluster.ai. Defaults to `google/gemma-3-27b-it`.
- `TEXT_MODEL`: LLM used for text cleaning and question answering via Kluster.ai. Defaults to `deepseek-ai/DeepSeek-V3-0324`.
- `SYSTEM_PROMPT_PATH`: Path to the system prompt file. Defaults to `S02E05_sys_prompt.txt` in the same directory as the script.
- `TEXT_CLEANING_PROMPT`: Prompt used for LLM-based text cleaning.

### System Prompt (`S02E05_sys_prompt.txt`)

This file contains detailed instructions for the LLM (DeepSeek) used in the question-answering phase. It guides the AI to:

- Act as an advanced agent analyzing multimodal data.
- Base answers strictly on the provided consolidated Markdown context.
- Understand that the context includes processed article text, image descriptions, and audio transcriptions.
- Provide answers in a specific JSON format: `{ "ID-pytania-01": "krótka odpowiedź w 1 zdaniu", ... }`.
- Ensure answers are concise (single sentence), accurate, and faithful to the source material.
- Analyze and justify its reasoning before providing the final answer (though the script primarily extracts the tagged answer).

### Cache Directory (`arxiv_cache/`)

The agent creates and utilizes an `arxiv_cache/` directory in its working path to store intermediate and final outputs. This speeds up subsequent runs and allows for modular execution.

- `text1.md`: The main textual content of the article, converted from HTML to Markdown by html2text.
- `text1formatted.txt` (Optional): Text content after being cleaned by an LLM (if the `--text` workflow triggers this).
- `images.json`: A JSON file mapping downloaded image filenames (e.g., `image1.png`) to their LLM-generated descriptions.
- `audio.json`: A JSON file mapping downloaded audio filenames (e.g., `audio1.mp3`) to their transcriptions from AssemblyAI.
- `consolidated_context.md`: A Markdown file combining the article text, image descriptions, and audio transcriptions. This serves as the primary input for the question-answering LLM.
- `questions.txt`: Raw text of questions fetched from the API.
- `questions.json`: Questions processed into a JSON list of dictionaries (e.g., `[ {"ID-pytania-01": "Question text?"}, ... ]`).
- `answers.json`: The final answers generated by the LLM, stored as a JSON object (e.g., `{ "ID-pytania-01": "Answer.", ... }`).
- `temp_media/`:
  - `images/`: Contains downloaded image files (e.g., `.png`, `.jpg`).
  - `audio/`: Contains downloaded audio files (e.g., `.mp3`).

---

## 📄 File Structure

```text
.
├── S02E05_sfa.py             # Main agent script
├── S02E05_sys_prompt.txt     # System prompt for the LLM
├── .env                      # Environment variables (user-created, gitignored)
├── requirements.txt          # Python dependencies (user-created)
└── arxiv_cache/              # Directory for cached files (created by the script)
    ├── text1.md
    ├── text1formatted.txt    # (Optional, if LLM text cleaning occurs)
    ├── images.json
    ├── audio.json
    ├── consolidated_context.md
    ├── questions.txt
    ├── questions.json
    ├── answers.json
    └── temp_media/
        ├── images/
        │   └── ... (downloaded image files)
        └── audio/
            └── ... (downloaded audio files)
```

---

This `README.md` should provide a good understanding of your agent's capabilities and how to use it.
