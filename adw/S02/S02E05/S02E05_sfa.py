"""
Single File Agent - S02E05: Arxiv Article Analysis and Question Answering
Analiza multimodalnego artykułu (tekst, gra<PERSON><PERSON>, d<PERSON><PERSON><PERSON><PERSON>) i odpowiadanie na pytania.

Author: Augment Agent
Date: May 25, 2025
Version: 1.0.0
"""

# --- IMPORTS ---
import os
import json
import argparse
import base64
import re
import time
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union

# Environment variables
from dotenv import load_dotenv

# HTTP & API clients
import requests
import assemblyai as aai
from openai import OpenAI, APIError, APIConnectionError, RateLimitError

# File processing & HTML parsing
from PIL import Image
import io
from bs4 import BeautifulSoup
import html2text

# --- CONFIGURATION ---
# Load environment variables
load_dotenv()

# --- APPLICATION SETTINGS ---
TASK_NAME = "arxiv"
API_BASE_URL = os.getenv("API_BASE_URL", "https://c3ntrala.ag3nts.org")
ARTICLE_URL = "https://c3ntrala.ag3nts.org/dane/arxiv-draft.html"
QUESTIONS_ENDPOINT = f"{API_BASE_URL}/data/{{API_KEY}}/arxiv.txt" # API_KEY will be replaced
REPORT_ENDPOINT = f"{API_BASE_URL}/report"
AIDEVS_API_KEY = os.getenv("AIDEVS_API_KEY")

REQUEST_TIMEOUT = int(os.getenv("REQUEST_TIMEOUT", "30")) # seconds
RETRY_ATTEMPTS = int(os.getenv("RETRY_ATTEMPTS", "2"))
RETRY_DELAY = int(os.getenv("RETRY_DELAY", "2")) # seconds

# --- LLM SETTINGS ---
KLUSTER_API_KEY = os.getenv("KLUSTER_API_KEY")
KLUSTER_BASE_URL = os.getenv("KLUSTER_BASE_URL", "https://api.kluster.ai/v1")
ASSEMBLY_API_KEY = os.getenv("ASSEMBLY_API_KEY")

IMAGE_MODEL = "google/gemma-3-27b-it"
TEXT_MODEL = "deepseek-ai/DeepSeek-V3-0324"
LLM_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", "4000"))
LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.1")) # Lower for more precise answers

SYSTEM_PROMPT_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "S02E05_sys_prompt.txt")
TEXT_CLEANING_PROMPT = """
Jesteś ekspertem od przetwarzania tekstu. Twoim zadaniem jest oczyszczenie i sformatowanie dostarczonego tekstu. Usuń wszelkie zbędne białe znaki, tagi HTML, fragmenty kodu, nagłówki i stopki, oraz inne elementy, które nie są częścią głównej, merytorycznej treści. Zachowaj spójność i czytelność tekstu. Zwróć tylko oczyszczony i sformatowany tekst, bez żadnych dodatkowych komentarzy.
"""

# --- REGEX PATTERNS ---
FLAG_REGEX = r"FLG[a-zA-Z0-9_-]+"
FLAG_REGEX_CURLY = r"\{\{FLG:[a-zA-Z0-9_-]+\}\}"
ANSWER_TAG_REGEX = r"<answer>(.*?)</answer>" # For extracting answer from LLM response

# --- LOGGING CONFIGURATION ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# --- HELPER FUNCTIONS ---

def validate_configuration() -> bool:
    """
    Validates that all required configuration variables are set.
    Returns:
        bool: True if configuration is valid, False otherwise
    """
    required_env_vars = {
        "AIDEVS_API_KEY": "API key for Central API",
        "KLUSTER_API_KEY": "API key for Kluster.ai",
        "ASSEMBLY_API_KEY": "API key for AssemblyAI",
    }
    missing_vars = []

    for var, description in required_env_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"{var} ({description})")

    if missing_vars:
        logger.critical(f"Missing required environment variables: {', '.join(missing_vars)}")
        return False
    
    logger.info("Configuration validation successful")
    return True

def load_system_prompt(path: str) -> str:
    """
    Wczytuje system prompt z pliku.
    Args:
        path (str): Ścieżka do pliku z system promptem.
    Returns:
        str: Zawartość pliku z system promptem.
    """
    try:
        with open(path, 'r', encoding='utf-8') as file:
            system_prompt = file.read().strip()
        logger.info(f"Wczytano system prompt z pliku: {path}")
        return system_prompt
    except Exception as e:
        logger.error(f"Błąd podczas wczytywania system promptu z {path}: {e}")
        return "You are a helpful AI assistant." # Fallback default

def call_llm(prompt: str, system_prompt: str, model: str, api_key: str, base_url: str, max_tokens: int, temperature: float, attempt: int = 1) -> Optional[str]:
    """
    Calls the LLM with the given prompt and handles errors with retry logic.
    Args:
        prompt (str): The user prompt to send to the LLM.
        system_prompt (str): The system prompt for the LLM.
        model (str): The LLM model to use.
        api_key (str): API key for the LLM provider.
        base_url (str): Base URL for the LLM API.
        max_tokens (int): Maximum tokens for the LLM response.
        temperature (float): Temperature for LLM response generation.
        attempt (int): Current attempt number (for retry logic).
    Returns:
        Optional[str]: The LLM's response, or None if failed.
    """
    logger.info(f"Calling LLM ({model}) with prompt (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    try:
        client_params = {"api_key": api_key}
        if base_url:
            client_params["base_url"] = base_url

        client = OpenAI(**client_params)

        completion = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            temperature=temperature,
            max_tokens=max_tokens # Use max_tokens for completion
        )

        response = completion.choices[0].message.content.strip()
        logger.info("LLM call successful")
        return response

    except (APIError, APIConnectionError, RateLimitError) as e:
        logger.error(f"LLM API error: {e}")
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return call_llm(prompt, system_prompt, model, api_key, base_url, max_tokens, temperature, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except Exception as e:
        logger.error(f"Unexpected error during LLM call: {e}")
        return None

def send_report(url: str, api_key: str, task_name: str, answer: Any, attempt: int = 1) -> Optional[Dict]:
    """
    Sends the final report/answer to the specified endpoint.
    Args:
        url (str): The URL to send the report to.
        api_key (str): API key for authentication.
        task_name (str): Name of the task being performed.
        answer (Any): The answer/data to send.
        attempt (int): Current attempt number (for retry logic).
    Returns:
        Optional[Dict]: The response from the server, or None if failed.
    """
    logger.info(f"Sending report to {url} (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    payload = {
        "task": task_name,
        "apikey": api_key,
        "answer": answer
    }

    headers = {
        "Content-Type": "application/json; charset=utf-8"
    }

    logger.info(f"Sending payload: {json.dumps(payload, ensure_ascii=False)}")

    try:
        response = requests.post(
            url,
            json=payload,
            headers=headers,
            timeout=REQUEST_TIMEOUT
        )
        response.raise_for_status()

        result = response.json()
        logger.info("Report sent successfully")
        return result

    except requests.exceptions.RequestException as e:
        logger.error(f"Error sending report: {e}")
        if hasattr(e, 'response') and hasattr(e.response, 'text'):
            logger.error(f"Server response: {e.response.text}")
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return send_report(url, api_key, task_name, answer, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error during report submission: {e}")
        return None

def check_for_flag(response: Any) -> Optional[str]:
    """
    Sprawdza, czy odpowiedź zawiera flagę w formatach:
    - FLG[a-zA-Z0-9_-]+
    - {{FLG:[a-zA-Z0-9_-]+}}
    Args:
        response (Any): Odpowiedź do sprawdzenia (może być string, dict lub inny typ).
    Returns:
        Optional[str]: Znaleziona flaga lub None, jeśli nie znaleziono.
    """
    response_str = str(response)
    if not response_str:
        logger.info("Empty response, no flag to check.")
        return None

    match_curly = re.search(FLAG_REGEX_CURLY, response_str)
    if match_curly:
        flag = match_curly.group(0)
        logger.info(f"Flag found (curly format): {flag}")
        return flag

    match = re.search(FLAG_REGEX, response_str)
    if match:
        flag = match.group(0)
        logger.info(f"Flag found (simple format): {flag}")
        return flag

    if "flag" in response_str.lower() or "flg" in response_str.lower():
        logger.info(f"Response contains flag-related text: {response_str}")
        
    logger.info("No flag found in response.")
    return None

class ArxivProcessor:
    """
    Main SFA class implementing the arxiv article processing and question answering workflow.
    """
    
    # Configuration constants
    CACHE_DIR = "arxiv_cache"
    IMAGE_CACHE_FILE = os.path.join(CACHE_DIR, "images.json")
    AUDIO_CACHE_FILE = os.path.join(CACHE_DIR, "audio.json")
    TEXT_RAW_FILE = os.path.join(CACHE_DIR, "text1.txt")
    TEXT_FORMATTED_FILE = os.path.join(CACHE_DIR, "text1formatted.txt")
    CONSOLIDATED_CONTEXT_FILE = os.path.join(CACHE_DIR, "consolidated_context.md")
    FINAL_ANSWERS_FILE = os.path.join(CACHE_DIR, "answers.json")

    def __init__(self):
        """
        Inicjalizuje procesor, ładuje konfigurację i system prompt.
        """
        self.aidevs_api_key = AIDEVS_API_KEY
        self.kluster_api_key = KLUSTER_API_KEY
        self.assembly_api_key = ASSEMBLY_API_KEY
        self.kluster_base_url = KLUSTER_BASE_URL
        self.system_prompt = load_system_prompt(SYSTEM_PROMPT_PATH)
        aai.settings.api_key = self.assembly_api_key
        os.makedirs(self.CACHE_DIR, exist_ok=True) # Ensure cache directory exists
        os.makedirs(os.path.join(self.CACHE_DIR, "temp_media", "images"), exist_ok=True) # For downloaded images
        os.makedirs(os.path.join(self.CACHE_DIR, "temp_media", "audio"), exist_ok=True) # For downloaded audio

    # --- Core Workflow Methods ---
    def run_full_workflow(self) -> None:
        """
        Orkiestruje cały proces: pobieranie, przetwarzanie, odpowiadanie, wysyłanie.
        """
        logger.info("=== Starting full workflow ===")
        html_content = self._fetch_html_article(ARTICLE_URL)
        if not html_content:
            logger.error("Failed to fetch article HTML. Exiting full workflow.")
            return

        # Process text
        text_file_path = self._process_text_content(html_content)
        if not text_file_path:
            logger.error("Failed to process text content. Exiting full workflow.")
            return

        # Parse HTML for media URLs
        _, image_urls, audio_urls = self._parse_html_content(html_content, ARTICLE_URL)

        # Download media
        downloaded_images = self._download_media(image_urls, "images", os.path.join(self.CACHE_DIR, "temp_media", "images"))
        downloaded_audio = self._download_media(audio_urls, "audio", os.path.join(self.CACHE_DIR, "temp_media", "audio"))

        # Process images (descriptions)
        image_descriptions = {}
        for img_path in downloaded_images:
            img_name = Path(img_path).name
            cached_data = self._load_cache(self.IMAGE_CACHE_FILE)
            if img_name in cached_data:
                image_descriptions[img_name] = cached_data[img_name]
                logger.info(f"Loaded image description for {img_name} from cache.")
            else:
                # Placeholder for context_text, ideally extracted from HTML near image
                context_text = f"Image {img_name} from article." 
                description = self._describe_image(img_path, context_text)
                if description:
                    image_descriptions[img_name] = description
                    cached_data[img_name] = description
                    self._save_cache(self.IMAGE_CACHE_FILE, cached_data)
                    logger.info(f"Generated and cached description for {img_name}.")
                else:
                    logger.warning(f"Failed to describe image {img_name}.")

        # Process audio (transcriptions)
        audio_transcriptions = {}
        for aud_path in downloaded_audio:
            aud_name = Path(aud_path).name
            cached_data = self._load_cache(self.AUDIO_CACHE_FILE)
            if aud_name in cached_data:
                audio_transcriptions[aud_name] = cached_data[aud_name]
                logger.info(f"Loaded audio transcription for {aud_name} from cache.")
            else:
                transcription = self._transcribe_audio(aud_path)
                if transcription:
                    audio_transcriptions[aud_name] = transcription
                    cached_data[aud_name] = transcription
                    self._save_cache(self.AUDIO_CACHE_FILE, cached_data)
                    logger.info(f"Generated and cached transcription for {aud_name}.")
                else:
                    logger.warning(f"Failed to transcribe audio {aud_name}.")

        # Consolidate context
        consolidated_context = self._consolidate_context(text_file_path, image_descriptions, audio_transcriptions)
        if not consolidated_context:
            logger.error("Failed to consolidate context. Exiting full workflow.")
            return
        
        # Fetch questions
        questions = self._fetch_questions(self.aidevs_api_key)
        if not questions:
            logger.error("Failed to fetch questions. Exiting full workflow.")
            return

        # Answer questions
        answers = self._answer_questions(questions, consolidated_context)
        if not answers:
            logger.error("Failed to answer questions. Exiting full workflow.")
            return

        # Format and submit
        formatted_answer = self._format_final_answer(answers)
        response_from_centrala = self._submit_answer(formatted_answer)
        
        if response_from_centrala:
            flag = check_for_flag(response_from_centrala)
            if flag:
                print(f"\n🎉 FLAG FOUND: {flag}")
            else:
                print(f"\n📋 Centrala Response: {response_from_centrala}")
        else:
            logger.error("Failed to get response from centrala.")

    # --- Data Fetching Methods ---
    def _fetch_html_article(self, url: str) -> Optional[str]:
        """
        Pobiera zawartość HTML artykułu z podanego URL.
        Args:
            url (str): Adres URL artykułu HTML.
        Returns:
            Optional[str]: Zawartość HTML jako string, lub None w przypadku błędu.
        """
        logger.info(f"Fetching HTML article from: {url}")
        try:
            response = requests.get(url, timeout=REQUEST_TIMEOUT)
            response.raise_for_status()
            return response.text
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching article HTML from {url}: {e}")
            return None

    def _fetch_questions(self, api_key: str) -> Optional[Dict[str, str]]:
        """
        Pobiera pytania od centrali z podanego endpointu API, zapisuje do pliku txt,
        a następnie przetwarza i zapisuje do pliku json.
        Args:
            api_key (str): Klucz API do autentykacji.
        Returns:
            Optional[Dict[str, str]]: Słownik pytań (ID: treść), lub None w przypadku błędu.
        """
        questions_url = QUESTIONS_ENDPOINT.replace("{API_KEY}", api_key)
        questions_txt_path = os.path.join(self.CACHE_DIR, "questions.txt")
        questions_json_path = os.path.join(self.CACHE_DIR, "questions.json")

        # Check if questions.json already exists and is not empty
        if os.path.exists(questions_json_path) and os.path.getsize(questions_json_path) > 0:
            logger.info(f"Questions JSON already exists: {questions_json_path}. Loading from cache.")
            try:
                with open(questions_json_path, 'r', encoding='utf-8') as f:
                    questions_list = json.load(f)
                # Convert list of {"ID": "text"} to Dict[str, str]
                questions_dict = {list(q.keys())[0]: list(q.values())[0] for q in questions_list}
                logger.info(f"Loaded {len(questions_dict)} questions from cache.")
                return questions_dict
            except (json.JSONDecodeError, IndexError, KeyError) as e:
                logger.error(f"Error loading questions from cache {questions_json_path}: {e}. Attempting to refetch.")
                # Fall through to fetching logic if cache is corrupted

        logger.info(f"Fetching questions from: {questions_url}")
        try:
            response = requests.get(questions_url, timeout=REQUEST_TIMEOUT)
            response.raise_for_status()
            questions_raw_text = response.text.strip()

            # Save raw text to questions.txt
            with open(questions_txt_path, 'w', encoding='utf-8') as f:
                f.write(questions_raw_text)
            logger.info(f"Saved raw questions text to: {questions_txt_path}")

            # Process each line and format into JSON
            questions_list = []
            for line in questions_raw_text.split('\n'):
                line = line.strip()
                if '=' in line:
                    q_id, q_text = line.split('=', 1)
                    questions_list.append({q_id.strip(): q_text.strip()})
                elif ':' in line: # Handle potential alternative format from S02E04 example
                    q_id, q_text = line.split(':', 1)
                    questions_list.append({q_id.strip(): q_text.strip()})


            # Save formatted questions to questions.json
            with open(questions_json_path, 'w', encoding='utf-8') as f:
                json.dump(questions_list, f, ensure_ascii=False, indent=2)
            logger.info(f"Saved formatted questions to: {questions_json_path}")

            # Return questions in Dict[str, str] format
            questions_dict = {list(q.keys())[0]: list(q.values())[0] for q in questions_list}
            logger.info(f"Successfully fetched and processed {len(questions_dict)} questions.")
            return questions_dict

        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching questions from {questions_url}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error processing questions data: {e}")
            return None

    # --- Multimodal Processing & Context Consolidation Methods ---
    def _parse_html_content(self, html_content: str, base_url: str) -> Tuple[str, List[str], List[str]]:
        """
        Parsuje zawartość HTML, ekstrahuje tekst, URL obrazów i URL plików audio.
        Args:
            html_content (str): Surowa zawartość HTML.
            base_url (str): Bazowy URL artykułu do konwersji względnych URL na absolutne.
        Returns:
            Tuple[str, List[str], List[str]]: Oczyszczony tekst, lista URL obrazów, lista URL audio.
        """
        logger.info("Parsing HTML content...")
        soup = BeautifulSoup(html_content, 'html.parser')

        # Remove unwanted tags for text extraction
        for script_or_style in soup(["script", "style", "header", "footer", "nav"]):
            script_or_style.decompose()

        # Extract main text content
        article_body = soup.find('body') # Assuming main content is in body, adjust if needed
        if article_body:
            h = html2text.HTML2Text()
            h.ignore_links = False
            h.ignore_images = False
            text_content = h.handle(str(article_body))
        else:
            text_content = ""
            logger.warning("Could not find main article body for text extraction.")

        # Extract image URLs
        img_urls = []
        for img in soup.find_all('img'):
            src = img.get('src')
            if src:
                if not src.startswith(('http://', 'https://')):
                    src = requests.compat.urljoin(base_url, src)
                img_urls.append(src)

        # Extract audio URLs
        audio_urls = []
        for audio in soup.find_all('audio'):
            src = audio.get('src')
            if src and src.endswith('.mp3'):
                if not src.startswith(('http://', 'https://')):
                    src = requests.compat.urljoin(base_url, src)
                audio_urls.append(src)
            else:
                for source in audio.find_all('source'):
                    src = source.get('src')
                    if src and src.endswith('.mp3'):
                        if not src.startswith(('http://', 'https://')):
                            src = requests.compat.urljoin(base_url, src)
                        audio_urls.append(src)
        
        logger.info(f"Parsed HTML: {len(img_urls)} images, {len(audio_urls)} audio files found.")
        return text_content, img_urls, audio_urls

    def _download_media(self, urls: List[str], media_type: str, output_dir: str) -> List[str]:
        """
        Pobiera pliki multimedialne (obrazy/audio) z podanych URL i zapisuje je lokalnie.
        Args:
            urls (List[str]): Lista adresów URL do pobrania.
            media_type (str): Typ mediów ('images' lub 'audio') dla logowania.
            output_dir (str): Katalog do zapisu pobranych plików.
        Returns:
            List[str]: Lista ścieżek do pobranych plików.
        """
        logger.info(f"Downloading {media_type} files to {output_dir}...")
        downloaded_paths = []
        os.makedirs(output_dir, exist_ok=True)

        for i, url in enumerate(urls):
            file_name = Path(url).name
            if not file_name: # Handle cases where URL might not have a direct file name
                file_name = f"{media_type}_{i}{'.png' if media_type == 'images' else '.mp3'}"
            file_path = os.path.join(output_dir, file_name)

            if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
                logger.info(f"File {file_name} already exists and is not empty. Skipping download.")
                downloaded_paths.append(file_path)
                continue

            try:
                response = requests.get(url, timeout=REQUEST_TIMEOUT)
                response.raise_for_status()
                with open(file_path, 'wb') as f:
                    f.write(response.content)
                downloaded_paths.append(file_path)
                logger.info(f"Successfully downloaded {file_name}.")
            except requests.exceptions.RequestException as e:
                logger.error(f"Error downloading {file_name} from {url}: {e}")
        return downloaded_paths

    def _load_cache(self, cache_file: str) -> Dict[str, Any]:
        """
        Ładuje dane z pliku cache JSON.
        Args:
            cache_file (str): Ścieżka do pliku cache.
        Returns:
            Dict[str, Any]: Załadowane dane, lub pusty słownik jeśli plik nie istnieje/jest pusty.
        """
        if os.path.exists(cache_file) and os.path.getsize(cache_file) > 0:
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"Loaded cache from {cache_file}.")
                return data
            except json.JSONDecodeError as e:
                logger.warning(f"Cache file {cache_file} is corrupted: {e}. Starting with empty cache.")
                return {}
        return {}

    def _save_cache(self, cache_file: str, data: Dict[str, Any]) -> None:
        """
        Zapisuje dane do pliku cache JSON.
        Args:
            cache_file (str): Ścieżka do pliku cache.
            data (Dict[str, Any]): Dane do zapisania.
        """
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"Saved cache to {cache_file}.")
        except Exception as e:
            logger.error(f"Error saving cache to {cache_file}: {e}")

    def _describe_image(self, image_path: str, context_text: str) -> str:
        """
        Generuje opis obrazu za pomocą Kluster.ai Gemma, uwzględniając kontekst tekstowy.
        Args:
            image_path (str): Ścieżka do pliku obrazu.
            context_text (str): Tekst z otoczenia obrazu (np. podpis).
        Returns:
            str: Wygenerowany opis obrazu.
        """
        logger.info(f"Describing image: {image_path}")
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                # Optimize size if too large (e.g., > 2MP)
                if img.size[0] * img.size[1] > 2000000:
                    img.thumbnail((1500, 1500), Image.Resampling.LANCZOS)
                
                buffer = io.BytesIO()
                img.save(buffer, format='PNG')
                image_b64 = base64.b64encode(buffer.getvalue()).decode()

            prompt = f"Przyglądaj się dokładnie i zwracaj uwagę na istotne szczegóły. Opisz szczegółowo ten obraz, uwzględniając kontekst: '{context_text}'. Skup się na elementach istotnych dla artykułu naukowego. Zwróć tylko opis, bez dodatkowych komentarzy."
            
            # Kluster.ai specific call for vision model
            headers = {
                "Authorization": f"Bearer {self.kluster_api_key}",
                "Content-Type": "application/json"
            }
            payload = {
                "model": IMAGE_MODEL,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_b64}"}}
                        ]
                    }
                ],
                "max_tokens": LLM_MAX_TOKENS,
                "temperature": LLM_TEMPERATURE
            }
            
            response = requests.post(
                f"{self.kluster_base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=REQUEST_TIMEOUT
            )
            response.raise_for_status()
            result = response.json()
            return result["choices"][0]["message"]["content"].strip()

        except Exception as e:
            logger.error(f"Error describing image {image_path}: {e}")
            return ""

    def _transcribe_audio(self, audio_path: str) -> str:
        """
        Transkrybuje plik audio na tekst za pomocą AssemblyAI.
        Args:
            audio_path (str): Ścieżka do pliku audio.
        Returns:
            str: Transkrybowany tekst.
        """
        logger.info(f"Transcribing audio: {audio_path}")
        try:
            config = aai.TranscriptionConfig(speech_model=aai.SpeechModel.best, language_code="pl")
            transcriber = aai.Transcriber(config=config)
            transcript = transcriber.transcribe(audio_path)
            
            if transcript.status == aai.TranscriptStatus.error:
                raise RuntimeError(f"AssemblyAI transcription failed: {transcript.error}")
            
            return transcript.text
        except Exception as e:
            logger.error(f"Error transcribing audio {audio_path}: {e}")
            return ""

    def _process_text_content(self, html_content: str) -> str:
        """
        Pobiera, czyści i formatuje tekst artykułu.
        Args:
            html_content (str): Surowa zawartość HTML artykułu.
        Returns:
            str: Ścieżka do pliku z przetworzonym tekstem (text1.md lub text1formatted.txt).
        """
        logger.info("Processing text content...")
        
        # 1. Parse HTML and convert to Markdown (text1.md)
        text_md_path = os.path.join(self.CACHE_DIR, "text1.md")
        if os.path.exists(text_md_path) and os.path.getsize(text_md_path) > 0:
            logger.info(f"Text content (Markdown) already exists: {text_md_path}. Skipping initial parsing.")
            with open(text_md_path, 'r', encoding='utf-8') as f:
                initial_text = f.read()
        else:
            soup = BeautifulSoup(html_content, 'html.parser')
            for script_or_style in soup(["script", "style", "header", "footer", "nav"]):
                script_or_style.decompose()
            article_body = soup.find('body')
            if article_body:
                h = html2text.HTML2Text()
                h.ignore_links = False
                h.ignore_images = False
                initial_text = h.handle(str(article_body))
                with open(text_md_path, 'w', encoding='utf-8') as f:
                    f.write(initial_text)
                logger.info(f"Initial text content saved to: {text_md_path}")
            else:
                logger.error("Could not extract text from HTML body.")
                return ""

        # 2. Check if text1.md is sufficiently clean (simple heuristic for now)
        # This check can be more sophisticated if needed. For now, assume html2text is good.
        # If the user wants LLM cleaning, it will be explicitly triggered.
        
        # 3. If LLM cleaning is explicitly requested or deemed necessary
        text_formatted_path = os.path.join(self.CACHE_DIR, "text1formatted.txt")
        # This logic would be triggered by a flag or a more complex heuristic
        # For now, we assume html2text is sufficient unless --text flag implies LLM cleaning.
        # The --text flag will handle the LLM cleaning part.
        
        # For the full workflow, we use the initial markdown text.
        return text_md_path

    def _consolidate_context(self, text_file_path: str, image_descriptions: Dict[str, str], audio_transcriptions: Dict[str, str]) -> str:
        """
        Łączy wszystkie przetworzone dane w jeden spójny kontekst Markdown.
        Args:
            text_file_path (str): Ścieżka do pliku z przetworzonym tekstem artykułu.
            image_descriptions (Dict[str, str]): Słownik opisów obrazów (URL: opis).
            audio_transcriptions (Dict[str, str]): Słownik transkrypcji audio (URL: transkrypcja).
        Returns:
            str: Skonsolidowany kontekst w formacie Markdown.
        """
        logger.info("Consolidating context...")
        
        # Load processed text
        try:
            with open(text_file_path, 'r', encoding='utf-8') as f:
                main_text = f.read()
        except Exception as e:
            logger.error(f"Error reading processed text file {text_file_path}: {e}")
            main_text = ""

        context_parts = [f"# Artykuł: {Path(ARTICLE_URL).name}\n\n"]
        context_parts.append(main_text)

        if image_descriptions:
            context_parts.append("\n\n## Opisy Obrazów\n")
            for name, desc in image_descriptions.items():
                context_parts.append(f"### Obraz: {name}\n{desc}\n")

        if audio_transcriptions:
            context_parts.append("\n\n## Transkrypcje Nagrań Audio\n")
            for name, trans in audio_transcriptions.items():
                context_parts.append(f"### Nagranie: {name}\n{trans}\n")

        consolidated_md = "".join(context_parts)
        
        with open(self.CONSOLIDATED_CONTEXT_FILE, 'w', encoding='utf-8') as f:
            f.write(consolidated_md)
        logger.info(f"Consolidated context saved to: {self.CONSOLIDATED_CONTEXT_FILE}")
        return consolidated_md

    # --- Question Answering Methods ---
    def _answer_questions(self, questions: Dict[str, str], consolidated_context: str) -> Dict[str, str]:
        """
        Odpowiada na listę pytań, używając skonsolidowanego kontekstu i LLM.
        Args:
            questions (Dict[str, str]): Słownik pytań (ID: treść).
            consolidated_context (str): Cały skonsolidowany kontekst Markdown.
        Returns:
            Dict[str, str]: Słownik odpowiedzi (ID: krótka odpowiedź).
        """
        logger.info("Answering questions...")
        answers = {}
        for q_id, q_text in questions.items():
            logger.info(f"Answering question {q_id}: {q_text}")
            
            # Construct prompt for LLM
            user_prompt = f"Pytanie: {q_text}\n\nKontekst:\n{consolidated_context}\n\n" \
                          "Odpowiedz na pytanie w jednym, krótkim zdaniu. " \
                          "Zwróć odpowiedź w tagach <answer>odpowiedź</answer>. " \
                          "Przed odpowiedzią, przedstaw swoje rozumowanie i uzasadnienie."

            answer = "Nie udało się wyodrębnić odpowiedzi." # Default answer
            for attempt in range(1, 4): # 1 initial attempt + 2 retries = 3 total attempts
                logger.info(f"Attempt {attempt} to answer question {q_id}")
                llm_response = call_llm(
                    prompt=user_prompt,
                    system_prompt=self.system_prompt,
                    model=TEXT_MODEL,
                    api_key=self.kluster_api_key,
                    base_url=self.kluster_base_url,
                    max_tokens=LLM_MAX_TOKENS,
                    temperature=LLM_TEMPERATURE
                )

                if llm_response:
                    match = re.search(ANSWER_TAG_REGEX, llm_response, re.DOTALL)
                    if match:
                        extracted_answer = match.group(1).strip()
                        if extracted_answer != "Nie udało się wyodrębnić odpowiedzi.":
                            answer = extracted_answer
                            logger.info(f"Answer for {q_id} found on attempt {attempt}: {answer}")
                            break # Exit retry loop if a valid answer is found
                        else:
                            logger.warning(f"LLM response for {q_id} on attempt {attempt} indicates extraction failed. Retrying...")
                    else:
                        logger.warning(f"Could not extract answer from LLM response for {q_id} on attempt {attempt}. Full response: {llm_response}. Retrying...")
                else:
                    logger.error(f"No LLM response for question {q_id} on attempt {attempt}. Retrying...")

                if attempt < 3:
                    time.sleep(RETRY_DELAY) # Wait before retrying

            answers[q_id] = answer # Store the final answer (either valid or the default failure message)
        
        self._save_cache(self.FINAL_ANSWERS_FILE, answers)
        return answers

    # --- Finalization & Submission Methods ---
    def _format_final_answer(self, answers: Dict[str, str]) -> Dict[str, str]:
        """
        Formatuje odpowiedzi do wymaganego formatu JSON dla centrali.
        Args:
            answers (Dict[str, str]): Słownik odpowiedzi (ID: krótka odpowiedź).
        Returns:
            Dict[str, str]: Sformatowany słownik odpowiedzi.
        """
        logger.info("Formatting final answer.")
        # The answers dictionary is already in the desired format {"ID": "answer"}
        # The task specifies "answer": { "01": "...", "02": "..." }
        # So we just return the dictionary as is.
        return answers

    def _submit_answer(self, formatted_answer: Dict[str, str]) -> Optional[Dict]:
        """
        Wysyła sformatowaną odpowiedź do centrali.
        Args:
            formatted_answer (Dict[str, str]): Sformatowany słownik odpowiedzi.
        Returns:
            Optional[Dict]: Odpowiedź z serwera centrali, lub None w przypadku błędu.
        """
        logger.info("Submitting answer to centrala.")
        return send_report(REPORT_ENDPOINT, self.aidevs_api_key, TASK_NAME, formatted_answer)

    # --- Flag-specific Workflow Methods ---
    def process_images_only(self) -> None:
        """
        Pobiera, opisuje obrazy i zapisuje wyniki do images.json.
        """
        logger.info("=== Running --image workflow ===")
        html_content = self._fetch_html_article(ARTICLE_URL)
        if not html_content: return
        
        _, image_urls, _ = self._parse_html_content(html_content, ARTICLE_URL)
        downloaded_images = self._download_media(image_urls, "images", os.path.join(self.CACHE_DIR, "temp_media", "images"))
        
        image_descriptions = self._load_cache(self.IMAGE_CACHE_FILE)
        for img_path in downloaded_images:
            img_name = Path(img_path).name
            if img_name not in image_descriptions:
                context_text = f"Image {img_name} from article." # Placeholder
                description = self._describe_image(img_path, context_text)
                if description:
                    image_descriptions[img_name] = description
                    logger.info(f"Generated description for {img_name}.")
                else:
                    logger.warning(f"Failed to describe image {img_name}.")
        self._save_cache(self.IMAGE_CACHE_FILE, image_descriptions)
        logger.info("Image processing complete.")

    def process_audio_only(self) -> None:
        """
        Pobiera, transkrybuje pliki audio i zapisuje wyniki do audio.json.
        """
        logger.info("=== Running --sound workflow ===")
        html_content = self._fetch_html_article(ARTICLE_URL)
        if not html_content: return
        
        _, _, audio_urls = self._parse_html_content(html_content, ARTICLE_URL)
        downloaded_audio = self._download_media(audio_urls, "audio", os.path.join(self.CACHE_DIR, "temp_media", "audio"))
        
        audio_transcriptions = self._load_cache(self.AUDIO_CACHE_FILE)
        for aud_path in downloaded_audio:
            aud_name = Path(aud_path).name
            if aud_name not in audio_transcriptions:
                transcription = self._transcribe_audio(aud_path)
                if transcription:
                    audio_transcriptions[aud_name] = transcription
                    logger.info(f"Generated transcription for {aud_name}.")
                else:
                    logger.warning(f"Failed to transcribe audio {aud_name}.")
        self._save_cache(self.AUDIO_CACHE_FILE, audio_transcriptions)
        logger.info("Audio processing complete.")

    def process_text_only(self) -> None:
        """
        Pobiera, czyści i formatuje tekst artykułu, zapisując do text1.md lub text1formatted.txt.
        """
        logger.info("=== Running --text workflow ===")
        html_content = self._fetch_html_article(ARTICLE_URL)
        if not html_content: return

        # Initial parse to Markdown
        text_md_path = os.path.join(self.CACHE_DIR, "text1.md")
        if not (os.path.exists(text_md_path) and os.path.getsize(text_md_path) > 0):
            # Perform initial parsing if not cached
            soup = BeautifulSoup(html_content, 'html.parser')
            for script_or_style in soup(["script", "style", "header", "footer", "nav"]):
                script_or_style.decompose()
            article_body = soup.find('body')
            if article_body:
                h = html2text.HTML2Text()
                h.ignore_links = False
                h.ignore_images = False
                initial_text = h.handle(str(article_body))
                with open(text_md_path, 'w', encoding='utf-8') as f:
                    f.write(initial_text)
                logger.info(f"Initial text content saved to: {text_md_path}")
            else:
                logger.error("Could not extract text from HTML body for --text flag.")
                return
        else:
            logger.info(f"Text content (Markdown) already exists: {text_md_path}. Skipping initial parsing.")
            with open(text_md_path, 'r', encoding='utf-8') as f:
                initial_text = f.read()

        # Decide if LLM cleaning is needed (simple heuristic: check for remaining HTML tags)
        # This is a heuristic, a more robust check might be needed.
        needs_llm_cleaning = bool(re.search(r'<[^>]+>', initial_text)) # Check for any HTML tags
        
        final_text_path = text_md_path
        if needs_llm_cleaning:
            logger.info("Detected remaining HTML-like tags. Sending text for LLM cleaning.")
            text_formatted_path = os.path.join(self.CACHE_DIR, "text1formatted.txt")
            if os.path.exists(text_formatted_path) and os.path.getsize(text_formatted_path) > 0:
                logger.info(f"Formatted text already exists: {text_formatted_path}. Skipping LLM cleaning.")
                final_text_path = text_formatted_path
            else:
                cleaned_text = call_llm(
                    prompt=initial_text,
                    system_prompt=TEXT_CLEANING_PROMPT,
                    model=TEXT_MODEL,
                    api_key=self.kluster_api_key,
                    base_url=self.kluster_base_url,
                    max_tokens=LLM_MAX_TOKENS,
                    temperature=LLM_TEMPERATURE
                )
                if cleaned_text:
                    with open(text_formatted_path, 'w', encoding='utf-8') as f:
                        f.write(cleaned_text)
                    logger.info(f"LLM cleaned text saved to: {text_formatted_path}")
                    final_text_path = text_formatted_path
                else:
                    logger.warning("LLM cleaning failed. Using initial markdown text.")
        else:
            logger.info("Initial markdown parsing seems sufficient. No LLM cleaning needed.")
        
        logger.info(f"Text processing complete. Final text saved to: {final_text_path}")

    def answer_questions_only(self) -> None:
        """
        Pobiera pytania, ładuje skonsolidowany kontekst, odpowiada na pytania i zapisuje do answers.json.
        """
        logger.info("=== Running --answer workflow ===")
        
        questions = self._fetch_questions(self.aidevs_api_key)
        if not questions:
            logger.error("Failed to fetch questions. Exiting --answer workflow.")
            return

        # Load consolidated context (must have been generated by full workflow or other flags)
        if not (os.path.exists(self.CONSOLIDATED_CONTEXT_FILE) and os.path.getsize(self.CONSOLIDATED_CONTEXT_FILE) > 0):
            logger.error(f"Consolidated context file not found or empty: {self.CONSOLIDATED_CONTEXT_FILE}. "
                         "Please run full workflow or relevant processing flags first.")
            return
        with open(self.CONSOLIDATED_CONTEXT_FILE, 'r', encoding='utf-8') as f:
            consolidated_context = f.read()

        answers = self._answer_questions(questions, consolidated_context)
        if answers:
            logger.info(f"Answers saved to {self.FINAL_ANSWERS_FILE}.")
        else:
            logger.error("Failed to generate answers.")

    def submit_centrala_only(self) -> None:
        """
        Ładuje odpowiedzi z answers.json, wysyła do centrali i szuka flagi.
        """
        logger.info("=== Running --central workflow ===")
        
        answers = self._load_cache(self.FINAL_ANSWERS_FILE)
        if not answers:
            logger.error(f"No answers found in {self.FINAL_ANSWERS_FILE}. Please run --answer workflow first.")
            return
        
        formatted_answer = self._format_final_answer(answers)
        response_from_centrala = self._submit_answer(formatted_answer)
        
        if response_from_centrala:
            flag = check_for_flag(response_from_centrala)
            if flag:
                print(f"\n🎉 FLAG FOUND: {flag}")
            else:
                print(f"\n📋 Centrala Response: {response_from_centrala}")
        else:
            logger.error("Failed to get response from centrala.")

    def consolidate_context_only(self) -> None:
        """
        Ładuje dane z plików cache (text1.md, images.json, audio.json) i tworzy consolidated_context.md.
        """
        logger.info("=== Running --context workflow ===")

        text_file_path = os.path.join(self.CACHE_DIR, "text1.md")
        image_cache_path = self.IMAGE_CACHE_FILE
        audio_cache_path = self.AUDIO_CACHE_FILE

        text_cached = os.path.exists(text_file_path) and os.path.getsize(text_file_path) > 0
        images_cached = os.path.exists(image_cache_path) and os.path.getsize(image_cache_path) > 0
        audio_cached = os.path.exists(audio_cache_path) and os.path.getsize(audio_cache_path) > 0

        if not (text_cached or images_cached or audio_cached):
            logger.error("No cache files (text1.md, images.json, audio.json) found. Please run --text, --image, or --sound flags first.")
            return

        logger.info("Individual cache files found. Consolidating context...")
        image_descriptions = self._load_cache(image_cache_path) if images_cached else {}
        audio_transcriptions = self._load_cache(audio_cache_path) if audio_cached else {}

        consolidated_context = self._consolidate_context(text_file_path if text_cached else "", image_descriptions, audio_transcriptions)

        if consolidated_context:
            logger.info(f"Consolidated context saved to {self.CONSOLIDATED_CONTEXT_FILE}.")
        else:
            logger.error("Failed to consolidate context.")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Arxiv Article Analysis SFA')
    parser.add_argument('--image', action='store_true', help='Process only images (download, describe, cache).')
    parser.add_argument('--sound', action='store_true', help='Process only audio files (download, transcribe, cache).')
    parser.add_argument('--text', action='store_true', help='Process only text content (download, clean, cache).')
    parser.add_argument('--answer', action='store_true', help='Answer questions using cached data (text, images, audio).')
    parser.add_argument('--central', action='store_true', help='Submit answers to centrala using cached answers.json.')
    parser.add_argument('--context', action='store_true', help='Consolidate context from cached text, images, and audio.')
    args = parser.parse_args()
    
    if not validate_configuration():
        logger.critical("Invalid configuration. Exiting.")
        return

    processor = ArxivProcessor()
    
    if args.image:
        processor.process_images_only()
    elif args.sound:
        processor.process_audio_only()
    elif args.text:
        processor.process_text_only()
    elif args.answer:
        processor.answer_questions_only()
    elif args.central:
        processor.submit_centrala_only()
    elif args.context:
        processor.consolidate_context_only()
    else:
        processor.run_full_workflow()

if __name__ == "__main__":
    main()
