"""
Single File Agent Base Schema (SFA-BBS) - Template

This script provides a foundational template for creating Single File Agents (SFAs)
based on common patterns identified in existing agent implementations.
It includes standard imports, configuration handling, logging, error management,
and modular functions for common operations like API interactions and LLM calls.

Author: Augment Agent
Date: 2025-05-21
Version: 1.0.0
"""

# --- IMPORTS ---
import os
import re
import json
import logging
import time
import requests
from typing import Dict, List, Any, Tuple, Optional, Union
from dotenv import load_dotenv
from openai import OpenAI, APIError, APIConnectionError, RateLimitError

# --- CONFIGURATION ---
# Load environment variables
load_dotenv()

# --- APPLICATION SETTINGS ---
# These settings should be customized for each specific agent implementation
TASK_NAME = "SFA_BASE_SCHEMA"
API_BASE_URL = os.getenv("API_BASE_URL", "https://c3ntrala.ag3nts.org")
DATA_ENDPOINT = f"{API_BASE_URL}/data"
REPORT_ENDPOINT = f"{API_BASE_URL}/report"
API_KEY = os.getenv("AIDEVS_API_KEY")

# Request settings
REQUEST_TIMEOUT = 10  # seconds
RETRY_ATTEMPTS = 2
RETRY_DELAY = 2  # seconds

# --- LLM SETTINGS ---
LLM_MODEL = os.getenv("KLUSTER_LLM_MODEL", os.getenv("OPENAI_LLM_MODEL"))
LLM_API_KEY = os.getenv("KLUSTER_API_KEY", os.getenv("OPENAI_API_KEY"))
LLM_BASE_URL = os.getenv("KLUSTER_BASE_URL", os.getenv("OPENAI_BASE_URL"))
LLM_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", "4000"))
LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.3"))
LLM_TOP_P = float(os.getenv("LLM_TOP_P", "1.0"))

# System prompt path - load from file instead of hardcoding
SYSTEM_PROMPT_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "system_prompt.txt")

# Default system prompt (used if file not found)
DEFAULT_SYSTEM_PROMPT = """
You are an AI assistant helping with a specific task.
Provide clear, concise, and accurate responses.
"""

# --- REGEX PATTERNS ---
# Common regex patterns for flag extraction - customize as needed
FLAG_REGEX = r"FLG[a-zA-Z0-9_-]+"
FLAG_REGEX_CURLY = r"\{\{FLG:[a-zA-Z0-9_-]+\}\}"

# --- LOGGING CONFIGURATION ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# --- HELPER FUNCTIONS ---

def validate_configuration() -> bool:
    """
    Validates that all required configuration variables are set.

    Returns:
        bool: True if configuration is valid, False otherwise
    """
    required_env_vars = {
        "AIDEVS_API_KEY": "API key for Central API",
        "KLUSTER_API_KEY": "API key for Kluster.ai",
    }
    missing_vars = []

    for var, description in required_env_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"{var} ({description})")

    if missing_vars:
        logger.critical(f"Missing required environment variables: {', '.join(missing_vars)}")
        return False

    # Check if API_KEY is set
    if not API_KEY:
        logger.critical("API_KEY is not set, check AIDEVS_API_KEY environment variable")
        return False

    # Check if LLM_API_KEY is set
    if not LLM_API_KEY:
        logger.warning("LLM_API_KEY is not set, using KLUSTER_API_KEY")

    return True

def fetch_data(url: str, api_key: str, attempt: int = 1) -> Optional[Dict]:
    """
    Fetches data from the specified URL with retry logic.

    Args:
        url: The URL to fetch data from
        api_key: API key for authentication
        attempt: Current attempt number (for retry logic)

    Returns:
        Optional[Dict]: The fetched data as a dictionary, or None if failed
    """
    logger.info(f"Fetching data from {url} (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    try:
        response = requests.get(
            url,
            headers=headers,
            timeout=REQUEST_TIMEOUT
        )
        response.raise_for_status()

        data = response.json()
        logger.info("Data fetched successfully")
        return data

    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching data: {e}")

        # Retry logic
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return fetch_data(url, api_key, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response: {e}")
        return None

def load_system_prompt() -> str:
    """
    Wczytuje system prompt z pliku.

    Returns:
        str: Zawartość pliku z system promptem
    """
    try:
        with open(SYSTEM_PROMPT_PATH, 'r', encoding='utf-8') as file:
            system_prompt = file.read()
        logger.info(f"Wczytano system prompt z pliku: {SYSTEM_PROMPT_PATH}")
        return system_prompt
    except Exception as e:
        logger.error(f"Błąd podczas wczytywania system promptu: {e}")
        logger.warning("Używanie domyślnego system promptu")
        return DEFAULT_SYSTEM_PROMPT

def call_llm(prompt: str, system_prompt_path: str = None, attempt: int = 1) -> Optional[str]:
    """
    Calls the LLM with the given prompt and handles errors with retry logic.
    Uses system prompt from file if provided, otherwise loads from SYSTEM_PROMPT_PATH.

    Args:
        prompt: The user prompt to send to the LLM
        system_prompt_path: Path to system prompt file (optional)
        attempt: Current attempt number (for retry logic)

    Returns:
        Optional[str]: The LLM's response, or None if failed
    """
    logger.info(f"Calling LLM with prompt (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    try:
        # Load system prompt from file or use default
        if system_prompt_path and os.path.exists(system_prompt_path):
            with open(system_prompt_path, 'r', encoding='utf-8') as file:
                system_prompt = file.read()
            logger.info(f"Using system prompt from file: {system_prompt_path}")
        else:
            system_prompt = load_system_prompt()

        client_params = {"api_key": LLM_API_KEY}
        if LLM_BASE_URL:
            client_params["base_url"] = LLM_BASE_URL

        client = OpenAI(**client_params)

        logger.info(f"Using model: {LLM_MODEL} via API: {LLM_BASE_URL}")

        completion = client.chat.completions.create(
            model=LLM_MODEL,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            temperature=LLM_TEMPERATURE,
            max_completion_tokens=LLM_MAX_TOKENS,
            top_p=LLM_TOP_P
        )

        response = completion.choices[0].message.content.strip()
        logger.info("LLM call successful")
        return response

    except (APIError, APIConnectionError, RateLimitError) as e:
        logger.error(f"LLM API error: {e}")

        # Retry logic
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return call_llm(prompt, system_prompt_path, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except Exception as e:
        logger.error(f"Unexpected error during LLM call: {e}")
        return None

def send_report(url: str, api_key: str, task_name: str, answer: Any, attempt: int = 1) -> Optional[Dict]:
    """
    Sends the final report/answer to the specified endpoint.

    Args:
        url: The URL to send the report to
        api_key: API key for authentication
        task_name: Name of the task being performed
        answer: The answer/data to send
        attempt: Current attempt number (for retry logic)

    Returns:
        Optional[Dict]: The response from the server, or None if failed
    """
    logger.info(f"Sending report to {url} (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    # Ensure the answer is properly formatted
    # Remove whitespace from beginning and end
    if isinstance(answer, str):
        clean_answer = answer.strip()

        # Remove quotes if present
        if (clean_answer.startswith('"') and clean_answer.endswith('"')) or \
           (clean_answer.startswith("'") and clean_answer.endswith("'")):
            clean_answer = clean_answer[1:-1]

        logger.info(f"Original answer: '{answer}'")
        logger.info(f"Cleaned answer: '{clean_answer}'")
    else:
        clean_answer = answer

    payload = {
        "task": task_name,
        "apikey": api_key,
        "answer": clean_answer
    }

    headers = {
        "Content-Type": "application/json; charset=utf-8"
    }

    # Log full payload before sending
    logger.info(f"Sending payload: {json.dumps(payload, ensure_ascii=False)}")

    try:
        response = requests.post(
            url,
            json=payload,  # Use json instead of data to let requests handle serialization
            headers=headers,
            timeout=REQUEST_TIMEOUT
        )

        # Log full server response
        logger.info(f"Response code: {response.status_code}")
        logger.info(f"Response headers: {dict(response.headers)}")
        logger.info(f"Response content: {response.text}")

        # Display full response in console
        print(f"\n{'='*50}")
        print(f"RESPONSE FROM SERVER:")
        print(f"Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Content: {response.text}")
        print(f"{'='*50}\n")

        response.raise_for_status()

        result = response.json()
        logger.info("Report sent successfully")
        return result

    except requests.exceptions.RequestException as e:
        logger.error(f"Error sending report: {e}")

        if hasattr(e, 'response') and hasattr(e.response, 'text'):
            logger.error(f"Server response: {e.response.text}")
            logger.error(f"Response headers: {dict(e.response.headers) if hasattr(e.response, 'headers') else 'No headers'}")

            # Display full error response in console
            print(f"\n{'='*50}")
            print(f"ERROR FROM SERVER:")
            print(f"Code: {e.response.status_code if hasattr(e.response, 'status_code') else 'No code'}")
            print(f"Headers: {dict(e.response.headers) if hasattr(e.response, 'headers') else 'No headers'}")
            print(f"Content: {e.response.text}")
            print(f"{'='*50}\n")

        # Retry logic
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return send_report(url, api_key, task_name, answer, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response: {e}")
        return None

def check_for_flag(response) -> Optional[str]:
    """
    Sprawdza, czy odpowiedź zawiera flagę w formatach:
    - FLG[a-zA-Z0-9_-]+
    - {{FLG:[a-zA-Z0-9_-]+}}

    Args:
        response: Odpowiedź do sprawdzenia (może być string, dict lub inny typ)

    Returns:
        Optional[str]: Znaleziona flaga lub None, jeśli nie znaleziono
    """
    # Convert response to string for easier searching
    response_str = str(response)

    if not response_str:
        return None

    # Search for flag in {{FLG:XXXX}} format
    match_curly = re.search(FLAG_REGEX_CURLY, response_str)
    if match_curly:
        flag = match_curly.group(0)
        logger.info(f"Flag found in {{{{FLG:XXXX}}}} format: {flag}")
        print(f"\n{'='*50}\nFLAG: {flag}\n{'='*50}\n")
        return flag

    # Search for flag in FLG[a-zA-Z0-9_-]+ format
    match = re.search(FLAG_REGEX, response_str)
    if match:
        flag = match.group(0)
        logger.info(f"Flag found in FLG[a-zA-Z0-9_-]+ format: {flag}")
        print(f"\n{'='*50}\nFLAG: {flag}\n{'='*50}\n")
        return flag

    # Check if text contains the word "flag" or "FLG"
    if "flag" in response_str.lower() or "flg" in response_str.lower():
        logger.info(f"Text contains the word 'flag' or 'FLG': {response_str}")
        print(f"\n{'='*50}\nPOTENTIAL FLAG: {response_str}\n{'='*50}\n")
        return response_str

    logger.info("No flag found in response")
    return None

def process_data(data: Dict) -> Dict:
    """
    Processes the fetched data according to task requirements.
    This is a placeholder function that should be customized for each specific agent.

    Args:
        data: The data to process

    Returns:
        Dict: The processed data
    """
    logger.info("Processing data")

    # This is where task-specific data processing would occur
    # For example:
    # - Extracting specific information
    # - Transforming data format
    # - Applying business logic

    # Placeholder implementation - simply returns the input data
    processed_data = data

    logger.info("Data processing complete")
    return processed_data

# --- MAIN FUNCTION ---

def main():
    """
    Main function that orchestrates the agent's workflow.
    """
    logger.info(f"--- Starting {TASK_NAME} agent ---")

    # Validate configuration
    if not validate_configuration():
        logger.critical("Invalid configuration. Exiting.")
        return

    try:
        # Step 1: Fetch data
        data = fetch_data(DATA_ENDPOINT, API_KEY)
        if not data:
            logger.error("Failed to fetch data. Exiting.")
            return

        # Step 2: Process data (task-specific)
        processed_data = process_data(data)

        # Step 3: Prepare data for LLM (if needed)
        # This step is optional and depends on whether LLM processing is required
        if "question" in processed_data:
            # Example: If the data contains a question to be answered by LLM
            question = processed_data["question"]

            # Step 4: Call LLM
            llm_response = call_llm(question)
            if not llm_response:
                logger.error("Failed to get response from LLM. Exiting.")
                return

            # Step 5: Prepare final answer
            answer = llm_response
        else:
            # If no LLM processing is needed, use the processed data as the answer
            answer = processed_data

        # Step 6: Send report
        report_response = send_report(REPORT_ENDPOINT, API_KEY, TASK_NAME, answer)
        if not report_response:
            logger.error("Failed to send report. Exiting.")
            return

        # Step 7: Check for flag in response
        flag = check_for_flag(report_response)

        # Step 8: Display results
        if flag:
            logger.info(f"SUCCESS! Flag: {flag}")
            print(f"\n{'='*50}\nFLAG: {flag}\n{'='*50}\n")
        else:
            logger.info("Task completed, but no flag found in response")
            print(f"\n{'='*50}\nRESPONSE: {json.dumps(report_response, indent=2)}\n{'='*50}\n")

    except Exception as e:
        logger.critical(f"Unexpected error: {e}", exc_info=True)

    logger.info(f"--- {TASK_NAME} agent finished ---")

# --- SCRIPT ENTRY POINT ---

if __name__ == "__main__":
    main()
