# Plan Implementacji SFA: Wyszukiwanie Semantyczne Raportów Broni

#### 1. Cel Implementacji

Celem jest stworzenie kompletnego, jednoplikowego agenta (SFA) w Pythonie, który zaimplementuje workflow wyszukiwania semantycznego raportów broni. Aplikacja ma za zadanie zaindeksować raporty tekstowe w bazie wektorowej (Qdrant) z użyciem embeddingów OpenAI, a następnie odpowiedzieć na konkretne pytanie, odnajdując najbardziej pasujący raport i zwracając datę jego powstania. Całość ma być zgodna z najlepszymi praktykami inżynierii oprogramowania i wykorzystywać dostarczony schemat `sfa_bbs.py` jako bazę.

#### 2. Struktura Pliku (adw/S03/S03E02/S03E02_agent.py)

Aplikacja będzie pojedynczym plikiem Pythona, rozszerzającym i modyfikującym dostarczony szablon `sfa_bbs.py`.

```python adw/S03/S03E02/S03E02_agent.py
# --- IMPORTS ---
# ... (existing imports from sfa_bbs.py)
import glob # New import for file listing
from qdrant_client import QdrantClient, models # New imports for Qdrant

# --- CONFIGURATION ---
# ... (existing configuration from sfa_bbs.py)
TASK_NAME = "wektory" # Update task name

# New configuration for reports directory and Qdrant
REPORTS_DIR = "/Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S02/pliki_z_fabryki/weapons_tests/do-not-share/"
QDRANT_HOST = "localhost"
QDRANT_PORT = 3333
QDRANT_COLLECTION_NAME = "weapon_reports"
EMBEDDING_MODEL_NAME = "text-embedding-3-large"
EMBEDDING_MODEL_DIMENSIONS = 3072 # Dimensions for text-embedding-3-large

# Remove SYSTEM_PROMPT_PATH and DEFAULT_SYSTEM_PROMPT as they are not needed for embedding models.

# --- LLM SETTINGS ---
# Keep existing LLM settings, ensuring OPENAI_API_KEY is used for LLM_API_KEY
# LLM_MODEL will be used for embedding model name.
# LLM_MAX_TOKENS, LLM_TEMPERATURE, LLM_TOP_P are not directly applicable to embedding calls,
# but can be kept as general LLM settings if the agent were to expand.
# For embedding calls, only model name and input are relevant.

# --- HELPER FUNCTIONS ---

# ... (existing validate_configuration, fetch_data, send_report, check_for_flag)

# Remove load_system_prompt and call_llm as they are for chat completions.
# New functions for embedding and Qdrant operations:

def initialize_qdrant_client() -> QdrantClient:
    \"\"\"
    Initializes Qdrant client and creates a collection for weapon reports.
    \"\"\"
    logger.info(f"Initializing Qdrant client at {QDRANT_HOST}:{QDRANT_PORT}")
    client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)

    logger.info(f"Checking if collection '{QDRANT_COLLECTION_NAME}' exists...")
    try:
        client.get_collection(collection_name=QDRANT_COLLECTION_NAME)
        logger.info(f"Collection '{QDRANT_COLLECTION_NAME}' already exists. Skipping creation.")
    except Exception: # QdrantClient raises exception if collection not found
        logger.info(f"Collection '{QDRANT_COLLECTION_NAME}' not found. Creating new collection...")
        client.recreate_collection(
            collection_name=QDRANT_COLLECTION_NAME,
            vectors_config=models.VectorParams(size=EMBEDDING_MODEL_DIMENSIONS, distance=models.Distance.COSINE),
        )
        logger.info(f"Collection '{QDRANT_COLLECTION_NAME}' created successfully with {EMBEDDING_MODEL_DIMENSIONS} dimensions.")
    return client

def get_embedding(text: str, attempt: int = 1) -> Optional[List[float]]:
    \"\"\"
    Generates an embedding for the given text using the specified OpenAI model.
    Includes retry logic.
    \"\"\"
    logger.info(f"Generating embedding (attempt {attempt}/{RETRY_ATTEMPTS + 1})")
    try:
        client_params = {"api_key": LLM_API_KEY}
        if LLM_BASE_URL:
            client_params["base_url"] = LLM_BASE_URL
        client = OpenAI(**client_params)

        response = client.embeddings.create(
            input=text,
            model=EMBEDDING_MODEL_NAME
        )
        embedding_vector = response.data[0].embedding
        logger.info("Embedding generated successfully.")
        return embedding_vector
    except (APIError, APIConnectionError, RateLimitError) as e:
        logger.error(f"Embedding API error: {e}")
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return get_embedding(text, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached for embedding generation.")
            return None
    except Exception as e:
        logger.error(f"Unexpected error during embedding generation: {e}")
        return None

def index_reports(qdrant_client: QdrantClient, reports_directory: str):
    \"\"\"
    Reads report files, generates embeddings, and indexes them in Qdrant.
    \"\"\"
    logger.info(f"Starting indexing of reports from: {reports_directory}")
    report_files = glob.glob(os.path.join(reports_directory, "*.txt"))
    if not report_files:
        logger.warning(f"No .txt files found in {reports_directory}. Skipping indexing.")
        return

    points_to_upsert = []
    for file_path in report_files:
        file_name = os.path.basename(file_path)
        try:
            # Extract date from filename (e.g., 2024-02-21_XR-5_report.txt)
            date_match = re.match(r"(\d{4}-\d{2}-\d{2})", file_name)
            if not date_match:
                logger.warning(f"Could not extract date from filename: {file_name}. Skipping.")
                continue
            report_date = date_match.group(1)

            with open(file_path, 'r', encoding='utf-8') as f:
                report_content = f.read()

            embedding = get_embedding(report_content)
            if embedding:
                points_to_upsert.append(
                    models.PointStruct(
                        id=hash(file_name), # Simple unique ID for each report
                        vector=embedding,
                        payload={"date": report_date, "filename": file_name}
                    )
                )
                logger.info(f"Prepared embedding for {file_name} (Date: {report_date})")
            else:
                logger.error(f"Failed to get embedding for {file_name}. Skipping.")

        except Exception as e:
            logger.error(f"Error processing file {file_name}: {e}")

    if points_to_upsert:
        logger.info(f"Upserting {len(points_to_upsert)} points to Qdrant collection '{QDRANT_COLLECTION_NAME}'...")
        qdrant_client.upsert(
            collection_name=QDRANT_COLLECTION_NAME,
            wait=True,
            points=points_to_upsert
        )
        logger.info("Reports indexed successfully in Qdrant.")
    else:
        logger.info("No points to upsert. Indexing complete (or no valid reports found).")

def find_report_date(qdrant_client: QdrantClient, question: str) -> Optional[str]:
    \"\"\"
    Generates embedding for the question, queries Qdrant, and returns the date
    from the most relevant report's metadata.
    \"\"\"
    logger.info(f"Searching for answer to question: '{question}'")
    question_embedding = get_embedding(question)
    if not question_embedding:
        logger.error("Failed to get embedding for the question.")
        return None

    search_result = qdrant_client.search(
        collection_name=QDRANT_COLLECTION_NAME,
        query_vector=question_embedding,
        limit=1
    )

    if search_result and search_result[0].payload:
        found_date = search_result[0].payload.get("date")
        found_filename = search_result[0].payload.get("filename")
        logger.info(f"Found most relevant report: {found_filename} (Date: {found_date})")
        return found_date
    else:
        logger.warning("No relevant report found in Qdrant or missing date metadata.")
        return None

# --- MAIN FUNCTION ---

def main():
    \"\"\"
    Main function that orchestrates the agent's workflow.
    \"\"\"
    logger.info(f"--- Starting {TASK_NAME} agent ---")

    # Validate configuration
    if not validate_configuration():
        logger.critical("Invalid configuration. Exiting.")
        return

    try:
        # Step 1: Initialize Qdrant client and collection
        qdrant_client = initialize_qdrant_client()

        # Step 2: Index reports
        index_reports(qdrant_client, REPORTS_DIR)

        # Step 3: Define the question
        question = "W raporcie, z którego dnia znajduje się wzmianka o kradzieży prototypu broni?"

        # Step 4: Find the report date by querying Qdrant
        answer_date = find_report_date(qdrant_client, question)
        if not answer_date:
            logger.error("Failed to find the answer date. Exiting.")
            return

        # Step 5: Send report to Centrala
        report_response = send_report(REPORT_ENDPOINT, API_KEY, TASK_NAME, answer_date)
        if not report_response:
            logger.error("Failed to send report. Exiting.")
            return

        # Step 6: Check for flag in response
        flag = check_for_flag(report_response)

        # Step 7: Display results
        if flag:
            logger.info(f"SUCCESS! Flag: {flag}")
            print(f"\\n{'='*50}\\nFLAG: {flag}\\n{'='*50}\\n")
        else:
            logger.info("Task completed, but no flag found in response")
            print(f"\\n{'='*50}\\nRESPONSE: {json.dumps(report_response, indent=2)}\\n{'='*50}\\n")

    except Exception as e:
        logger.critical(f"Unexpected error: {e}", exc_info=True)

    logger.info(f"--- {TASK_NAME} agent finished ---")

# --- SCRIPT ENTRY POINT ---

if __name__ == "__main__":
    main()
```

#### 3. Wymagane Zależności (requirements.txt)

Aby uruchomić aplikację, należy zainstalować następujące pakiety:

```
requests
python-dotenv
openai
qdrant-client
```

#### 4. Szczegółowy Plan Implementacji

1. **Konfiguracja Początkowa (`adw/S03/S03E02/S03E02_agent.py`)**
    * **Importy:** Dodaj `glob` do listowania plików oraz `QdrantClient`, `models` z `qdrant_client`.
    * **`TASK_NAME`:** Zmień na `"wektory"`.
    * **Nowe zmienne konfiguracyjne:**
        * `REPORTS_DIR`: `/Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S02/pliki_z_fabryki/weapons_tests/do-not-share/`
        * `QDRANT_HOST`: `"localhost"`
        * `QDRANT_PORT`: `3333`
        * `QDRANT_COLLECTION_NAME`: `"weapon_reports"` (nazwa kolekcji w Qdrant)
        * `EMBEDDING_MODEL_NAME`: `"text-embedding-3-large"`
        * `EMBEDDING_MODEL_DIMENSIONS`: `3072` (wymiar wektora dla wybranego modelu)
    * **Usunięcie niepotrzebnej logiki:** Usuń zmienne `SYSTEM_PROMPT_PATH` i `DEFAULT_SYSTEM_PROMPT` oraz funkcje `load_system_prompt()` i `call_llm()`, ponieważ nie są one potrzebne dla modeli embeddingowych.

2. **Funkcje Pomocnicze (Modyfikacje i Nowe)**

    * **`validate_configuration()`:**
        * Upewnij się, że sprawdza obecność `AIDEVS_API_KEY` (dla Centrali) oraz `OPENAI_API_KEY` (dla modelu embeddingowego). `sfa_bbs.py` już to robi, więc wystarczy upewnić się, że `LLM_API_KEY` jest poprawnie mapowany na `OPENAI_API_KEY`.

    * **`initialize_qdrant_client() -> QdrantClient` (NOWA FUNKCJA)**
        * Inicjalizuje `QdrantClient` z `QDRANT_HOST` i `QDRANT_PORT`.
        * Sprawdza, czy kolekcja o nazwie `QDRANT_COLLECTION_NAME` już istnieje.
        * Jeśli nie istnieje, tworzy nową kolekcję za pomocą `client.recreate_collection()`.
        * Kluczowe: `vectors_config` musi mieć `size=EMBEDDING_MODEL_DIMENSIONS` i `distance=models.Distance.COSINE`.
        * Zwraca zainicjalizowany obiekt klienta Qdrant.

    * **`get_embedding(text: str, attempt: int = 1) -> Optional[List[float]]` (NOWA FUNKCJA)**
        * Przyjmuje tekst jako argument.
        * Używa `OpenAI` klienta (z `LLM_API_KEY` i `LLM_BASE_URL`).
        * Wywołuje `client.embeddings.create()` z `input=text` i `model=EMBEDDING_MODEL_NAME`.
        * Implementuje logikę ponawiania prób (`RETRY_ATTEMPTS`, `RETRY_DELAY`) podobną do tej z `sfa_bbs.py`.
        * Zwraca listę floatów (wektor embeddingowy) lub `None` w przypadku błędu.

    * **`index_reports(qdrant_client: QdrantClient, reports_directory: str)` (NOWA FUNKCJA)**
        * Używa `glob.glob()` do znalezienia wszystkich plików `.txt` w `reports_directory`.
        * Iteruje przez każdy plik:
            * Odczytuje nazwę pliku i wyodrębnia datę (YYYY-MM-DD) z początku nazwy pliku za pomocą wyrażenia regularnego.
            * Wczytuje całą treść raportu.
            * Wywołuje `get_embedding()` dla treści raportu.
            * Jeśli embedding został pomyślnie wygenerowany, tworzy `models.PointStruct`:
                * `id`: Unikalny identyfikator (np. `hash(file_name)`).
                * `vector`: Wygenerowany embedding.
                * `payload`: Słownik zawierający metadane, obowiązkowo `{"date": report_date, "filename": file_name}`.
            * Zbiera punkty do listy i wykonuje `qdrant_client.upsert()` w trybie wsadowym (`wait=True`) po przetworzeniu wszystkich plików.

    * **`find_report_date(qdrant_client: QdrantClient, question: str) -> Optional[str]` (NOWA FUNKCJA)**
        * Przyjmuje pytanie jako argument.
        * Wywołuje `get_embedding()` dla pytania.
        * Wykonuje zapytanie do Qdrant za pomocą `qdrant_client.search()`:
            * `collection_name`: `QDRANT_COLLECTION_NAME`.
            * `query_vector`: Embedding pytania.
            * `limit`: `1` (interesuje nas tylko najbardziej pasujący wynik).
        * Zwraca datę (`"date"`) z `payload` najbardziej pasującego wyniku. Zwraca `None`, jeśli nie znaleziono wyniku lub brakuje metadanych.

    * **`send_report()`:** Zachowaj istniejącą funkcję. Będzie ona używana do wysłania ostatecznej daty do Centrali.

    * **`check_for_flag()`:** Zachowaj istniejącą funkcję. Będzie ona używana do sprawdzenia odpowiedzi z Centrali.

3. **Główna Logika (`main()` Function)**

    * **Walidacja Konfiguracji:** Wywołaj `validate_configuration()`.
    * **Inicjalizacja Qdrant:** Wywołaj `qdrant_client = initialize_qdrant_client()`.
    * **Indeksowanie Raportów:** Wywołaj `index_reports(qdrant_client, REPORTS_DIR)`.
    * **Definicja Pytania:** Zdefiniuj zmienną `question` z treścią pytania.
    * **Wyszukiwanie Daty:** Wywołaj `answer_date = find_report_date(qdrant_client, question)`. Obsłuż przypadek, gdy `answer_date` jest `None`.
    * **Wysłanie Raportu:** Wywołaj `report_response = send_report(REPORT_ENDPOINT, API_KEY, TASK_NAME, answer_date)`.
    * **Sprawdzenie Flagi:** Wywołaj `flag = check_for_flag(report_response)`.
    * **Wyświetlenie Wyników:** Wyświetl flagę lub pełną odpowiedź z Centrali.

#### 5. Zasady Projektowania Oprogramowania

* **DRY (Don't Repeat Yourself):** Unikamy powielania kodu, np. logika ponawiania prób jest scentralizowana w `get_embedding` i `send_report`.
* **KISS (Keep It Simple, Stupid):** Funkcje są proste i skupiają się na jednym zadaniu (np. `get_embedding` tylko generuje embedding, `index_reports` tylko indeksuje).
* **YAGNI (You Aren't Gonna Need It):** Usunięto logikę system promptów i `call_llm`, ponieważ nie jest potrzebna dla modeli embeddingowych.
* **SOLID:**
  * **Single Responsibility Principle:** Każda funkcja ma jasno określoną odpowiedzialność (np. `initialize_qdrant_client` tylko inicjalizuje Qdrant, `index_reports` tylko indeksuje).
  * **Open/Closed Principle:** Nowe funkcjonalności są dodawane poprzez nowe funkcje, a nie modyfikowanie istniejących w sposób, który by je łamał.
* **Obsługa Błędów:** Wszędzie tam, gdzie występują operacje sieciowe lub plikowe, zastosowano bloki `try-except` z logowaniem błędów i mechanizmem ponawiania prób.
* **Logowanie:** Użycie modułu `logging` do śledzenia postępu i diagnozowania problemów.
* **Typowanie (Type Hinting):** Zachowanie i dodanie typowania dla argumentów funkcji i zwracanych wartości dla lepszej czytelności i walidacji kodu.

#### 6. Instrukcje Uruchomienia

1. **Zapisz kod:** Zapisz zaimplementowany kod jako `adw/S03/S03E02/S03E02_agent.py`.
2. **Zainstaluj zależności:**

    ```bash
    pip install -r requirements.txt
    ```

    (Upewnij się, że plik `requirements.txt` zawiera wymienione zależności).
3. **Uruchom Qdrant (jeśli jeszcze nie działa):**

    ```bash
    docker run -p 3333:6333 -p 6334:6334 -d --name qdrant_instance qdrant/qdrant
    ```

    *Uwaga: Zmieniono mapowanie portów na `3333:6333` zgodnie z Twoją prośbą.*
4. **Ustaw zmienne środowiskowe:** Utwórz plik `.env` w katalogu głównym projektu (lub tam, gdzie skrypt go szuka) i dodaj:

    ```
    AIDEVS_API_KEY="YOUR_AIDEVS_API_KEY"
    OPENAI_API_KEY="YOUR_OPENAI_API_KEY"
    # Opcjonalnie, jeśli używasz niestandardowego URL dla OpenAI
    # OPENAI_BASE_URL="https://api.openai.com/v1"
    ```

5. **Uruchom skrypt:**

    ```bash
    python adw/S03/S03E02/S03E02_agent.py
    ```
