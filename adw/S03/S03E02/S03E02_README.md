# S03E02 - Semantic Search of Weapon Reports

## Overview

This project demonstrates a system for performing semantic searches on a collection of weapon test reports. It involves indexing text documents (weapon reports) into a Qdrant vector database using embeddings generated by an AI model (e.g., OpenAI's embedding models or `BAAI/bge-m3`). Once indexed, the system can answer natural language questions by finding the most semantically relevant report and extracting specific information (in this case, the date of the report).

The primary goal is to identify the date of a weapon report that contains information about a specific event (e.g., "theft of a weapon prototype") by leveraging vector similarity search.

---

## Files

### 1. `S03E02_sfa.py`

This is the main "Single File Agent" (SFA) script that orchestrates the entire process. Its key functionalities include:

- **Configuration Management**: Loads necessary API keys, URLs, Qdrant settings, and embedding model details from environment variables.
- **Qdrant Initialization**:
  - Connects to a local Qdrant instance.
  - Ensures a collection named `weapon_reports` exists with the correct vector dimensions. If the collection exists, it is deleted and recreated to ensure schema consistency (especially vector size and distance metric).
- **Embedding Generation**:
  - Uses an embedding model (configurable, e.g., `BAAI/bge-m3` or OpenAI models) to convert text (both report content and search queries) into dense vector representations.
  - Includes retry logic for API calls to the embedding service.
- **Report Indexing**:
  - Scans a specified directory (`REPORTS_DIR`) for weapon report files (assumed to be `.txt` files).
  - Extracts the date from each report's filename (expected format: `YYYY_MM_DD.txt`).
  - For each report:
    - Reads its content.
    - Generates an embedding for the content.
    - Upserts the report into the Qdrant `weapon_reports` collection with its embedding, a unique ID, and metadata (report date and filename).
- **Semantic Search**:
  - Takes a natural language question (e.g., "W raporcie, z którego dnia znajduje się wzmianka o kradzieży prototypu broni?").
  - Generates an embedding for the question.
  - Queries the Qdrant collection to find the report whose content embedding is most similar (cosine similarity) to the question embedding.
  - Extracts the `date` from the metadata of the top matching report.
- **Reporting**:
  - Sends the extracted date as an answer to a specified API endpoint (`REPORT_ENDPOINT`).
  - Includes retry logic for sending the report.
- **Flag Checking**: Checks the response from the reporting API for a "flag," which typically indicates successful completion of a task in a challenge environment.
- **Logging**: Provides detailed logging throughout its execution.

### 2. `S03E02_DELETE_collection.py`

This is a utility script for managing the Qdrant vector database. Its sole purpose is:

- **Collection Deletion**:
  - Connects to the Qdrant instance specified by `QDRANT_HOST` and `QDRANT_PORT`.
  - Checks if the collection named `QDRANT_COLLECTION_NAME` (e.g., `weapon_reports`) exists.
  - If the collection exists, it deletes it.
  - If the collection does not exist, it reports that and exits.

This script is useful for cleaning up the database before a fresh run of `S03E02_sfa.py` or for general maintenance.

---

## Core Functionality: Semantic Search Workflow

The system operates in two main phases:

### 1. Indexing Process (handled by `S03E02_sfa.py`)

1. **Initialization**: The Qdrant client is initialized, and the `weapon_reports` collection is (re)created.
2. **File Discovery**: The script scans the `REPORTS_DIR` for `.txt` files.
3. **Data Extraction & Preprocessing**: For each file:
   - The content is read.
   - The date is extracted from the filename (e.g., `2024_02_21.txt` becomes `2024-02-21`).
4. **Embedding**: The text content of the report is converted into a numerical vector (embedding) using the configured AI model.
5. **Storage**: The embedding, along with metadata (date, filename), is stored as a point in the Qdrant collection. Each point is assigned a unique UUID.

### 2. Querying Process (handled by `S03E02_sfa.py`)

1. **Question Input**: A specific question is defined within the script.
2. **Query Embedding**: The question text is converted into an embedding using the same AI model used for indexing the reports.
3. **Vector Search**: The Qdrant client searches the `weapon_reports` collection for the vector (report embedding) that is most semantically similar to the question embedding. Cosine similarity is used to measure this.
4. **Result Extraction**: The metadata (specifically the `date`) of the top-scoring (most similar) report is retrieved.
5. **Answer Submission**: This date is then submitted as the answer to the task.

---

## Setup and Configuration

### Environment Variables

The script `S03E02_sfa.py` relies on environment variables for configuration. These should be defined in a `.env` file in the same directory or set in the environment.

- `AIDEVS_API_KEY`: API key for the Centrala/AI Devs platform.
- `OPENAI_API_KEY`: API key for OpenAI (or a compatible service like Kluster.ai if `LLM_BASE_URL` is set).
- `API_BASE_URL` (optional, defaults to `https://c3ntrala.ag3nts.org`): Base URL for the AI Devs API.
- `KLUSTER_LLM_MODEL` / `OPENAI_LLM_MODEL` (optional, used for `EMBEDDING_MODEL_NAME`): Name of the embedding model.
- `KLUSTER_API_KEY` / `OPENAI_API_KEY` (used for `LLM_API_KEY`): API key for the embedding model provider.
- `KLUSTER_BASE_URL` / `OPENAI_BASE_URL` (used for `LLM_BASE_URL`): Base URL for the embedding model provider (if not OpenAI default).

### Qdrant Setup

- A Qdrant instance must be running and accessible. The default configuration in the scripts points to `localhost:6333`.
- You can run Qdrant using Docker:

    ```bash
    docker run -p 6333:6333 -p 6334:6334 qdrant/qdrant
    ```

### Python Dependencies

The scripts require several Python packages. You can install them using pip:

```bash
pip install qdrant-client openai python-dotenv requests
```

### Report Files

- The `S03E02_sfa.py` script expects weapon report `.txt` files to be located in the directory specified by `REPORTS_DIR`. The default is `/Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S02/pliki_z_fabryki/weapons_tests/do-not-share/`. Ensure this path is correct or update it in the script.
- Filenames are expected to start with a date in `YYYY_MM_DD` format (e.g., `2023_10_26_report_alpha.txt`).

---

## Usage

### Running the Main Agent (`S03E02_sfa.py`)

1. Ensure your Qdrant instance is running.
2. Set up your `.env` file with the required API keys and configurations.
3. Place the weapon report `.txt` files in the configured `REPORTS_DIR`.
4. Execute the script:

    ```bash
    python /Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S03/S03E02/S03E02_sfa.py
    ```

    The script will:
    - Initialize/recreate the Qdrant collection.
    - Index the reports.
    - Perform the semantic search for the predefined question.
    - Submit the found date to the reporting API.
    - Log its progress and any results/flags.

### Running the Delete Collection Script (`S03E02_DELETE_collection.py`)

1. Ensure your Qdrant instance is running.
2. Execute the script:

    ```bash
    python /Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S03/S03E02/S03E02_DELETE_collection.py
    ```

    This will delete the `weapon_reports` collection from Qdrant if it exists.

---

## Key Technologies Used

- **Python**: Core programming language.
- **Qdrant**: Vector database for storing and searching embeddings.
- **OpenAI API / Compatible Embedding Models**: For generating text embeddings (e.g., `BAAI/bge-m3`).
- **Requests**: For making HTTP API calls.
- **python-dotenv**: For managing environment variables.
- **glob**: For finding files matching a pattern.
- **uuid**: For generating unique IDs for Qdrant points.
