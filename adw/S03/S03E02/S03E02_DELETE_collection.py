import os
from qdrant_client import QdrantClient

# Configuration matching your S03E02_sfa.py
QDRANT_HOST = "localhost"
QDRANT_PORT = 6333
QDRANT_COLLECTION_NAME = "weapon_reports"

def delete_qdrant_collection():
    print(f"Connecting to Qdrant at {QDRANT_HOST}:{QDRANT_PORT}")
    client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)

    print(f"Checking if collection '{QDRANT_COLLECTION_NAME}' exists...")
    if client.collection_exists(collection_name=QDRANT_COLLECTION_NAME):
        print(f"Collection '{QDRANT_COLLECTION_NAME}' found. Deleting...")
        client.delete_collection(collection_name=QDRANT_COLLECTION_NAME)
        print(f"Collection '{QDRANT_COLLECTION_NAME}' deleted successfully.")
    else:
        print(f"Collection '{QDRANT_COLLECTION_NAME}' does not exist. Nothing to delete.")

if __name__ == "__main__":
    delete_qdrant_collection()