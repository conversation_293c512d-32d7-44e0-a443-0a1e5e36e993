"""
Single File Agent (SFA) for Semantic Search of Weapon Reports

This script indexes weapon test reports using OpenAI embeddings into a Qdrant
vector database and then performs a semantic search to answer a specific question,
returning the date of the most relevant report.

Based on Single File Agent Base Schema (SFA-BBS) - Template.

Author: Augment Agent
Date: 2025-05-21
Version: 1.0.0
"""

# --- IMPORTS ---
import os
import re
import json
import logging
import time
import requests
import glob # New import for file listing
import uuid
from typing import Dict, List, Any, Tuple, Optional, Union
from dotenv import load_dotenv
from openai import OpenAI, APIError, APIConnectionError, RateLimitError
from qdrant_client import QdrantClient, models # New imports for Qdrant

# --- CONFIGURATION ---
# Load environment variables
load_dotenv()

# --- APPLICATION SETTINGS ---
# These settings should be customized for each specific agent implementation
TASK_NAME = "wektory" # Updated task name

API_BASE_URL = os.getenv("API_BASE_URL", "https://c3ntrala.ag3nts.org")
# DATA_ENDPOINT is not used in this specific task's workflow as data is local
REPORT_ENDPOINT = f"{API_BASE_URL}/report"
API_KEY = os.getenv("AIDEVS_API_KEY")

# Request settings
REQUEST_TIMEOUT = 10  # seconds
RETRY_ATTEMPTS = 2
RETRY_DELAY = 2  # seconds

# New configuration for reports directory and Qdrant
REPORTS_DIR = "/Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S02/pliki_z_fabryki/weapons_tests/do-not-share/"
QDRANT_HOST = "localhost"
QDRANT_PORT = 6333
QDRANT_COLLECTION_NAME = "weapon_reports"
EMBEDDING_MODEL_NAME = "BAAI/bge-m3"
EMBEDDING_MODEL_DIMENSIONS = 1024 # Dimensions for text-embedding-3-large

# --- LLM SETTINGS ---
# LLM_MODEL will be used for embedding model name.
# LLM_MAX_TOKENS, LLM_TEMPERATURE, LLM_TOP_P are not directly applicable to embedding calls,
# but are kept as general LLM settings from SFA-BBS for consistency if other LLM calls were added.
LLM_MODEL = os.getenv("KLUSTER_LLM_MODEL", os.getenv("OPENAI_LLM_MODEL")) # Used for embedding model name
LLM_API_KEY = os.getenv("KLUSTER_API_KEY", os.getenv("OPENAI_API_KEY")) # Used for OpenAI API key
LLM_BASE_URL = os.getenv("KLUSTER_BASE_URL", os.getenv("OPENAI_BASE_URL")) # Used for OpenAI base URL
LLM_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", "4000")) # Not directly used for embeddings
LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.3")) # Not directly used for embeddings
LLM_TOP_P = float(os.getenv("LLM_TOP_P", "1.0")) # Not directly used for embeddings

# SYSTEM_PROMPT_PATH and DEFAULT_SYSTEM_PROMPT are removed as they are not needed for embedding models.

# --- REGEX PATTERNS ---
# Common regex patterns for flag extraction - customize as needed
FLAG_REGEX = r"FLG[a-zA-Z0-9_-]+"
FLAG_REGEX_CURLY = r"\{\{FLG:[a-zA-Z0-9_-]+\}\}"

# --- LOGGING CONFIGURATION ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# --- HELPER FUNCTIONS ---

def validate_configuration() -> bool:
    """
    Validates that all required configuration variables are set.

    Returns:
        bool: True if configuration is valid, False otherwise
    """
    required_env_vars = {
        "AIDEVS_API_KEY": "API key for Central API",
        "OPENAI_API_KEY": "API key for OpenAI (LLM/Embedding model)",
    }
    missing_vars = []

    for var, description in required_env_vars.items():
        # Check specifically for AIDEVS_API_KEY and OPENAI_API_KEY
        if var == "AIDEVS_API_KEY" and not API_KEY:
            missing_vars.append(f"{var} ({description})")
        elif var == "OPENAI_API_KEY" and not LLM_API_KEY:
            missing_vars.append(f"{var} ({description})")

    if missing_vars:
        logger.critical(f"Missing required environment variables: {', '.join(missing_vars)}")
        return False

    return True

def fetch_data(url: str, api_key: str, attempt: int = 1) -> Optional[Dict]:
    """
    Fetches data from the specified URL with retry logic.
    (This function is part of the SFA-BBS template but not directly used in this task's workflow)

    Args:
        url: The URL to fetch data from
        api_key: API key for authentication
        attempt: Current attempt number (for retry logic)

    Returns:
        Optional[Dict]: The fetched data as a dictionary, or None if failed
    """
    logger.info(f"Fetching data from {url} (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    try:
        response = requests.get(
            url,
            headers=headers,
            timeout=REQUEST_TIMEOUT
        )
        response.raise_for_status()

        data = response.json()
        logger.info("Data fetched successfully")
        return data

    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching data: {e}")

        # Retry logic
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return fetch_data(url, api_key, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response: {e}")
        return None

def send_report(url: str, api_key: str, task_name: str, answer: Any, attempt: int = 1) -> Optional[Dict]:
    """
    Sends the final report/answer to the specified endpoint.

    Args:
        url: The URL to send the report to
        api_key: API key for authentication
        task_name: Name of the task being performed
        answer: The answer/data to send
        attempt: Current attempt number (for retry logic)

    Returns:
        Optional[Dict]: The response from the server, or None if failed
    """
    logger.info(f"Sending report to {url} (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    # Ensure the answer is properly formatted
    # Remove whitespace from beginning and end
    if isinstance(answer, str):
        clean_answer = answer.strip()

        # Remove quotes if present
        if (clean_answer.startswith('"') and clean_answer.endswith('"')) or \
           (clean_answer.startswith("'") and clean_answer.endswith("'")):
            clean_answer = clean_answer[1:-1]

        logger.info(f"Original answer: '{answer}'")
        logger.info(f"Cleaned answer: '{clean_answer}'")
    else:
        clean_answer = answer

    payload = {
        "task": task_name,
        "apikey": api_key,
        "answer": clean_answer
    }

    headers = {
        "Content-Type": "application/json; charset=utf-8"
    }

    # Log full payload before sending
    logger.info(f"Sending payload: {json.dumps(payload, ensure_ascii=False)}")

    try:
        response = requests.post(
            url,
            json=payload,  # Use json instead of data to let requests handle serialization
            headers=headers,
            timeout=REQUEST_TIMEOUT
        )

        # Log full server response
        logger.info(f"Response code: {response.status_code}")
        logger.info(f"Response headers: {dict(response.headers)}")
        logger.info(f"Response content: {response.text}")

        # Display full response in console
        print(f"\n{'='*50}")
        print(f"RESPONSE FROM SERVER:")
        print(f"Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Content: {response.text}")
        print(f"{'='*50}\n")

        response.raise_for_status()

        result = response.json()
        logger.info("Report sent successfully")
        return result

    except requests.exceptions.RequestException as e:
        logger.error(f"Error sending report: {e}")

        if hasattr(e, 'response') and hasattr(e.response, 'text'):
            logger.error(f"Server response: {e.response.text}")
            logger.error(f"Response headers: {dict(e.response.headers) if hasattr(e.response, 'headers') else 'No headers'}")

            # Display full error response in console
            print(f"\n{'='*50}")
            print(f"ERROR FROM SERVER:")
            print(f"Code: {e.response.status_code if hasattr(e.response, 'status_code') else 'No code'}")
            print(f"Headers: {dict(e.response.headers) if hasattr(e.response, 'headers') else 'No headers'}")
            print(f"Content: {e.response.text}")
            print(f"{'='*50}\n")

        # Retry logic
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return send_report(url, api_key, task_name, answer, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response: {e}")
        return None

def check_for_flag(response) -> Optional[str]:
    """
    Sprawdza, czy odpowiedź zawiera flagę w formatach:
    - FLG[a-zA-Z0-9_-+
    - {{FLG:[a-zA-Z0-9_-]+}}

    Args:
        response: Odpowiedź do sprawdzenia (może być string, dict lub inny typ)

    Returns:
        Optional[str]: Znaleziona flaga lub None, jeśli nie znaleziono
    """
    # Convert response to string for easier searching
    response_str = str(response)

    if not response_str:
        return None

    # Search for flag in {{FLG:XXXX}} format
    match_curly = re.search(FLAG_REGEX_CURLY, response_str)
    if match_curly:
        flag = match_curly.group(0)
        logger.info(f"Flag found in {{{{FLG:XXXX}}}} format: {flag}")
        print(f"\n{'='*50}\nFLAG: {flag}\n{'='*50}\n")
        return flag

    # Search for flag in FLG[a-zA-Z0-9_-]+ format
    match = re.search(FLAG_REGEX, response_str)
    if match:
        flag = match.group(0)
        logger.info(f"Flag found in FLG[a-zA-Z0-9_-]+ format: {flag}")
        print(f"\n{'='*50}\nFLAG: {flag}\n{'='*50}\n")
        return flag

    # Check if text contains the word "flag" or "FLG"
    if "flag" in response_str.lower() or "flg" in response_str.lower():
        logger.info(f"Text contains the word 'flag' or 'FLG': {response_str}")
        print(f"\n{'='*50}\nPOTENTIAL FLAG: {response_str}\n{'='*50}\n")
        return response_str

    logger.info("No flag found in response")
    return None

# --- NEW HELPER FUNCTIONS FOR QDRANT AND EMBEDDINGS ---

def initialize_qdrant_client() -> QdrantClient:
    """
    Initializes Qdrant client and creates a collection for weapon reports.
    Ensures the collection has the correct dimensions by recreating it if it exists.
    """
    logger.info(f"Initializing Qdrant client at {QDRANT_HOST}:{QDRANT_PORT}")
    client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)

    logger.info(f"Checking if collection '{QDRANT_COLLECTION_NAME}' exists...")
    if client.collection_exists(collection_name=QDRANT_COLLECTION_NAME):
        logger.info(f"Collection '{QDRANT_COLLECTION_NAME}' exists. Deleting and recreating to ensure correct dimensions.")
        client.delete_collection(collection_name=QDRANT_COLLECTION_NAME)
        logger.info(f"Collection '{QDRANT_COLLECTION_NAME}' deleted.")
    else:
        logger.info(f"Collection '{QDRANT_COLLECTION_NAME}' not found.")

    logger.info(f"Creating collection '{QDRANT_COLLECTION_NAME}' with {EMBEDDING_MODEL_DIMENSIONS} dimensions...")
    client.create_collection(
        collection_name=QDRANT_COLLECTION_NAME,
        vectors_config=models.VectorParams(size=EMBEDDING_MODEL_DIMENSIONS, distance=models.Distance.COSINE),
    )
    logger.info(f"Collection '{QDRANT_COLLECTION_NAME}' created successfully.")
    return client

def get_embedding(text: str, attempt: int = 1) -> Optional[List[float]]:
    """
    Generates an embedding for the given text using the specified embedding model.
    Includes retry logic.
    """
    logger.info(f"Generating embedding (attempt {attempt}/{RETRY_ATTEMPTS + 1})")
    try:
        client_params = {"api_key": LLM_API_KEY}
        if LLM_BASE_URL:
            client_params["base_url"] = LLM_BASE_URL
        client = OpenAI(**client_params)

        response = client.embeddings.create(
            input=text,
            model=EMBEDDING_MODEL_NAME,
            encoding_format="float" # Added as per Kluster.ai example
        )
        embedding_vector = response.data[0].embedding
        logger.info("Embedding generated successfully.")
        return embedding_vector
    except (APIError, APIConnectionError, RateLimitError) as e:
        logger.error(f"Embedding API error: {e}")
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return get_embedding(text, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached for embedding generation.")
            return None
    except Exception as e:
        logger.error(f"Unexpected error during embedding generation: {e}")
        return None

def index_reports(qdrant_client: QdrantClient, reports_directory: str):
    """
    Reads report files, generates embeddings, and indexes them in Qdrant.
    """
    logger.info(f"Starting indexing of reports from: {reports_directory}")
    report_files = glob.glob(os.path.join(reports_directory, "*.txt"))
    if not report_files:
        logger.warning(f"No .txt files found in {reports_directory}. Skipping indexing.")
        return

    points_to_upsert = []
    for file_path in report_files:
        file_name = os.path.basename(file_path)
        try:
            # Extract date from filename (e.g., 2024_02_21.txt)
            # Updated regex to match YYYY_MM_DD format
            date_match = re.match(r"(\d{4}_\d{2}_\d{2})", file_name)
            if not date_match:
                logger.warning(f"Could not extract date from filename: {file_name}. Skipping.")
                continue
            
            # Convert extracted date from YYYY_MM_DD to YYYY-MM-DD
            report_date_raw = date_match.group(1)
            report_date = report_date_raw.replace('_', '-') # Convert to YYYY-MM-DD

            with open(file_path, 'r', encoding='utf-8') as f:
                report_content = f.read()

            embedding = get_embedding(report_content)
            if embedding:
                points_to_upsert.append(
                    models.PointStruct(
                        id=str(uuid.uuid4()), # Changed to use UUID for unique and valid IDs
                        vector=embedding,
                        payload={"date": report_date, "filename": file_name}
                    )
                )
                logger.info(f"Prepared embedding for {file_name} (Date: {report_date})")
            else:
                logger.error(f"Failed to get embedding for {file_name}. Skipping.")

        except Exception as e:
            logger.error(f"Error processing file {file_name}: {e}")

    if points_to_upsert:
        logger.info(f"Upserting {len(points_to_upsert)} points to Qdrant collection '{QDRANT_COLLECTION_NAME}'...")
        qdrant_client.upsert(
            collection_name=QDRANT_COLLECTION_NAME,
            wait=True,
            points=points_to_upsert
        )
        logger.info("Reports indexed successfully in Qdrant.")
    else:
        logger.info("No points to upsert. Indexing complete (or no valid reports found).")

def find_report_date(qdrant_client: QdrantClient, question: str) -> Optional[str]:
    """
    Generates embedding for the question, queries Qdrant, and returns the date
    from the most relevant report's metadata.
    """
    logger.info(f"Searching for answer to question: '{question}'")
    question_embedding = get_embedding(question)
    if not question_embedding:
        logger.error("Failed to get embedding for the question.")
        return None

    search_result = qdrant_client.search(
        collection_name=QDRANT_COLLECTION_NAME,
        query_vector=question_embedding,
        limit=1
    )

    if search_result and search_result[0].payload:
        found_date = search_result[0].payload.get("date")
        found_filename = search_result[0].payload.get("filename")
        logger.info(f"Found most relevant report: {found_filename} (Date: {found_date})")
        return found_date
    else:
        logger.warning("No relevant report found in Qdrant or missing date metadata.")
        return None

# --- MAIN FUNCTION ---

def main():
    """
    Main function that orchestrates the agent's workflow.
    """
    logger.info(f"--- Starting {TASK_NAME} agent ---")

    # Validate configuration
    if not validate_configuration():
        logger.critical("Invalid configuration. Exiting.")
        return

    try:
        # Step 1: Initialize Qdrant client and collection
        qdrant_client = initialize_qdrant_client()

        # Step 2: Index reports
        index_reports(qdrant_client, REPORTS_DIR)

        # Step 3: Define the question
        question = "W raporcie, z którego dnia znajduje się wzmianka o kradzieży prototypu broni?"

        # Step 4: Find the report date by querying Qdrant
        answer_date = find_report_date(qdrant_client, question)
        if not answer_date:
            logger.error("Failed to find the answer date. Exiting.")
            return

        # Step 5: Send report to Centrala
        report_response = send_report(REPORT_ENDPOINT, API_KEY, TASK_NAME, answer_date)
        if not report_response:
            logger.error("Failed to send report. Exiting.")
            return

        # Step 6: Check for flag in response
        flag = check_for_flag(report_response)

        # Step 7: Display results
        if flag:
            logger.info(f"SUCCESS! Flag: {flag}")
            print(f"\n{'='*50}\nFLAG: {flag}\n{'='*50}\n")
        else:
            logger.info("Task completed, but no flag found in response")
            print(f"\n{'='*50}\nRESPONSE: {json.dumps(report_response, indent=2)}\n{'='*50}\n")

    except Exception as e:
        logger.critical(f"Unexpected error: {e}", exc_info=True)

    logger.info(f"--- {TASK_NAME} agent finished ---")

# --- SCRIPT ENTRY POINT ---

if __name__ == "__main__":
    # Ensure the directory exists before creating the file
    os.makedirs(os.path.dirname("adw/S03/S03E02/S03E02_sfa.py"), exist_ok=True)
    main()