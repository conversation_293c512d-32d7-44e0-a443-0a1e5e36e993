# Analiza Zadania: Wyszukiwanie Semantyczne Raportów Broni

## 1. Wyjaśnienie Zadania

Głównym celem zadania jest zbudowanie systemu zdolnego do przeszukiwania raportów z testów broni w oparciu o ich semantyczne znaczenie, a nie tylko słowa kluczowe. System ten ma za zadanie zidentyfikować konkretny raport, który odpowiada na zadane pytanie, wykorzystując technologię embeddingów tekstowych i baz wektorowych.

**Kluczowe etapy zadania:**

* **Generowanie Embeddingów:** Każdy raport tekstowy zostanie przekształcony w wektor liczbowy (embedding) za pomocą specjalnego modelu językowego. Wektory te uchwytują semantyczne znaczenie tekstu, co oznacza, że teksty o podobnym znaczeniu będą miały "bliskie" wektory w przestrzeni wielowymiarowej.
* **Indeksowanie w Bazie Wektorowej:** Wygenerowane wektory, wraz z kluczowymi metadanymi (takimi jak data raportu), zostaną zapisane w bazie wektorowej. Bazy te są zoptymalizowane do szybkiego wyszukiwania podobnych wektorów.
* **Zapytanie Semantyczne:** Zadane pytanie również zostanie przekształcone w embedding. Ten wektor zapytania posłuży do przeszukania bazy wektorowej w celu znalezienia raportu, którego wektor jest najbardziej podobny, czyli raportu semantycznie najbardziej zbliżonego do pytania.
* **Odczytanie i Przesłanie Odpowiedzi:** Z najbardziej pasującego raportu zostanie odczytana data z jego metadanych i przesłana w wymaganym formacie.

Zadanie to ma na celu praktyczne zapoznanie się z działaniem baz wektorowych, które są fundamentalne dla nowoczesnych systemów wyszukiwania, rekomendacji i RAG (Retrieval Augmented Generation) w kontekście dużych modeli językowych.

## 2. Propozycja Optymalnego Planu Działania

1. **Przygotowanie Środowiska:**
    * Utwórz nowe środowisko wirtualne Pythona.
    * Aktywuj środowisko.
    * Zainstaluj niezbędne biblioteki: `requests`, `python-dotenv`, bibliotekę klienta dla wybranej bazy wektorowej (np. `qdrant-client`), oraz bibliotekę do obsługi modelu embeddingowego (np. `openai`).
    * Jeśli wybierzesz Qdrant lokalnie, upewnij się, że masz zainstalowanego Dockera i pobierz obraz Qdrant (`docker pull qdrant/qdrant`).

2. **Konfiguracja Bazy Wektorowej:**
    * **Wybierz model embeddingowy jako pierwszy!** To krytyczne, ponieważ od niego zależy wymiar wektorów. Rekomendowany `text-embedding-3-large` od OpenAI generuje wektory o 3072 wymiarach.
    * Uruchom Qdrant w Dockerze (jeśli go wybrałeś): `docker run -p 6333:6333 -p 6334:6334 -d --name qdrant_instance qdrant/qdrant`.
    * W skrypcie Pythona zainicjuj klienta Qdrant.
    * Utwórz nową kolekcję w Qdrant, podając **dokładny wymiar (np. 3072 dla text-embedding-3-large)**.

3. **Indeksowanie Raportów:**
    * Przejdź przez każdy plik `.txt` znajdujący się w katalogu: `/Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S02/pliki_z_fabryki/weapons_tests/do-not-share/`.
    * Dla każdego pliku:
        * Wyodrębnij datę z nazwy pliku (np. `2024-02-21_XR-5_report.txt` -> `2024-02-21`). Upewnij się, że format to `YYYY-MM-DD`.
        * Wczytaj całą treść raportu.
        * Wyślij treść raportu do wybranego modelu embeddingowego (np. API OpenAI), aby uzyskać wektor.
        * Zapisz ten wektor w kolekcji Qdrant, dołączając metadane, takie jak `date` (z wyodrębnioną datą) i opcjonalnie `filename`.

4. **Zadanie Pytania i Wyszukiwanie:**
    * Zdefiniuj pytanie: `"W raporcie, z którego dnia znajduje się wzmianka o kradzieży prototypu broni?"`
    * Wygeneruj embedding dla tego pytania, używając **TEGO SAMEGO** modelu embeddingowego, którego użyłeś do indeksowania raportów.
    * Wykonaj zapytanie do Qdrant, używając wektora pytania. Ustaw `limit=1`, aby otrzymać tylko najbardziej pasujący wynik.

5. **Odczytanie i Przesłanie Odpowiedzi:**
    * Z otrzymanego wyniku (najbardziej pasującego raportu) odczytaj datę z jego metadanych.
    * Zbuduj obiekt JSON w formacie: `{"task": "wektory", "apikey": "YOUR_API_KEY", "answer": "YYYY-MM-DD"}`.
    * Wyślij ten obiekt JSON jako POST request do endpointu `/report` w centrali.

6. **Czyszczenie (Opcjonalnie):**
    * Zatrzymaj i usuń kontener Qdrant (jeśli używasz Dockera i nie potrzebujesz go dalej).

## 3. Propozycja Stacku Technologicznego

* **Język Programowania:** Python (standardowy wybór dla zadań z LLM i przetwarzania danych).
* **Obsługa Plików:**
  * `os` / `pathlib`: Do operacji na ścieżkach plików i katalogach oraz odczytu plików tekstowych.
* **Zarządzanie Kluczami API:**
  * `python-dotenv`: Do bezpiecznego przechowywania klucza API w pliku `.env` i ładowania go do zmiennych środowiskowych.
* **Baza Wektorowa:**
  * **Qdrant:** Rekomendowany przez zadanie. Możliwość uruchomienia lokalnie za pomocą Dockera. Posiada dedykowany klient Pythona (`qdrant-client`).
  * *Alternatywy (jeśli Qdrant sprawi problemy):* `ChromaDB` (bardzo prosta w użyciu, może działać w trybie embedded bez serwera), `LanceDB` (również embedded, serwerless).
* **Model Embeddingowy:**
  * **OpenAI `text-embedding-3-large`:** Najbardziej sugerowany i bardzo skuteczny. Wymaga klucza API OpenAI.
  * *Alternatywy:* Modele od Jina AI, Cohere, lub open-source'owe takie jak `all-MiniLM-L6-v2` (można je uruchomić lokalnie za pomocą biblioteki `sentence-transformers`). Pamiętaj, że musisz użyć tego samego modelu do indeksowania i do zapytania.
* **Komunikacja HTTP:**
  * `requests`: Do wysyłania końcowej odpowiedzi do centrali.
* **Zarządzanie Zależnościami:**
  * `pip` z plikiem `requirements.txt`.
