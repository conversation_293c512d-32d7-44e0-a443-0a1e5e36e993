# Plan implementacji SFA dla zadania S03E03 - ZENCODER

## 1. Wprowadzenie

Plan implementacji Single File Agent (SFA) dla zadania S03E03, które polega na komunikacji z bazą danych firmy BanAN poprzez specjalne API w celu znalezienia numerów ID aktywnych centrów danych zarządzanych przez nieaktywnych menedżerów.

## 2. Struktura pliku SFA

Plik SFA będzie zawierał następujące sekcje:

1. **Importy i konfiguracja**
   - Importy niezbędnych bibliotek
   - Ładowanie zmiennych środowiskowych

2. **Stałe i zmienne środowiskowe**
   - Konfiguracja endpointów API
   - Konfiguracja LLM
   - Ścieżki do plików z system promptami
   - Wzorce regex do wykrywania flag

3. **Konfiguracja logowania**
   - Ustawienie poziomu logowania
   - Format logów

4. **Funkcje pomocnicze**
   - Walidacja konfiguracji
   - Pobieranie danych z API
   - Ładowanie system promptów
   - Komunikacja z LLM
   - Wysyłanie raportów
   - Wykrywanie flag

5. **Funkcje specyficzne dla zadania**
   - Wykonywanie zapytań SQL
   - Odkrywanie struktury bazy danych
   - Analiza struktury bazy danych z użyciem LLM
   - Generowanie zapytań SQL z użyciem LLM
   - Debugowanie zapytań SQL
   - Przetwarzanie wyników zapytań

6. **Funkcja główna (main)**
   - Koordynacja workflow
   - Obsługa błędów

7. **Punkt wejścia skryptu**
   - Wywołanie funkcji main

## 3. Szczegółowy opis implementacji

### 3.1. Importy i konfiguracja

```python
import os
import re
import json
import logging
import time
import requests
from typing import Dict, List, Any, Tuple, Optional, Union
from dotenv import load_dotenv
from openai import OpenAI, APIError, APIConnectionError, RateLimitError
```

### 3.2. Stałe i zmienne środowiskowe

```python
# Load environment variables
load_dotenv()

# Application settings
TASK_NAME = "database"
API_BASE_URL = "https://c3ntrala.ag3nts.org"
DATA_ENDPOINT = f"{API_BASE_URL}/apidb"
REPORT_ENDPOINT = f"{API_BASE_URL}/report"
API_KEY = os.getenv("AIDEVS_API_KEY")

# Request settings
REQUEST_TIMEOUT = 10  # seconds
RETRY_ATTEMPTS = 2
RETRY_DELAY = 2  # seconds

# LLM settings
LLM_MODEL = os.getenv("KLUSTER_LLM_MODEL", os.getenv("OPENAI_LLM_MODEL"))
LLM_API_KEY = os.getenv("KLUSTER_API_KEY", os.getenv("OPENAI_API_KEY"))
LLM_BASE_URL = os.getenv("KLUSTER_BASE_URL", os.getenv("OPENAI_BASE_URL"))
LLM_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", "4000"))
LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.3"))
LLM_TOP_P = float(os.getenv("LLM_TOP_P", "1.0"))

# System prompt paths
SYSTEM_PROMPT_ANALYZE_SCHEMA = os.path.join(os.path.dirname(os.path.abspath(__file__)), "S03E03_ZEN_system_prompt_analiza_schematow.md")
SYSTEM_PROMPT_GENERATE_SQL = os.path.join(os.path.dirname(os.path.abspath(__file__)), "S03E03_ZEN_system_prompt_generowanie_sql.md")
SYSTEM_PROMPT_DEBUG_SQL = os.path.join(os.path.dirname(os.path.abspath(__file__)), "S03E03_ZEN_system_prompt_debugowanie_sql.md")
SYSTEM_PROMPT_INTERPRET_RESULTS = os.path.join(os.path.dirname(os.path.abspath(__file__)), "S03E03_ZEN_system_prompt_interpretacja_wynikow.md")

# Default system prompt (used if file not found)
DEFAULT_SYSTEM_PROMPT = """
You are an AI assistant helping with SQL database queries.
Provide clear, concise, and accurate responses.
"""

# Regex patterns for flag extraction
FLAG_REGEX = r"FLG[a-zA-Z0-9_-]+"
FLAG_REGEX_CURLY = r"\{\{FLG:[a-zA-Z0-9_-]+\}\}"
```

### 3.3. Konfiguracja logowania

```python
# Logging configuration
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)
```

### 3.4. Funkcje pomocnicze

#### 3.4.1. Walidacja konfiguracji

```python
def validate_configuration() -> bool:
    """
    Validates that all required configuration variables are set.
    """
    # Sprawdzenie czy wszystkie wymagane zmienne środowiskowe są ustawione
    # Zwraca True jeśli konfiguracja jest poprawna, False w przeciwnym przypadku
```

#### 3.4.2. Pobieranie danych z API

```python
def fetch_data(url: str, api_key: str, attempt: int = 1) -> Optional[Dict]:
    """
    Fetches data from the specified URL with retry logic.
    """
    # Pobieranie danych z API z obsługą błędów i mechanizmem retry
    # Zwraca dane jako słownik lub None w przypadku błędu
```

#### 3.4.3. Ładowanie system promptów

```python
def load_system_prompt(prompt_path: str) -> str:
    """
    Loads system prompt from file.
    """
    # Ładowanie system promptu z pliku
    # Zwraca domyślny prompt w przypadku błędu
```

#### 3.4.4. Komunikacja z LLM

```python
def call_llm(prompt: str, system_prompt_path: str = None, attempt: int = 1, max_retries: int = 3) -> Optional[str]:
    """
    Calls the LLM with the given prompt and handles errors with retry logic.
    """
    # Wywołanie LLM z obsługą błędów i mechanizmem retry
    # Zwraca odpowiedź LLM lub None w przypadku błędu
```

#### 3.4.5. Wysyłanie raportów

```python
def send_report(url: str, api_key: str, task_name: str, answer: Any, attempt: int = 1) -> Optional[Dict]:
    """
    Sends the final report/answer to the specified endpoint.
    """
    # Wysyłanie raportu do API z obsługą błędów i mechanizmem retry
    # Zwraca odpowiedź serwera lub None w przypadku błędu
```

#### 3.4.6. Wykrywanie flag

```python
def check_for_flag(response) -> Optional[str]:
    """
    Checks if the response contains a flag in various formats.
    """
    # Sprawdzanie czy odpowiedź zawiera flagę w różnych formatach
    # Zwraca znalezioną flagę lub None jeśli nie znaleziono
```

### 3.5. Funkcje specyficzne dla zadania

#### 3.5.1. Wykonywanie zapytań SQL

```python
def execute_sql_query(query: str, api_key: str, attempt: int = 1) -> Optional[Dict]:
    """
    Executes SQL query via the API and returns the results.
    """
    # Wykonanie zapytania SQL poprzez API
    # Zwraca wyniki zapytania lub None w przypadku błędu
```

#### 3.5.2. Odkrywanie struktury bazy danych

```python
def discover_database_structure(api_key: str) -> Optional[Dict]:
    """
    Discovers the database structure by querying table schemas.
    """
    # Odkrywanie struktury bazy danych (tabele i schematy)
    # Zwraca strukturę bazy danych lub None w przypadku błędu
```

#### 3.5.3. Analiza struktury bazy danych z użyciem LLM

```python
def analyze_database_structure(db_structure: Dict) -> Optional[str]:
    """
    Uses LLM to analyze the database structure and identify key relationships.
    """
    # Analiza struktury bazy danych przy użyciu LLM
    # Zwraca analizę lub None w przypadku błędu
```

#### 3.5.4. Generowanie zapytań SQL z użyciem LLM

```python
def generate_sql_query(db_structure: Dict, analysis: str) -> Optional[str]:
    """
    Uses LLM to generate SQL query based on database structure and analysis.
    """
    # Generowanie zapytania SQL przy użyciu LLM
    # Zwraca zapytanie SQL lub None w przypadku błędu
```

#### 3.5.5. Debugowanie zapytań SQL

```python
def debug_sql_query(query: str, error_message: str, db_structure: Dict) -> Optional[str]:
    """
    Uses LLM to debug and fix SQL query if it fails.
    """
    # Debugowanie i poprawianie zapytania SQL przy użyciu LLM
    # Zwraca poprawione zapytanie SQL lub None w przypadku błędu
```

#### 3.5.6. Przetwarzanie wyników zapytań

```python
def process_query_results(results: Dict) -> List[int]:
    """
    Processes query results to extract datacenter IDs.
    """
    # Przetwarzanie wyników zapytania SQL
    # Zwraca listę ID centrów danych
```

### 3.6. Funkcja główna (main)

```python
def main():
    """
    Main function that orchestrates the agent's workflow.
    """
    # Koordynacja workflow agenta
    # 1. Walidacja konfiguracji
    # 2. Odkrycie struktury bazy danych
    # 3. Analiza struktury bazy danych przy użyciu LLM
    # 4. Generowanie zapytania SQL przy użyciu LLM
    # 5. Wykonanie zapytania SQL z obsługą błędów i mechanizmem retry
    # 6. Przetworzenie wyników zapytania
    # 7. Wysłanie odpowiedzi do centrali
    # 8. Sprawdzenie odpowiedzi pod kątem flagi
    # 9. Wyświetlenie wyników
```

### 3.7. Punkt wejścia skryptu

```python
if __name__ == "__main__":
    main()
```

## 4. Workflow aplikacji

1. **Walidacja konfiguracji**
   - Sprawdzenie czy wszystkie wymagane zmienne środowiskowe są ustawione

2. **Odkrycie struktury bazy danych**
   - Wykonanie zapytania `SHOW TABLES` aby pobrać listę tabel
   - Wykonanie zapytań `SHOW CREATE TABLE` dla każdej tabeli
   - Zebranie schematów tabel

3. **Analiza struktury bazy danych**
   - Wykorzystanie LLM do analizy schematów tabel
   - Identyfikacja kluczowych pól i relacji między tabelami

4. **Generowanie zapytania SQL**
   - Wykorzystanie LLM do wygenerowania zapytania SQL
   - Zapytanie powinno znaleźć aktywne datacenter zarządzane przez nieaktywnych menedżerów

5. **Wykonanie zapytania SQL**
   - Wysłanie zapytania SQL do API
   - W przypadku błędu, wykorzystanie LLM do debugowania i poprawienia zapytania
   - Ponowne próby wykonania zapytania (maksymalnie 3)

6. **Przetwarzanie wyników**
   - Ekstrakcja ID centrów danych z wyników zapytania
   - Usunięcie duplikatów i sortowanie

7. **Wysłanie odpowiedzi**
   - Przygotowanie odpowiedzi w formacie JSON
   - Wysłanie odpowiedzi do centrali

8. **Sprawdzenie odpowiedzi**
   - Sprawdzenie czy odpowiedź zawiera flagę
   - Wyświetlenie flagi lub odpowiedzi

## 5. Obsługa błędów i mechanizmy bezpieczeństwa

1. **Retry dla operacji sieciowych**
   - Automatyczne ponawianie prób w przypadku błędów sieciowych
   - Konfigurowalny limit prób i opóźnienie między próbami

2. **Limit prób dla zapytań LLM**
   - Maksymalnie 3 próby dla każdego zapytania do LLM
   - Obsługa błędów API LLM

3. **Walidacja danych**
   - Sprawdzanie poprawności danych na każdym etapie przetwarzania
   - Obsługa przypadków brzegowych

4. **Logowanie**
   - Szczegółowe logowanie wszystkich operacji
   - Logowanie błędów z pełnym kontekstem

## 6. Wykorzystanie LLM

1. **Analiza schematów**
   - LLM analizuje strukturę bazy danych
   - Identyfikuje kluczowe relacje i pola

2. **Generowanie SQL**
   - LLM generuje zapytanie SQL na podstawie analizy schematów
   - Zapytanie jest optymalizowane pod kątem wydajności

3. **Debugowanie SQL**
   - LLM pomaga w debugowaniu i poprawianiu błędnych zapytań
   - Analiza błędów i sugestie poprawek

## 7. Wymagania techniczne

1. **Język programowania**: Python 3.8+
2. **Biblioteki**:
   - `requests` - do komunikacji HTTP z API
   - `openai` - do integracji z LLM
   - `dotenv` - do zarządzania zmiennymi środowiskowymi
   - `logging` - do logowania
   - `json` - do przetwarzania danych w formacie JSON
   - `re` - do obsługi wyrażeń regularnych
   - `time` - do obsługi opóźnień

3. **Zmienne środowiskowe**:
   - `AIDEVS_API_KEY` - klucz API do centrali
   - `KLUSTER_API_KEY` - klucz API do Kluster.ai
   - Opcjonalnie: `KLUSTER_LLM_MODEL`, `KLUSTER_BASE_URL`

## 8. Testowanie

1. **Testy jednostkowe**
   - Testowanie poszczególnych funkcji w izolacji
   - Mockowanie odpowiedzi API

2. **Testy integracyjne**
   - Testowanie interakcji między komponentami
   - Testowanie komunikacji z API

3. **Testy end-to-end**
   - Testowanie całego workflow
   - Weryfikacja poprawności wyników

## 9. Podsumowanie

Plan implementacji SFA dla zadania S03E03 obejmuje wszystkie niezbędne elementy do realizacji zadania zgodnie z wymaganiami. Aplikacja będzie w stanie odkryć strukturę bazy danych, wygenerować odpowiednie zapytanie SQL, wykonać je i przetworzyć wyniki, a następnie wysłać odpowiedź do centrali.

Implementacja będzie zgodna z najlepszymi praktykami programowania w Pythonie, z odpowiednim podziałem na funkcje zgodnie z zasadami DRY, KISS, YAGNI i SOLID. Aplikacja będzie również odporna na błędy dzięki mechanizmom retry i walidacji danych.
