"""
Single File Agent (SFA) for S03E03 - Database Query Task

This script implements a Single File Agent that communicates with BanAN's database API
to find IDs of active datacenters managed by inactive managers.

The agent follows these steps:
1. Discover database structure (tables and schemas)
2. Analyze the structure using LLM
3. Generate SQL query using LLM
4. Execute the query and process results
5. Send the answer to the central API

Author: AI Agent
Date: 2025-05-21
Version: 1.1.0
"""

# --- IMPORTS ---
import os
import re
import json
import logging
import time
import requests
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv
from openai import OpenAI, APIError, APIConnectionError, RateLimitError

# --- CONFIGURATION ---
# Load environment variables
load_dotenv()

# --- APPLICATION SETTINGS ---
TASK_NAME = "database"
API_BASE_URL = "https://c3ntrala.ag3nts.org"
DATA_ENDPOINT = f"{API_BASE_URL}/apidb"
REPORT_ENDPOINT = f"{API_BASE_URL}/report"
API_KEY = os.getenv("AIDEVS_API_KEY")

# Request settings
REQUEST_TIMEOUT = 10  # seconds
RETRY_ATTEMPTS = 2
RETRY_DELAY = 2  # seconds

# --- LLM SETTINGS ---
LLM_MODEL = os.getenv("KLUSTER_LLM_MODEL", os.getenv("OPENAI_LLM_MODEL"))
LLM_API_KEY = os.getenv("KLUSTER_API_KEY", os.getenv("OPENAI_API_KEY"))
LLM_BASE_URL = os.getenv("KLUSTER_BASE_URL", os.getenv("OPENAI_BASE_URL"))
LLM_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", "4000"))
LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.3"))
LLM_TOP_P = float(os.getenv("LLM_TOP_P", "1.0"))

# System prompt paths
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
SYSTEM_PROMPT_ANALYZE_SCHEMA = os.path.join(CURRENT_DIR, "S03E03_ZEN_system_prompt_analiza_schematow.md")
SYSTEM_PROMPT_GENERATE_SQL = os.path.join(CURRENT_DIR, "S03E03_ZEN_system_prompt_generowanie_sql.md")
SYSTEM_PROMPT_DEBUG_SQL = os.path.join(CURRENT_DIR, "S03E03_ZEN_system_prompt_debugowanie_sql.md")
SYSTEM_PROMPT_INTERPRET_RESULTS = os.path.join(CURRENT_DIR, "S03E03_ZEN_system_prompt_interpretacja_wynikow.md")

# Default system prompt (used if file not found)
DEFAULT_SYSTEM_PROMPT = """
You are an AI assistant helping with SQL database queries.
Provide clear, concise, and accurate responses.
"""

# --- REGEX PATTERNS ---
# Common regex patterns for flag extraction
FLAG_REGEX = r"FLG[a-zA-Z0-9_-]+"
FLAG_REGEX_CURLY = r"\{\{FLG:[a-zA-Z0-9_-]+\}\}"

# --- LOGGING CONFIGURATION ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# --- HELPER FUNCTIONS ---

def validate_configuration() -> bool:
    """
    Validates that all required configuration variables are set.

    Returns:
        bool: True if configuration is valid, False otherwise
    """
    required_env_vars = {
        "AIDEVS_API_KEY": "API key for Central API",
        "KLUSTER_API_KEY": "API key for Kluster.ai",
    }
    missing_vars = []

    for var, description in required_env_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"{var} ({description})")

    if missing_vars:
        logger.critical(f"Missing required environment variables: {', '.join(missing_vars)}")
        return False

    # Check if API_KEY is set
    if not API_KEY:
        logger.critical("API_KEY is not set, check AIDEVS_API_KEY environment variable")
        return False

    # Check if LLM_API_KEY is set
    if not LLM_API_KEY:
        logger.warning("LLM_API_KEY is not set, using KLUSTER_API_KEY")

    return True

def fetch_data(url: str, api_key: str, attempt: int = 1) -> Optional[Dict]:
    """
    Fetches data from the specified URL with retry logic.

    Args:
        url: The URL to fetch data from
        api_key: API key for authentication
        attempt: Current attempt number (for retry logic)

    Returns:
        Optional[Dict]: The fetched data as a dictionary, or None if failed
    """
    logger.info(f"Fetching data from {url} (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    try:
        response = requests.get(
            url,
            headers=headers,
            timeout=REQUEST_TIMEOUT
        )
        response.raise_for_status()

        data = response.json()
        logger.info("Data fetched successfully")
        return data

    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching data: {e}")

        # Retry logic
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return fetch_data(url, api_key, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response: {e}")
        return None

def load_system_prompt(prompt_path: str) -> str:
    """
    Loads system prompt from file.

    Args:
        prompt_path: Path to the system prompt file

    Returns:
        str: The system prompt content
    """
    try:
        with open(prompt_path, 'r', encoding='utf-8') as file:
            system_prompt = file.read()
        logger.info(f"Loaded system prompt from file: {prompt_path}")
        return system_prompt
    except Exception as e:
        logger.error(f"Error loading system prompt: {e}")
        logger.warning("Using default system prompt")
        return DEFAULT_SYSTEM_PROMPT

def call_llm(prompt: str, system_prompt_path: str = None, attempt: int = 1, max_retries: int = 3, is_sql_generation: bool = False) -> Optional[str]:
    """
    Calls the LLM with the given prompt and handles errors with retry logic.

    Args:
        prompt: The user prompt to send to the LLM
        system_prompt_path: Path to system prompt file
        attempt: Current attempt number (for retry logic)
        max_retries: Maximum number of retry attempts
        is_sql_generation: Flag indicating if this is a SQL generation call

    Returns:
        Optional[str]: The LLM's response, or None if failed
    """
    logger.info(f"Calling LLM with prompt (attempt {attempt}/{max_retries})")

    try:
        # Load system prompt from file or use default
        if system_prompt_path and os.path.exists(system_prompt_path):
            system_prompt = load_system_prompt(system_prompt_path)
        else:
            system_prompt = DEFAULT_SYSTEM_PROMPT
            
        # For SQL generation, add additional instructions to system prompt
        if is_sql_generation and system_prompt_path == SYSTEM_PROMPT_GENERATE_SQL:
            system_prompt += "\n\nCRITICAL INSTRUCTION: Return ONLY the raw SQL query without ANY explanations, comments, markdown formatting, or code blocks. JUST THE PLAIN SQL TEXT."

        client_params = {"api_key": LLM_API_KEY}
        if LLM_BASE_URL:
            client_params["base_url"] = LLM_BASE_URL

        client = OpenAI(**client_params)

        logger.info(f"Using model: {LLM_MODEL} via API: {LLM_BASE_URL}")

        # For SQL generation, use lower temperature
        temp = 0.1 if is_sql_generation else LLM_TEMPERATURE

        completion = client.chat.completions.create(
            model=LLM_MODEL,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            temperature=temp,
            max_tokens=LLM_MAX_TOKENS,
            top_p=LLM_TOP_P
        )

        response = completion.choices[0].message.content.strip()
        logger.info("LLM call successful")
        return response

    except (APIError, APIConnectionError, RateLimitError) as e:
        logger.error(f"LLM API error: {e}")

        # Retry logic
        if attempt < max_retries:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return call_llm(prompt, system_prompt_path, attempt + 1, max_retries, is_sql_generation)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except Exception as e:
        logger.error(f"Unexpected error during LLM call: {e}")
        return None

def clean_answer_data(answer: Any) -> Any:
    """
    Ensures the answer is properly formatted.
    For this task, we need to ensure the answer is a list of integers.

    Args:
        answer: The raw answer data

    Returns:
        Properly formatted answer
    """
    if isinstance(answer, list):
        # Convert all elements to integers
        try:
            return [int(item) for item in answer]
        except (ValueError, TypeError) as e:
            logger.error(f"Error converting answer to integers: {e}")
            # If conversion fails, try to extract integers from the list
            clean_answer = []
            for item in answer:
                try:
                    if isinstance(item, dict) and "dc_id" in item:
                        clean_answer.append(int(item["dc_id"]))
                    elif isinstance(item, dict) and "id" in item:
                        clean_answer.append(int(item["id"]))
                    elif isinstance(item, (int, str)):
                        clean_answer.append(int(item))
                except (ValueError, TypeError):
                    logger.warning(f"Skipping invalid item: {item}")
            return clean_answer
    elif isinstance(answer, str):
        clean_answer = answer.strip()
        # Remove quotes if present
        if (clean_answer.startswith('"') and clean_answer.endswith('"')) or \
           (clean_answer.startswith("'") and clean_answer.endswith("'")):
            clean_answer = clean_answer[1:-1]
        return clean_answer
    else:
        return answer

def send_report(url: str, api_key: str, task_name: str, answer: Any, attempt: int = 1) -> Optional[Dict]:
    """
    Sends the final report/answer to the specified endpoint.

    Args:
        url: The URL to send the report to
        api_key: API key for authentication
        task_name: Name of the task being performed
        answer: The answer/data to send
        attempt: Current attempt number (for retry logic)

    Returns:
        Optional[Dict]: The response from the server, or None if failed
    """
    logger.info(f"Sending report to {url} (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    # Ensure the answer is properly formatted
    clean_answer = clean_answer_data(answer)
    logger.info(f"Cleaned answer: {clean_answer}")

    payload = {
        "task": task_name,
        "apikey": api_key,
        "answer": clean_answer
    }

    headers = {
        "Content-Type": "application/json; charset=utf-8"
    }

    # Log payload before sending
    logger.info(f"Sending payload: {json.dumps(payload, ensure_ascii=False)}")

    try:
        response = requests.post(
            url,
            json=payload,  # Use json instead of data to let requests handle serialization
            headers=headers,
            timeout=REQUEST_TIMEOUT
        )

        # Log server response
        logger.info(f"Response code: {response.status_code}")
        logger.info(f"Response content: {response.text}")

        response.raise_for_status()

        result = response.json()
        logger.info("Report sent successfully")
        return result

    except requests.exceptions.RequestException as e:
        logger.error(f"Error sending report: {e}")

        if hasattr(e, 'response') and hasattr(e.response, 'text'):
            logger.error(f"Server response: {e.response.text}")

        # Retry logic
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return send_report(url, api_key, task_name, answer, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response: {e}")
        return None

def check_for_flag(response) -> Optional[str]:
    """
    Checks if the response contains a flag in various formats.

    Args:
        response: Response to check (can be string, dict or other type)

    Returns:
        Optional[str]: Found flag or None if not found
    """
    # Convert response to string for easier searching
    response_str = str(response)

    if not response_str:
        return None

    # Search for flag in {{FLG:XXXX}} format
    match_curly = re.search(FLAG_REGEX_CURLY, response_str)
    if match_curly:
        flag = match_curly.group(0)
        logger.info(f"Flag found in {{{{FLG:XXXX}}}} format: {flag}")
        return flag

    # Search for flag in FLG[a-zA-Z0-9_-]+ format
    match = re.search(FLAG_REGEX, response_str)
    if match:
        flag = match.group(0)
        logger.info(f"Flag found in FLG[a-zA-Z0-9_-]+ format: {flag}")
        return flag

    # Check if text contains the word "flag" or "FLG"
    if "flag" in response_str.lower() or "flg" in response_str.lower():
        logger.info(f"Text contains the word 'flag' or 'FLG': {response_str}")
        return response_str

    logger.info("No flag found in response")
    return None

# --- TASK-SPECIFIC FUNCTIONS ---

def execute_sql_query(query: str, api_key: str, attempt: int = 1) -> Optional[Dict]:
    """
    Executes SQL query via the API and returns the results.

    Args:
        query: SQL query to execute
        api_key: API key for authentication
        attempt: Current attempt number (for retry logic)

    Returns:
        Optional[Dict]: Query results or None if failed
    """
    logger.info(f"Executing SQL query (attempt {attempt}/{RETRY_ATTEMPTS + 1})")
    logger.info(f"Query: {query}")

    url = DATA_ENDPOINT
    headers = {
        "Content-Type": "application/json"
    }
    payload = {
        "task": TASK_NAME,
        "apikey": api_key,
        "query": query
    }

    try:
        response = requests.post(
            url,
            json=payload,
            headers=headers,
            timeout=REQUEST_TIMEOUT
        )
        response.raise_for_status()

        data = response.json()
        logger.info("SQL query executed successfully")
        logger.info(f"Response data: {data}")
        
        # Check if response contains "error" field
        if "error" in data and data["error"]:
            logger.error(f"Error in SQL query response: {data['error']}")
        
        return data

    except requests.exceptions.RequestException as e:
        logger.error(f"Error executing SQL query: {e}")

        if hasattr(e, 'response') and hasattr(e.response, 'text'):
            logger.error(f"Server response: {e.response.text}")

        # Retry logic
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return execute_sql_query(query, api_key, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response: {e}")
        return None

def extract_result_data(response: Dict, context: str = "") -> Optional[List]:
    """
    Extracts result data from API response.
    
    Args:
        response: API response dictionary
        context: Context for error messages
        
    Returns:
        List of result data or None if not found
    """
    if not response:
        logger.error(f"Invalid response {context}")
        return None
        
    # Check if response contains "reply" field (according to API documentation)
    if "reply" in response:
        return response["reply"]
    # Maintain backward compatibility with "result" field
    elif "result" in response:
        return response["result"]
    else:
        logger.error(f"No 'reply' or 'result' field found in response {context}")
        return None

def extract_table_names(result_data: List) -> List[str]:
    """
    Extracts table names from result data.
    
    Args:
        result_data: Result data from API
        
    Returns:
        List of table names
    """
    tables = []
    for row in result_data:
        # If response is an array of objects with "Tables_in_banan" field
        if isinstance(row, dict) and "Tables_in_banan" in row:
            tables.append(row["Tables_in_banan"])
            logger.info(f"Found table from 'Tables_in_banan': {row['Tables_in_banan']}")
        # If response is an array of objects with "field" field
        elif isinstance(row, dict) and "field" in row:
            tables.append(row["field"])
            logger.info(f"Found table from 'field': {row['field']}")
        # If response is an array of arrays
        elif isinstance(row, (list, tuple)) and len(row) > 0:
            tables.append(row[0])
            logger.info(f"Found table from list: {row[0]}")
        # If response is a single value
        elif isinstance(row, str):
            tables.append(row)
            logger.info(f"Found table from string: {row}")
        # For other formats, try to extract table name from any dictionary field
        elif isinstance(row, dict):
            for key, value in row.items():
                if isinstance(value, str) and "table" in key.lower():
                    tables.append(value)
                    logger.info(f"Found table from key '{key}': {value}")
                    break
            else:
                logger.warning(f"Unknown table format: {row} (type: {type(row)})")
    
    return tables

def extract_schema_value(result_data: List, table: str) -> Optional[str]:
    """
    Extracts schema value from result data.
    
    Args:
        result_data: Result data from API
        table: Table name for context
        
    Returns:
        Schema value or None if not found
    """
    if not result_data:
        return None
        
    schema_value = None
    
    # If response is an array of objects with "Table" and "Create Table" fields
    if isinstance(result_data[0], dict) and "Table" in result_data[0] and "Create Table" in result_data[0]:
        schema_value = result_data[0]["Create Table"]
        logger.info(f"Found schema from 'Create Table' field: {schema_value[:50]}...")
    
    # If response is an array of objects with "field" field
    elif isinstance(result_data[0], dict) and "field" in result_data[0]:
        # Schema is usually in the "field" field of the second object (or first if there's only one)
        if len(result_data) > 1:
            schema_value = result_data[1]["field"]
        else:
            schema_value = result_data[0]["field"]
        logger.info(f"Found schema from 'field': {schema_value[:50]}...")
        
    # If response is an array of arrays
    elif isinstance(result_data[0], (list, tuple)):
        # Schema is usually in the second column of the first row
        if len(result_data[0]) > 1:
            schema_value = result_data[0][1]
        else:
            schema_value = str(result_data[0])
        logger.info(f"Found schema from list: {schema_value[:50]}...")
        
    # If response is a single value
    elif isinstance(result_data[0], str):
        schema_value = result_data[0]
        logger.info(f"Found schema from string: {schema_value[:50]}...")
        
    # For other formats, try to find schema in any dictionary field
    elif isinstance(result_data[0], dict):
        for key, value in result_data[0].items():
            if isinstance(value, str) and ("create" in key.lower() or "schema" in key.lower() or "table" in key.lower()):
                schema_value = value
                logger.info(f"Found schema from key '{key}': {schema_value[:50]}...")
                break
        else:
            logger.warning(f"Unknown schema format for table {table}: {result_data[0]} (type: {type(result_data[0])})")
    
    return schema_value

def discover_database_structure(api_key: str) -> Optional[Dict]:
    """
    Discovers the database structure by querying table schemas.

    Args:
        api_key: API key for authentication

    Returns:
        Optional[Dict]: Database structure information or None if failed
    """
    logger.info("Discovering database structure")

    # Step 1: Get list of tables
    tables_query = "SHOW TABLES;"
    tables_result = execute_sql_query(tables_query, api_key)
    
    result_data = extract_result_data(tables_result, "for tables list")
    if not result_data:
        return None
    
    # Process table names
    tables = extract_table_names(result_data)
    
    if not tables:
        logger.error("No tables found")
        return None
    
    logger.info(f"Found tables: {tables}")
    
    # Step 2: Get schema for each table
    schemas = {}
    table_schemas = {
        "connections": None,
        "correct_order": None,
        "datacenters": None,
        "users": None
    }
    
    # Get schema for each table
    for table in tables:
        schema_query = f"SHOW CREATE TABLE {table};"
        schema_result = execute_sql_query(schema_query, api_key)
        
        result_data = extract_result_data(schema_result, f"for table {table}")
        if not result_data:
            continue
            
        schema_value = extract_schema_value(result_data, table)
        
        # Save schema to appropriate variable
        if schema_value:
            schemas[table] = schema_value
            
            # Assign schema to appropriate variable
            if table in table_schemas:
                table_schemas[table] = schema_value
    
    return {
        "tables": tables,
        "schemas": schemas,
        **table_schemas
    }

def prepare_schemas_text(db_structure: Dict) -> str:
    """
    Prepares text with table schemas for LLM prompts.
    
    Args:
        db_structure: Database structure information
        
    Returns:
        Formatted text with table schemas
    """
    schemas_text = ""
    
    # Use dedicated variables if available
    for table in ["connections", "correct_order", "datacenters", "users"]:
        key = f"{table}_schema"
        if key in db_structure and db_structure[key]:
            schemas_text += f"Table: {table}\nSchema:\n{db_structure[key]}\n\n"
    
    # If we don't have dedicated variables, use general schemas dictionary
    if not schemas_text and "schemas" in db_structure:
        for table, schema in db_structure["schemas"].items():
            schemas_text += f"Table: {table}\nSchema:\n{schema}\n\n"
    
    return schemas_text

def clean_sql_query(sql_query: str) -> str:
    """
    Cleans up SQL query by removing markdown formatting, comments, etc.
    
    Args:
        sql_query: Raw SQL query from LLM
        
    Returns:
        Cleaned SQL query
    """
    # Remove markdown code blocks if present
    sql_query = sql_query.strip()
    if sql_query.startswith("```sql"):
        sql_query = sql_query[6:]
    if sql_query.startswith("```"):
        sql_query = sql_query[3:]
    if sql_query.endswith("```"):
        sql_query = sql_query[:-3]
    
    # Remove lines that are not part of SQL query
    lines = sql_query.split("\n")
    cleaned_lines = []
    for line in lines:
        line = line.strip()
        # Skip empty lines and lines that look like comments or explanations
        if line and not line.startswith("#") and not line.startswith("--") and not line.startswith("/*"):
            cleaned_lines.append(line)
    
    # Join lines back into one query
    sql_query = " ".join(cleaned_lines).strip()
    
    # Remove all double spaces
    while "  " in sql_query:
        sql_query = sql_query.replace("  ", " ")
        
    # Make sure query ends with semicolon
    if not sql_query.endswith(";"):
        sql_query += ";"
    
    return sql_query

def analyze_database_structure(db_structure: Dict) -> Optional[str]:
    """
    Uses LLM to analyze the database structure and identify key relationships.

    Args:
        db_structure: Database structure information

    Returns:
        Optional[str]: Analysis of database structure or None if failed
    """
    logger.info("Analyzing database structure with LLM")
    
    if not db_structure or "schemas" not in db_structure:
        logger.error("Invalid database structure data")
        return None
    
    schemas_text = prepare_schemas_text(db_structure)
    
    prompt = f"""
    Please analyze the following database schemas:
    
    {schemas_text}
    
    I need to find IDs of active datacenters managed by inactive managers.
    Explain the relationships between tables and identify which tables and columns are needed for this query.
    """
    
    # Call LLM to analyze schemas
    analysis = call_llm(prompt, SYSTEM_PROMPT_ANALYZE_SCHEMA)
    
    if not analysis:
        logger.error("Failed to analyze database structure")
        return None
    
    logger.info("Database structure analysis complete")
    return analysis

def generate_sql_query(db_structure: Dict, analysis: str) -> Optional[str]:
    """
    Uses LLM to generate SQL query based on database structure and analysis.

    Args:
        db_structure: Database structure information
        analysis: Analysis of database structure

    Returns:
        Optional[str]: Generated SQL query or None if failed
    """
    logger.info("Generating SQL query with LLM")
    
    schemas_text = prepare_schemas_text(db_structure)
    
    prompt = f"""
    Based on the following database schemas and analysis, generate a SQL query to find IDs of all active datacenters managed by inactive managers:
    
    DATABASE SCHEMAS:
    {schemas_text}
    
    ANALYSIS:
    {analysis}
    
    TASK:
    Find IDs of active datacenters managed by inactive managers.
    
    REQUIREMENTS:
    1. The query should return only the ID column from the datacenters table
    2. The query should filter for active datacenters (where active = 1)
    3. The query should filter for inactive managers (where active = 0)
    4. The query should use appropriate JOINs to connect the tables
    
    CRITICAL REQUIREMENT:
    RETURN ONLY THE RAW SQL QUERY WITHOUT ANY EXPLANATIONS, COMMENTS, MARKDOWN FORMATTING, OR CODE BLOCKS. JUST THE PLAIN SQL TEXT.
    
    Example of correct response format (return only the query, no formatting):
    SELECT datacenter_id FROM table1 JOIN table2 ON table1.id = table2.id WHERE condition1 AND condition2;
    """
    
    # Call LLM to generate SQL query
    sql_query = call_llm(prompt, SYSTEM_PROMPT_GENERATE_SQL, is_sql_generation=True)
    
    if not sql_query:
        logger.error("Failed to generate SQL query")
        return None
    
    # Clean up the query
    sql_query = clean_sql_query(sql_query)
    
    logger.info(f"Generated SQL query: {sql_query}")
    return sql_query

def extract_fixed_query(response: str) -> str:
    """
    Extracts fixed SQL query from LLM response.
    
    Args:
        response: LLM response with fixed query
        
    Returns:
        Extracted fixed SQL query
    """
    # Look for "POPRAWIONE ZAPYTANIE:" section
    if "POPRAWIONE ZAPYTANIE:" in response:
        lines = response.split("\n")
        start_idx = -1
        end_idx = -1
        
        for i, line in enumerate(lines):
            if "POPRAWIONE ZAPYTANIE:" in line:
                start_idx = i + 1
            elif start_idx != -1 and (line.strip() == "" or "UZASADNIENIE ZMIAN:" in line):
                end_idx = i
                break
        
        if start_idx != -1 and end_idx != -1:
            return "\n".join(lines[start_idx:end_idx]).strip()
    
    # If we can't find the sections, clean up the response as best we can
    return clean_sql_query(response)

def debug_sql_query(query: str, error_message: str, db_structure: Dict) -> Optional[str]:
    """
    Uses LLM to debug and fix SQL query if it fails.

    Args:
        query: Original SQL query
        error_message: Error message from failed query
        db_structure: Database structure information

    Returns:
        Optional[str]: Fixed SQL query or None if failed
    """
    logger.info("Debugging SQL query with LLM")
    
    schemas_text = prepare_schemas_text(db_structure)
    
    prompt = f"""
    The following SQL query failed with this error:
    
    QUERY:
    {query}
    
    ERROR:
    {error_message}
    
    DATABASE SCHEMAS:
    {schemas_text}
    
    TASK:
    Find IDs of active datacenters managed by inactive managers.
    
    Please fix the query and format your response as follows:
    
    POPRAWIONE ZAPYTANIE:
    [Your fixed SQL query here]
    
    UZASADNIENIE ZMIAN:
    [Explanation of what was wrong and how you fixed it]
    """
    
    # Call LLM to debug SQL query
    fixed_query_response = call_llm(prompt, SYSTEM_PROMPT_DEBUG_SQL)
    
    if not fixed_query_response:
        logger.error("Failed to debug SQL query")
        return None
    
    # Extract the fixed query from the response
    fixed_query = extract_fixed_query(fixed_query_response)
    
    logger.info(f"Fixed SQL query: {fixed_query}")
    return fixed_query

def extract_datacenter_id_from_row(row: Any) -> Optional[int]:
    """
    Extracts datacenter ID from a single row of query results.
    
    Args:
        row: A row from query results
        
    Returns:
        Extracted datacenter ID or None if not found
    """
    # If row is a dictionary with "dc_id" field
    if isinstance(row, dict) and "dc_id" in row:
        try:
            return int(row["dc_id"])
        except (ValueError, TypeError):
            logger.warning(f"Invalid datacenter ID: {row['dc_id']}")
    
    # If row is a dictionary with "id" field
    elif isinstance(row, dict) and "id" in row:
        try:
            return int(row["id"])
        except (ValueError, TypeError):
            logger.warning(f"Invalid datacenter ID: {row['id']}")
    
    # If row is a dictionary with "field" field
    elif isinstance(row, dict) and "field" in row:
        try:
            return int(row["field"])
        except (ValueError, TypeError):
            logger.warning(f"Invalid datacenter ID: {row['field']}")
    
    # If row is a list or tuple
    elif isinstance(row, (list, tuple)) and len(row) > 0:
        try:
            return int(row[0])
        except (ValueError, TypeError):
            logger.warning(f"Invalid datacenter ID: {row[0]}")
    
    # If row is a scalar value
    elif isinstance(row, (int, str)):
        try:
            return int(row)
        except (ValueError, TypeError):
            logger.warning(f"Invalid datacenter ID: {row}")
    
    # For other dictionary formats, try to find ID in any field
    elif isinstance(row, dict):
        # First try common ID field names
        for key in ["id", "datacenter_id", "datacenterid"]:
            if key.lower() in row:
                try:
                    return int(row[key.lower()])
                except (ValueError, TypeError):
                    logger.warning(f"Invalid datacenter ID from key '{key}': {row[key.lower()]}")
        
        # If no common ID field found, try any numeric value
        for key, value in row.items():
            if isinstance(value, (int, str)):
                try:
                    return int(value)
                except (ValueError, TypeError):
                    continue
    
    return None

def process_query_results(results: Dict) -> List[int]:
    """
    Processes query results to extract datacenter IDs.

    Args:
        results: Query results from API

    Returns:
        List[int]: List of datacenter IDs
    """
    logger.info("Processing query results")
    
    if not results:
        logger.error("Invalid query results")
        return []
    
    # Extract result data
    result_data = extract_result_data(results, "for query results")
    if not result_data:
        # Check if we have an "error" field in the response
        if "error" in results:
            logger.error(f"Error in response: {results['error']}")
        return []
    
    # Check if result_data is None
    if result_data is None:
        logger.error("Result data is None")
        return []
    
    # Check if result_data is iterable
    if not hasattr(result_data, '__iter__'):
        logger.error(f"Result data is not iterable: {type(result_data)}")
        # If result_data is not iterable but is an integer, add it to the list
        if isinstance(result_data, int):
            return [result_data]
        # If result_data is a string, try to convert to integer
        elif isinstance(result_data, str):
            try:
                return [int(result_data)]
            except ValueError:
                pass
        return []
    
    # Extract IDs from results
    datacenter_ids = []
    for row in result_data:
        datacenter_id = extract_datacenter_id_from_row(row)
        if datacenter_id is not None:
            datacenter_ids.append(datacenter_id)
            logger.info(f"Added datacenter ID: {datacenter_id}")
    
    # Remove duplicates and sort
    datacenter_ids = sorted(list(set(datacenter_ids)))
    
    logger.info(f"Extracted {len(datacenter_ids)} unique datacenter IDs")
    return datacenter_ids

def execute_query_with_retry(sql_query: str, api_key: str, db_structure: Dict, max_attempts: int = 3) -> Optional[Dict]:
    """
    Executes SQL query with retry logic and error handling.
    
    Args:
        sql_query: SQL query to execute
        api_key: API key for authentication
        db_structure: Database structure information
        max_attempts: Maximum number of retry attempts
        
    Returns:
        Query results or None if failed
    """
    query_results = None
    
    for attempt in range(1, max_attempts + 1):
        query_results = execute_sql_query(sql_query, api_key)
        
        # Check if query was successful
        if query_results:
            # Check if we have data in the response
            if "reply" in query_results or "result" in query_results:
                logger.info("Query successful with data")
                break
            # If there's no error, consider it a success (might be an empty response)
            elif "error" not in query_results or not query_results["error"]:
                logger.info("Query successful without explicit data fields")
                break
        
        # If query failed, get error message and try to fix it
        if query_results and "error" in query_results:
            error_message = query_results["error"]
            logger.error(f"SQL query failed: {error_message}")
            
            # Debug and fix query with LLM
            fixed_query = debug_sql_query(sql_query, error_message, db_structure)
            if fixed_query:
                sql_query = fixed_query
            else:
                logger.error("Failed to fix SQL query. Trying again with original.")
        
        if attempt < max_attempts:
            logger.info(f"Retrying SQL query (attempt {attempt + 1}/{max_attempts})...")
        else:
            logger.error("Maximum SQL query attempts reached.")
            return None
    
    return query_results

# --- MAIN FUNCTION ---

def main():
    """
    Main function that orchestrates the agent's workflow.
    """
    logger.info(f"--- Starting {TASK_NAME} agent ---")

    # Validate configuration
    if not validate_configuration():
        logger.critical("Invalid configuration. Exiting.")
        return

    try:
        # Step 1: Discover database structure
        db_structure = discover_database_structure(API_KEY)
        if not db_structure:
            logger.error("Failed to discover database structure. Exiting.")
            return

        # Step 2: Analyze database structure with LLM
        analysis = analyze_database_structure(db_structure)
        if not analysis:
            logger.error("Failed to analyze database structure. Exiting.")
            return

        # Step 3: Generate SQL query with LLM
        sql_query = generate_sql_query(db_structure, analysis)
        if not sql_query:
            logger.error("Failed to generate SQL query. Exiting.")
            return

        # Step 4: Execute SQL query with retry logic
        query_results = execute_query_with_retry(sql_query, API_KEY, db_structure)
        if not query_results:
            logger.error("Failed to execute SQL query. Exiting.")
            return
        
        # Additional check for errors in the response
        if "error" in query_results and query_results["error"] and query_results["error"] != "OK":
            logger.error(f"Error in query results: {query_results['error']}")
            # Try to fix the query and run it again
            fixed_query = debug_sql_query(sql_query, query_results["error"], db_structure)
            if fixed_query:
                logger.info(f"Trying with fixed query: {fixed_query}")
                query_results = execute_sql_query(fixed_query, API_KEY)
                if not query_results or ("error" in query_results and query_results["error"] and query_results["error"] != "OK"):
                    logger.error("Fixed query also failed. Exiting.")
                    return
            else:
                logger.error("Failed to fix SQL query. Exiting.")
                return
        
        # Step 5: Process query results
        datacenter_ids = process_query_results(query_results)
        
        if not datacenter_ids:
            logger.error("No datacenter IDs found in query results. Exiting.")
            return

        # Step 6: Prepare final answer
        # For this task, the answer is simply the list of datacenter IDs
        answer = datacenter_ids
        logger.info(f"Final answer: {answer}")

        # Step 7: Send report
        report_response = send_report(REPORT_ENDPOINT, API_KEY, TASK_NAME, answer)
        if not report_response:
            logger.error("Failed to send report. Exiting.")
            return

        # Step 8: Check for flag in response
        flag = check_for_flag(report_response)

        # Step 9: Display results
        if flag:
            logger.info(f"SUCCESS! Flag: {flag}")
        else:
            logger.info("Task completed, but no flag found in response")
            logger.info(f"Response: {report_response}")

    except Exception as e:
        logger.critical(f"Unexpected error: {e}", exc_info=True)

    logger.info(f"--- {TASK_NAME} agent finished ---")

# --- SCRIPT ENTRY POINT ---

if __name__ == "__main__":
    main()