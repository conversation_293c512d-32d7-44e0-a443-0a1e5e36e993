# Plan implementacji SFA dla zadania S03E03 - Zapytania do bazy danych

## 1. Cel i zakres zadania

Celem zadania jest stworzenie Single File Agent (SFA), który:

1. Komunikuje się z API bazy danych pod adresem `https://c3ntrala.ag3nts.org/apidb`
2. Odkrywa strukturę bazy danych (tabele i ich schematy)
3. Wykorzystuje LLM do wygenerowania zapytania SQL
4. Wykonuje zapytanie i przetwarza wyniki
5. Wysyła odpowiedź do centrali w określonym formacie

Zadanie polega na znalezieniu ID aktywnych datacenter z nieaktywnymi menadżerami.

## 2. Struktura pliku

Plik `S03E03_AUGMENT_sfa.py` będzie zawierał następującą strukturę:

```
1. Importy i konfiguracja
2. Stałe i zmienne globalne
3. Funkcje pomocnicze
   3.1. Walidacja konfiguracji
   3.2. Obsługa zapytań do API bazy danych
   3.3. Obsługa zapytań do LLM
   3.4. Obsługa cache
   3.5. Wysyłanie raportu
   3.6. Sprawdzanie flagi
4. Główne funkcje workflow
   4.1. Odkrywanie struktury bazy danych
   4.2. Generowanie zapytania SQL
   4.3. Wykonanie zapytania i przetwarzanie wyników
5. Funkcja main
6. Entry point
```

## 3. Szczegółowy opis implementacji

### 3.1. Importy i konfiguracja

```python
import os
import re
import json
import logging
import time
import requests
from typing import Dict, List, Any, Tuple, Optional, Union
from dotenv import load_dotenv
from openai import OpenAI, APIError, APIConnectionError, RateLimitError
```

### 3.2. Stałe i zmienne globalne

```python
# Ładowanie zmiennych środowiskowych
load_dotenv()

# Ustawienia aplikacji
TASK_NAME = "database"
API_BASE_URL = os.getenv("API_BASE_URL", "https://c3ntrala.ag3nts.org")
API_DB_ENDPOINT = f"{API_BASE_URL}/apidb"
REPORT_ENDPOINT = f"{API_BASE_URL}/report"
API_KEY = os.getenv("AIDEVS_API_KEY")

# Ustawienia zapytań
REQUEST_TIMEOUT = 10  # sekund
RETRY_ATTEMPTS = 2
RETRY_DELAY = 2  # sekund

# Ustawienia LLM
LLM_MODEL = os.getenv("KLUSTER_LLM_MODEL", os.getenv("OPENAI_LLM_MODEL"))
LLM_API_KEY = os.getenv("KLUSTER_API_KEY", os.getenv("OPENAI_API_KEY"))
LLM_BASE_URL = os.getenv("KLUSTER_BASE_URL", os.getenv("OPENAI_BASE_URL"))
LLM_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", "4000"))
LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.3"))
LLM_TOP_P = float(os.getenv("LLM_TOP_P", "1.0"))
LLM_MAX_RETRIES = 3

# Ścieżki do system promptów
SYSTEM_PROMPT_ANALYZE_SCHEMA = os.path.join(
    os.path.dirname(os.path.abspath(__file__)), 
    "system_prompt_analiza_schematow.md"
)
SYSTEM_PROMPT_GENERATE_SQL = os.path.join(
    os.path.dirname(os.path.abspath(__file__)), 
    "system_prompt_generowanie_sql.md"
)
SYSTEM_PROMPT_DEBUG_SQL = os.path.join(
    os.path.dirname(os.path.abspath(__file__)), 
    "system_prompt_debugowanie_sql.md"
)

# Ścieżki do plików cache
CACHE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cache")
TABLES_CACHE = os.path.join(CACHE_DIR, "tables.json")
SCHEMAS_CACHE = os.path.join(CACHE_DIR, "schemas.json")
ANALYSIS_CACHE = os.path.join(CACHE_DIR, "analysis.json")
SQL_QUERY_CACHE = os.path.join(CACHE_DIR, "sql_query.txt")
RESULTS_CACHE = os.path.join(CACHE_DIR, "results.json")

# Regex dla flagi
FLAG_REGEX = r"FLG[a-zA-Z0-9_-]+"
FLAG_REGEX_CURLY = r"\{\{FLG:[a-zA-Z0-9_-]+\}\}"

# Konfiguracja logowania
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)
```

### 3.3. Funkcje pomocnicze

#### 3.3.1. Walidacja konfiguracji

```python
def validate_configuration() -> bool:
    """
    Waliduje, czy wszystkie wymagane zmienne konfiguracyjne są ustawione.
    
    Returns:
        bool: True jeśli konfiguracja jest poprawna, False w przeciwnym razie
    """
    required_env_vars = {
        "AIDEVS_API_KEY": "Klucz API dla Centrali",
        "KLUSTER_API_KEY": "Klucz API dla Kluster.ai",
    }
    missing_vars = []

    for var, description in required_env_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"{var} ({description})")

    if missing_vars:
        logger.critical(f"Brakujące wymagane zmienne środowiskowe: {', '.join(missing_vars)}")
        return False

    # Sprawdź, czy API_KEY jest ustawiony
    if not API_KEY:
        logger.critical("API_KEY nie jest ustawiony, sprawdź zmienną środowiskową AIDEVS_API_KEY")
        return False

    # Sprawdź, czy LLM_API_KEY jest ustawiony
    if not LLM_API_KEY:
        logger.warning("LLM_API_KEY nie jest ustawiony, używam KLUSTER_API_KEY")

    return True
```

#### 3.3.2. Obsługa zapytań do API bazy danych

```python
def execute_db_query(query: str, attempt: int = 1) -> Optional[Dict]:
    """
    Wykonuje zapytanie SQL do API bazy danych z logiką ponownych prób.
    
    Args:
        query: Zapytanie SQL do wykonania
        attempt: Numer bieżącej próby (dla logiki ponownych prób)
        
    Returns:
        Optional[Dict]: Wynik zapytania jako słownik lub None w przypadku niepowodzenia
    """
    logger.info(f"Wykonuję zapytanie do bazy danych: {query} (próba {attempt}/{RETRY_ATTEMPTS + 1})")
    
    payload = {
        "task": TASK_NAME,
        "apikey": API_KEY,
        "query": query
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(
            API_DB_ENDPOINT,
            json=payload,
            headers=headers,
            timeout=REQUEST_TIMEOUT
        )
        response.raise_for_status()
        
        data = response.json()
        logger.info("Zapytanie wykonane pomyślnie")
        
        # Sprawdź, czy odpowiedź zawiera błąd
        if "error" in data and data["error"]:
            logger.error(f"Błąd w zapytaniu SQL: {data['error']}")
            return None
            
        return data
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Błąd podczas wykonywania zapytania: {e}")
        
        # Logika ponownych prób
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Ponawiam za {RETRY_DELAY} sekund...")
            time.sleep(RETRY_DELAY)
            return execute_db_query(query, attempt + 1)
        else:
            logger.error("Osiągnięto maksymalną liczbę prób")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Błąd dekodowania odpowiedzi JSON: {e}")
        return None
```

#### 3.3.3. Obsługa zapytań do LLM

```python
def load_system_prompt(prompt_path: str) -> str:
    """
    Wczytuje system prompt z pliku.
    
    Args:
        prompt_path: Ścieżka do pliku z system promptem
        
    Returns:
        str: Zawartość pliku z system promptem
    """
    try:
        with open(prompt_path, 'r', encoding='utf-8') as file:
            system_prompt = file.read()
        logger.info(f"Wczytano system prompt z pliku: {prompt_path}")
        return system_prompt
    except Exception as e:
        logger.error(f"Błąd podczas wczytywania system promptu: {e}")
        logger.warning("Używanie pustego system promptu")
        return "Jesteś pomocnym asystentem AI."

def call_llm(prompt: str, system_prompt_path: str, attempt: int = 1) -> Optional[str]:
    """
    Wywołuje LLM z podanym promptem i obsługuje błędy z logiką ponownych prób.
    
    Args:
        prompt: Prompt użytkownika do wysłania do LLM
        system_prompt_path: Ścieżka do pliku z system promptem
        attempt: Numer bieżącej próby (dla logiki ponownych prób)
        
    Returns:
        Optional[str]: Odpowiedź LLM lub None w przypadku niepowodzenia
    """
    logger.info(f"Wywołuję LLM z promptem (próba {attempt}/{LLM_MAX_RETRIES})")
    
    try:
        # Wczytaj system prompt z pliku
        system_prompt = load_system_prompt(system_prompt_path)
        
        client_params = {"api_key": LLM_API_KEY}
        if LLM_BASE_URL:
            client_params["base_url"] = LLM_BASE_URL
            
        client = OpenAI(**client_params)
        
        logger.info(f"Używam modelu: {LLM_MODEL} przez API: {LLM_BASE_URL}")
        
        completion = client.chat.completions.create(
            model=LLM_MODEL,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            temperature=LLM_TEMPERATURE,
            max_tokens=LLM_MAX_TOKENS,
            top_p=LLM_TOP_P
        )
        
        response = completion.choices[0].message.content.strip()
        logger.info("Wywołanie LLM zakończone sukcesem")
        return response
        
    except (APIError, APIConnectionError, RateLimitError) as e:
        logger.error(f"Błąd API LLM: {e}")
        
        # Logika ponownych prób
        if attempt < LLM_MAX_RETRIES:
            logger.info(f"Ponawiam za {RETRY_DELAY} sekund...")
            time.sleep(RETRY_DELAY)
            return call_llm(prompt, system_prompt_path, attempt + 1)
        else:
            logger.error("Osiągnięto maksymalną liczbę prób")
            return None
    except Exception as e:
        logger.error(f"Nieoczekiwany błąd podczas wywołania LLM: {e}")
        return None
```

#### 3.3.4. Funkcje do obsługi cache

```python
def save_to_cache(data: Any, cache_path: str) -> bool:
    """
    Zapisuje dane do pliku cache.
    
    Args:
        data: Dane do zapisania
        cache_path: Ścieżka do pliku cache
        
    Returns:
        bool: True jeśli zapis się powiódł, False w przeciwnym razie
    """
    try:
        # Upewnij się, że katalog cache istnieje
        os.makedirs(os.path.dirname(cache_path), exist_ok=True)
        
        # Zapisz dane w zależności od typu
        if isinstance(data, str):
            with open(cache_path, 'w', encoding='utf-8') as file:
                file.write(data)
        else:
            with open(cache_path, 'w', encoding='utf-8') as file:
                json.dump(data, file, ensure_ascii=False, indent=2)
                
        logger.info(f"Dane zapisane do cache: {cache_path}")
        return True
    except Exception as e:
        logger.error(f"Błąd podczas zapisywania do cache: {e}")
        return False

def load_from_cache(cache_path: str) -> Optional[Any]:
    """
    Wczytuje dane z pliku cache.
    
    Args:
        cache_path: Ścieżka do pliku cache
        
    Returns:
        Optional[Any]: Wczytane dane lub None w przypadku niepowodzenia
    """
    try:
        if not os.path.exists(cache_path):
            logger.info(f"Plik cache nie istnieje: {cache_path}")
            return None
            
        # Wczytaj dane w zależności od rozszerzenia pliku
        if cache_path.endswith('.json'):
            with open(cache_path, 'r', encoding='utf-8') as file:
                return json.load(file)
        else:
            with open(cache_path, 'r', encoding='utf-8') as file:
                return file.read()
                
        logger.info(f"Dane wczytane z cache: {cache_path}")
    except Exception as e:
        logger.error(f"Błąd podczas wczytywania z cache: {e}")
        return None
```

#### 3.3.5. Wysyłanie raportu

```python
def send_report(datacenter_ids: List[int], attempt: int = 1) -> Optional[Dict]:
    """
    Wysyła raport z listą ID datacenter do API centrali.
    
    Args:
        datacenter_ids: Lista ID datacenter
        attempt: Numer bieżącej próby (dla logiki ponownych prób)
        
    Returns:
        Optional[Dict]: Odpowiedź z serwera lub None w przypadku niepowodzenia
    """
    logger.info(f"Wysyłam raport do {REPORT_ENDPOINT} (próba {attempt}/{RETRY_ATTEMPTS + 1})")
    
    payload = {
        "task": TASK_NAME,
        "apikey": API_KEY,
        "answer": datacenter_ids
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    # Loguj pełny payload przed wysłaniem
    logger.info(f"Wysyłam payload: {json.dumps(payload, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            REPORT_ENDPOINT,
            json=payload,
            headers=headers,
            timeout=REQUEST_TIMEOUT
        )
        
        # Loguj pełną odpowiedź serwera
        logger.info(f"Kod odpowiedzi: {response.status_code}")
        logger.info(f"Nagłówki odpowiedzi: {dict(response.headers)}")
        logger.info(f"Treść odpowiedzi: {response.text}")
        
        # Wyświetl pełną odpowiedź w konsoli
        print(f"\n{'='*50}")
        print(f"ODPOWIEDŹ Z SERWERA:")
        print(f"Kod: {response.status_code}")
        print(f"Treść: {response.text}")
        print(f"{'='*50}\n")
        
        response.raise_for_status()
        
        result = response.json()
        logger.info("Raport wysłany pomyślnie")
        return result
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Błąd podczas wysyłania raportu: {e}")
        
        if hasattr(e, 'response') and hasattr(e.response, 'text'):
            logger.error(f"Odpowiedź serwera: {e.response.text}")
            
        # Logika ponownych prób
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Ponawiam za {RETRY_DELAY} sekund...")
            time.sleep(RETRY_DELAY)
            return send_report(datacenter_ids, attempt + 1)
        else:
            logger.error("Osiągnięto maksymalną liczbę prób")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Błąd dekodowania odpowiedzi JSON: {e}")
        return None
```

#### 3.3.6. Sprawdzanie flagi

```python
def check_for_flag(response) -> Optional[str]:
    """
    Sprawdza, czy odpowiedź zawiera flagę w formatach:
    - FLG[a-zA-Z0-9_-]+
    - {{FLG:[a-zA-Z0-9_-]+}}
    
    Args:
        response: Odpowiedź do sprawdzenia (może być string, dict lub inny typ)
        
    Returns:
        Optional[str]: Znaleziona flaga lub None, jeśli nie znaleziono
    """
    # Konwertuj odpowiedź na string dla łatwiejszego wyszukiwania
    response_str = str(response)
    
    if not response_str:
        return None
        
    # Szukaj flagi w formacie {{FLG:XXXX}}
    match_curly = re.search(FLAG_REGEX_CURLY, response_str)
    if match_curly:
        flag = match_curly.group(0)
        logger.info(f"Znaleziono flagę w formacie {{{{FLG:XXXX}}}}: {flag}")
        print(f"\n{'='*50}\nFLAGA: {flag}\n{'='*50}\n")
        return flag
        
    # Szukaj flagi w formacie FLG[a-zA-Z0-9_-]+
    match = re.search(FLAG_REGEX, response_str)
    if match:
        flag = match.group(0)
        logger.info(f"Znaleziono flagę w formacie FLG[a-zA-Z0-9_-]+: {flag}")
        print(f"\n{'='*50}\nFLAGA: {flag}\n{'='*50}\n")
        return flag
        
    # Sprawdź, czy tekst zawiera słowo "flag" lub "FLG"
    if "flag" in response_str.lower() or "flg" in response_str.lower():
        logger.info(f"Tekst zawiera słowo 'flag' lub 'FLG': {response_str}")
        print(f"\n{'='*50}\nPOTENCJALNA FLAGA: {response_str}\n{'='*50}\n")
        return response_str
        
    logger.info("Nie znaleziono flagi w odpowiedzi")
