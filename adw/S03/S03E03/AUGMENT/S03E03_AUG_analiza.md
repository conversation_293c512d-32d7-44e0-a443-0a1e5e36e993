# S03E03 - <PERSON><PERSON>za <PERSON>: Zapytania do bazy danych

## 1. Cel i zakres zadania

Celem zadania jest stworzenie agenta, kt<PERSON>ry komunikuje się z API bazy danych, odk<PERSON><PERSON> jej struktur<PERSON>, generuje zapytanie SQL przy pomocy LLM i przetwarza wyniki, aby znaleźć ID aktywnych datacenter z nieaktywnymi menadżerami.

### Kluczowe wymagania

1. Komunikacja z API bazy danych pod adresem `https://c3ntrala.ag3nts.org/apidb`
2. Odkrycie struktury bazy danych (tabele i ich schematy)
3. Wykorzystanie LLM do wygenerowania zapytania SQL
4. Wykonanie zapytania i przetworzenie wyników
5. Wysłanie odpowiedzi do centrali w określonym formacie

## 2. Analiza techniczna

### 2.1. Endpoint API bazy danych

- URL: `https://c3ntrala.ag3nts.org/apidb`
- Metoda: POST
- Format zapytania:

  ```json
  {
    "task": "database",
    "apikey": "YOUR_API_KEY",
    "query": "ZAPYTANIE_SQL"
  }
  ```

### 2.2. Dostępne operacje SQL

- `SHOW TABLES` - zwraca listę dostępnych tabel
- `SHOW CREATE TABLE NAZWA_TABELI` - pokazuje strukturę konkretnej tabeli
- Standardowe zapytania SELECT

### 2.3. Wymagany format odpowiedzi do centrali

```json
{
  "task": "database",
  "apikey": "YOUR_API_KEY",
  "answer": [1234, 5431, 2344]
}
```

## 3. Optymalny plan działania

### 3.1. Odkrycie struktury bazy danych

1. Wykonanie zapytania `SHOW TABLES;` aby poznać dostępne tabele
2. Wykonanie zapytania `SHOW CREATE TABLE` dla każdej istotnej tabeli (prawdopodobnie `users` i `datacenters`)
3. Analiza schematów w celu zrozumienia relacji między tabelami

### 3.2. Generowanie zapytania SQL

1. Przygotowanie kontekstu dla LLM zawierającego:
   - Schematy tabel
   - Opis zadania (znalezienie ID aktywnych datacenter z nieaktywnymi menadżerami)
   - Instrukcje dotyczące formatu odpowiedzi (tylko surowe zapytanie SQL)
2. Wysłanie zapytania do LLM
3. Wyodrębnienie surowego zapytania SQL z odpowiedzi LLM

### 3.3. Wykonanie zapytania i przetworzenie wyników

1. Wysłanie wygenerowanego zapytania SQL do API bazy danych
2. Parsowanie odpowiedzi JSON
3. Wyodrębnienie listy ID datacenter z odpowiedzi

### 3.4. Wysłanie odpowiedzi

1. Przygotowanie JSON-a z odpowiedzią w wymaganym formacie
2. Wysłanie odpowiedzi do centrali
3. Weryfikacja odpowiedzi (sprawdzenie, czy zawiera flagę)

## 4. Proponowany stack technologiczny

### 4.1. Język programowania

Python - idealny do szybkiego prototypowania, z doskonałym wsparciem dla HTTP i JSON.

### 4.2. Biblioteki

- `requests` - do komunikacji HTTP z API
- `json` - do przetwarzania danych JSON
- `openai` lub `kluster-client` - do komunikacji z LLM
- `python-dotenv` - do zarządzania zmiennymi środowiskowymi
- `logging` - do logowania działań agenta

### 4.3. Struktura kodu

- Single File Agent (SFA) - zgodnie z wcześniejszymi zadaniami
- Modułowe funkcje do każdego etapu procesu:
  - `discover_database_structure()` - odkrycie struktury bazy danych
  - `generate_sql_query()` - generowanie zapytania SQL przy pomocy LLM
  - `execute_query()` - wykonanie zapytania i przetworzenie wyników
  - `send_answer()` - wysłanie odpowiedzi do centrali

### 4.4. Zmienne środowiskowe

- `AIDEVS_API_KEY` - klucz do API centrali
- `LLM_API_KEY` - klucz do API LLM
- `LLM_BASE_URL` - URL bazowy API LLM (opcjonalnie)
- `LLM_MODEL` - nazwa modelu LLM (opcjonalnie)

## 5. Potencjalne wyzwania i rozwiązania

### 5.1. Interpretacja schematów bazy danych

- **Wyzwanie**: LLM musi poprawnie zrozumieć strukturę bazy danych i relacje między tabelami.
- **Rozwiązanie**: Dostarczenie LLM-owi pełnych schematów tabel oraz wskazówek dotyczących potencjalnych relacji.

### 5.2. Formatowanie odpowiedzi LLM

- **Wyzwanie**: LLM może zwrócić dodatkowe informacje oprócz surowego zapytania SQL.
- **Rozwiązanie**: Wyraźne instrukcje dla LLM oraz kod do wyodrębnienia zapytania SQL z odpowiedzi.

### 5.3. Obsługa błędów API

- **Wyzwanie**: Zapytania do API mogą się nie powieść z różnych powodów.
- **Rozwiązanie**: Implementacja mechanizmu ponownych prób oraz szczegółowe logowanie.

### 5.4. Poprawność zapytania SQL

- **Wyzwanie**: Wygenerowane zapytanie SQL może zawierać błędy składniowe lub logiczne.
- **Rozwiązanie**: Iteracyjne podejście - jeśli zapytanie zwraca błąd, popraw je i spróbuj ponownie.

## 6. Struktura pliku SFA

```python
"""
S03E03 Single File Agent - Database Query
Generuje zapytanie SQL przy pomocy LLM i wykonuje je na API bazy danych
"""

# --- IMPORTS ---
# --- CONFIGURATION ---
# --- HELPER FUNCTIONS ---
# --- MAIN FUNCTION ---
# --- ENTRY POINT ---
```

## 7. Podsumowanie

Zadanie wymaga stworzenia agenta, który łączy umiejętności komunikacji z API, przetwarzania danych oraz wykorzystania LLM do generowania zapytań SQL. Kluczowe jest zrozumienie struktury bazy danych oraz precyzyjne instrukcje dla LLM, aby wygenerował poprawne zapytanie SQL.

Podejście iteracyjne, z dokładnym logowaniem każdego kroku, pomoże w debugowaniu i rozwiązywaniu potencjalnych problemów. Ostatecznym celem jest znalezienie ID aktywnych datacenter z nieaktywnymi menadżerami i wysłanie tej listy do centrali w określonym formacie.
