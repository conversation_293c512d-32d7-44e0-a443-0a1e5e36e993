"""
S03E03 Single File Agent - Database Query
Generuje zapytanie SQL przy pomocy LLM i wykonuje je na API bazy danych
"""

# --- IMPORTS ---
import os
import re
import json
import logging
import time
import requests
from typing import Dict, List, Any, Tuple, Optional, Union
from dotenv import load_dotenv
from openai import OpenAI, APIError, APIConnectionError, RateLimitError

# --- CONFIGURATION ---
# Load environment variables
load_dotenv()

# --- APPLICATION SETTINGS ---
TASK_NAME = "database"
API_BASE_URL = os.getenv("API_BASE_URL", "https://c3ntrala.ag3nts.org")
API_DB_ENDPOINT = f"{API_BASE_URL}/apidb"
REPORT_ENDPOINT = f"{API_BASE_URL}/report"
API_KEY = os.getenv("AIDEVS_API_KEY")

# Request settings
REQUEST_TIMEOUT = 10  # seconds
RETRY_ATTEMPTS = 2
RETRY_DELAY = 2  # seconds

# --- LLM SETTINGS ---
LLM_MODEL = os.getenv("KLUSTER_LLM_MODEL", os.getenv("OPENAI_LLM_MODEL"))
LLM_API_KEY = os.getenv("KLUSTER_API_KEY", os.getenv("OPENAI_API_KEY"))
LLM_BASE_URL = os.getenv("KLUSTER_BASE_URL", os.getenv("OPENAI_BASE_URL"))
LLM_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", "4000"))
LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.3"))
LLM_TOP_P = float(os.getenv("LLM_TOP_P", "1.0"))
LLM_MAX_RETRIES = 3

# System prompt paths
SYSTEM_PROMPT_ANALYZE_SCHEMA = os.path.join(
    os.path.dirname(os.path.abspath(__file__)),
    "S03E03_AUG_system_prompt_analiza_schematow.md"
)
SYSTEM_PROMPT_GENERATE_SQL = os.path.join(
    os.path.dirname(os.path.abspath(__file__)),
    "S03E03_AUG_system_prompt_generowanie_sql.md"
)
SYSTEM_PROMPT_DEBUG_SQL = os.path.join(
    os.path.dirname(os.path.abspath(__file__)),
    "S03E03_AUG_system_prompt_debugowanie_sql.md"
)

# Cache paths
CACHE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cache")
TABLES_CACHE = os.path.join(CACHE_DIR, "tables.json")
SCHEMAS_CACHE = os.path.join(CACHE_DIR, "schemas.json")
ANALYSIS_CACHE = os.path.join(CACHE_DIR, "analysis.json")
SQL_QUERY_CACHE = os.path.join(CACHE_DIR, "sql_query.txt")
RESULTS_CACHE = os.path.join(CACHE_DIR, "results.json")

# Regex for flag detection
FLAG_REGEX = r"FLG[a-zA-Z0-9_-]+"
FLAG_REGEX_CURLY = r"\{\{FLG:[a-zA-Z0-9_-]+\}\}"

# --- LOGGING CONFIGURATION ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# --- HELPER FUNCTIONS ---

def validate_configuration() -> bool:
    """
    Validates that all required configuration variables are set.

    Returns:
        bool: True if configuration is valid, False otherwise
    """
    required_env_vars = {
        "AIDEVS_API_KEY": "API key for Central API",
        "KLUSTER_API_KEY": "API key for Kluster.ai",
    }
    missing_vars = []

    for var, description in required_env_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"{var} ({description})")

    if missing_vars:
        logger.critical(f"Missing required environment variables: {', '.join(missing_vars)}")
        return False

    # Check if API_KEY is set
    if not API_KEY:
        logger.critical("API_KEY is not set, check AIDEVS_API_KEY environment variable")
        return False

    # Check if LLM_API_KEY is set
    if not LLM_API_KEY:
        logger.warning("LLM_API_KEY is not set, using KLUSTER_API_KEY")

    return True

def execute_db_query(query: str, attempt: int = 1) -> Optional[Dict]:
    """
    Executes SQL query to database API with retry logic.

    Args:
        query: SQL query to execute
        attempt: Current attempt number (for retry logic)

    Returns:
        Optional[Dict]: Query result as dictionary or None if failed
    """
    logger.info(f"Executing database query: {query} (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    payload = {
        "task": TASK_NAME,
        "apikey": API_KEY,
        "query": query
    }

    headers = {
        "Content-Type": "application/json"
    }

    try:
        response = requests.post(
            API_DB_ENDPOINT,
            json=payload,
            headers=headers,
            timeout=REQUEST_TIMEOUT
        )
        response.raise_for_status()

        data = response.json()
        logger.info("Query executed successfully")

        # Check if response contains error
        if "error" in data and data["error"]:
            logger.error(f"SQL query error: {data['error']}")
            return None

        return data

    except requests.exceptions.RequestException as e:
        logger.error(f"Error executing query: {e}")

        # Retry logic
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return execute_db_query(query, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response: {e}")
        return None

def load_system_prompt(prompt_path: str) -> str:
    """
    Loads system prompt from file.

    Args:
        prompt_path: Path to system prompt file

    Returns:
        str: System prompt content
    """
    try:
        with open(prompt_path, 'r', encoding='utf-8') as file:
            system_prompt = file.read()
        logger.info(f"Loaded system prompt from file: {prompt_path}")
        return system_prompt
    except Exception as e:
        logger.error(f"Error loading system prompt: {e}")
        logger.warning("Using default system prompt")
        return "You are a helpful AI assistant."

def call_llm(prompt: str, system_prompt_path: str, attempt: int = 1) -> Optional[str]:
    """
    Calls LLM with given prompt and handles errors with retry logic.

    Args:
        prompt: User prompt to send to LLM
        system_prompt_path: Path to system prompt file
        attempt: Current attempt number (for retry logic)

    Returns:
        Optional[str]: LLM response or None if failed
    """
    logger.info(f"Calling LLM with prompt (attempt {attempt}/{LLM_MAX_RETRIES})")

    try:
        # Load system prompt from file
        system_prompt = load_system_prompt(system_prompt_path)

        client_params = {"api_key": LLM_API_KEY}
        if LLM_BASE_URL:
            client_params["base_url"] = LLM_BASE_URL

        client = OpenAI(**client_params)

        logger.info(f"Using model: {LLM_MODEL} via API: {LLM_BASE_URL}")

        completion = client.chat.completions.create(
            model=LLM_MODEL,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            temperature=LLM_TEMPERATURE,
            max_completion_tokens=LLM_MAX_TOKENS,
            top_p=LLM_TOP_P
        )

        response = completion.choices[0].message.content.strip()
        logger.info("LLM call successful")
        return response

    except (APIError, APIConnectionError, RateLimitError) as e:
        logger.error(f"LLM API error: {e}")

        # Retry logic
        if attempt < LLM_MAX_RETRIES:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return call_llm(prompt, system_prompt_path, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except Exception as e:
        logger.error(f"Unexpected error during LLM call: {e}")
        return None

def save_to_cache(data: Any, cache_path: str) -> bool:
    """
    Saves data to cache file.

    Args:
        data: Data to save
        cache_path: Path to cache file

    Returns:
        bool: True if save successful, False otherwise
    """
    try:
        # Ensure cache directory exists
        os.makedirs(os.path.dirname(cache_path), exist_ok=True)

        # Save data depending on type
        if isinstance(data, str):
            with open(cache_path, 'w', encoding='utf-8') as file:
                file.write(data)
        else:
            with open(cache_path, 'w', encoding='utf-8') as file:
                json.dump(data, file, ensure_ascii=False, indent=2)

        logger.info(f"Data saved to cache: {cache_path}")
        return True
    except Exception as e:
        logger.error(f"Error saving to cache: {e}")
        return False

def load_from_cache(cache_path: str) -> Optional[Any]:
    """
    Loads data from cache file.

    Args:
        cache_path: Path to cache file

    Returns:
        Optional[Any]: Loaded data or None if failed
    """
    try:
        if not os.path.exists(cache_path):
            logger.info(f"Cache file does not exist: {cache_path}")
            return None

        # Load data depending on file extension
        if cache_path.endswith('.json'):
            with open(cache_path, 'r', encoding='utf-8') as file:
                return json.load(file)
        else:
            with open(cache_path, 'r', encoding='utf-8') as file:
                return file.read()

        logger.info(f"Data loaded from cache: {cache_path}")
    except Exception as e:
        logger.error(f"Error loading from cache: {e}")
        return None

def send_report(datacenter_ids: List[int], attempt: int = 1) -> Optional[Dict]:
    """
    Sends report with datacenter IDs list to central API.

    Args:
        datacenter_ids: List of datacenter IDs
        attempt: Current attempt number (for retry logic)

    Returns:
        Optional[Dict]: Server response or None if failed
    """
    logger.info(f"Sending report to {REPORT_ENDPOINT} (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    payload = {
        "task": TASK_NAME,
        "apikey": API_KEY,
        "answer": datacenter_ids
    }

    headers = {
        "Content-Type": "application/json"
    }

    # Log full payload before sending
    logger.info(f"Sending payload: {json.dumps(payload, ensure_ascii=False)}")

    try:
        response = requests.post(
            REPORT_ENDPOINT,
            json=payload,
            headers=headers,
            timeout=REQUEST_TIMEOUT
        )

        # Log full server response
        logger.info(f"Response code: {response.status_code}")
        logger.info(f"Response headers: {dict(response.headers)}")
        logger.info(f"Response content: {response.text}")

        # Display full response in console
        print(f"\n{'='*50}")
        print(f"RESPONSE FROM SERVER:")
        print(f"Code: {response.status_code}")
        print(f"Content: {response.text}")
        print(f"{'='*50}\n")

        response.raise_for_status()

        result = response.json()
        logger.info("Report sent successfully")
        return result

    except requests.exceptions.RequestException as e:
        logger.error(f"Error sending report: {e}")

        if hasattr(e, 'response') and hasattr(e.response, 'text'):
            logger.error(f"Server response: {e.response.text}")

        # Retry logic
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return send_report(datacenter_ids, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response: {e}")
        return None

def check_for_flag(response) -> Optional[str]:
    """
    Checks if response contains flag in formats:
    - FLG[a-zA-Z0-9_-]+
    - {{FLG:[a-zA-Z0-9_-]+}}

    Args:
        response: Response to check (can be string, dict or other type)

    Returns:
        Optional[str]: Found flag or None if not found
    """
    # Convert response to string for easier searching
    response_str = str(response)

    if not response_str:
        return None

    # Search for flag in {{FLG:XXXX}} format
    match_curly = re.search(FLAG_REGEX_CURLY, response_str)
    if match_curly:
        flag = match_curly.group(0)
        logger.info(f"Flag found in {{{{FLG:XXXX}}}} format: {flag}")
        print(f"\n{'='*50}\nFLAG: {flag}\n{'='*50}\n")
        return flag

    # Search for flag in FLG[a-zA-Z0-9_-]+ format
    match = re.search(FLAG_REGEX, response_str)
    if match:
        flag = match.group(0)
        logger.info(f"Flag found in FLG[a-zA-Z0-9_-]+ format: {flag}")
        print(f"\n{'='*50}\nFLAG: {flag}\n{'='*50}\n")
        return flag

    # Check if text contains the word "flag" or "FLG"
    if "flag" in response_str.lower() or "flg" in response_str.lower():
        logger.info(f"Text contains the word 'flag' or 'FLG': {response_str}")
        print(f"\n{'='*50}\nPOTENTIAL FLAG: {response_str}\n{'='*50}\n")
        return response_str

    logger.info("No flag found in response")
    return None

# --- MAIN WORKFLOW FUNCTIONS ---

def discover_database_structure() -> Tuple[Optional[List[str]], Optional[Dict[str, str]]]:
    """
    Discovers database structure by getting table list and schemas.

    Returns:
        Tuple[Optional[List[str]], Optional[Dict[str, str]]]:
        (table_names, table_schemas) or (None, None) if failed
    """
    logger.info("Starting database structure discovery")

    # Try to load from cache first
    cached_tables = load_from_cache(TABLES_CACHE)
    cached_schemas = load_from_cache(SCHEMAS_CACHE)

    if cached_tables and cached_schemas:
        logger.info("Using cached database structure")
        return cached_tables, cached_schemas

    # Step 1: Get list of tables
    logger.info("Getting list of tables")
    tables_result = execute_db_query("SHOW TABLES;")
    if not tables_result:
        logger.error("Failed to get table list")
        return None, None

    # Extract table names from result
    table_names = []
    if "reply" in tables_result and isinstance(tables_result["reply"], list):
        for row in tables_result["reply"]:
            if isinstance(row, dict):
                # Get first value from the row (table name)
                table_name = list(row.values())[0]
                table_names.append(table_name)
            elif isinstance(row, list):
                # If row is a list, take first element
                table_names.append(row[0])

    logger.info(f"Found tables: {table_names}")

    # Step 2: Get schema for each table
    table_schemas = {}
    for table_name in table_names:
        logger.info(f"Getting schema for table: {table_name}")
        schema_result = execute_db_query(f"SHOW CREATE TABLE {table_name};")
        if schema_result and "reply" in schema_result:
            # Extract CREATE TABLE statement
            if isinstance(schema_result["reply"], list) and len(schema_result["reply"]) > 0:
                row = schema_result["reply"][0]
                if isinstance(row, dict):
                    # Usually the second column contains the CREATE TABLE statement
                    create_statement = list(row.values())[1] if len(row.values()) > 1 else str(row)
                    table_schemas[table_name] = create_statement
                else:
                    table_schemas[table_name] = str(row)
            else:
                table_schemas[table_name] = str(schema_result["reply"])
        else:
            logger.warning(f"Failed to get schema for table: {table_name}")
            table_schemas[table_name] = "Schema not available"

    # Save to cache
    save_to_cache(table_names, TABLES_CACHE)
    save_to_cache(table_schemas, SCHEMAS_CACHE)

    logger.info("Database structure discovery completed")
    return table_names, table_schemas

def analyze_database_structure(table_names: List[str], table_schemas: Dict[str, str]) -> Optional[str]:
    """
    Analyzes database structure using LLM to understand relationships and key fields.

    Args:
        table_names: List of table names
        table_schemas: Dictionary mapping table names to their CREATE TABLE statements

    Returns:
        Optional[str]: Analysis result or None if failed
    """
    logger.info("Starting database structure analysis")

    # Try to load from cache first
    cached_analysis = load_from_cache(ANALYSIS_CACHE)
    if cached_analysis:
        logger.info("Using cached database analysis")
        return cached_analysis

    # Prepare prompt for LLM
    prompt = "Przeanalizuj następujące schematy tabel:\n\n"

    for table_name, schema in table_schemas.items():
        prompt += f"Tabela: {table_name}\n"
        prompt += f"Schema: {schema}\n\n"

    prompt += "\nZnajdź wszystkie AKTYWNE centra danych, którymi zarządzają NIEAKTYWNI menedżerowie."

    # Call LLM for analysis
    analysis = call_llm(prompt, SYSTEM_PROMPT_ANALYZE_SCHEMA)
    if not analysis:
        logger.error("Failed to analyze database structure")
        return None

    # Save to cache
    save_to_cache(analysis, ANALYSIS_CACHE)

    logger.info("Database structure analysis completed")
    return analysis

def generate_sql_query(table_names: List[str], table_schemas: Dict[str, str], analysis: str) -> Optional[str]:
    """
    Generates SQL query using LLM based on database structure and analysis.

    Args:
        table_names: List of table names
        table_schemas: Dictionary mapping table names to their CREATE TABLE statements
        analysis: Database structure analysis from previous step

    Returns:
        Optional[str]: Generated SQL query or None if failed
    """
    logger.info("Starting SQL query generation")

    # Try to load from cache first
    cached_query = load_from_cache(SQL_QUERY_CACHE)
    if cached_query:
        logger.info("Using cached SQL query")
        return cached_query

    # Prepare prompt for LLM
    prompt = "Na podstawie następujących informacji wygeneruj zapytanie SQL:\n\n"

    prompt += "SCHEMATY TABEL:\n"
    for table_name, schema in table_schemas.items():
        prompt += f"Tabela: {table_name}\n"
        prompt += f"Schema: {schema}\n\n"

    prompt += f"ANALIZA STRUKTURY:\n{analysis}\n\n"

    prompt += "Wygeneruj zapytanie SQL, które znajdzie ID wszystkich AKTYWNYCH centrów danych zarządzanych przez NIEAKTYWNYCH menedżerów."

    # Call LLM for SQL generation
    sql_query = call_llm(prompt, SYSTEM_PROMPT_GENERATE_SQL)
    if not sql_query:
        logger.error("Failed to generate SQL query")
        return None

    # Extract clean SQL query (remove markdown formatting if present)
    clean_query = sql_query.strip()
    if clean_query.startswith("```sql"):
        clean_query = clean_query[6:]
    if clean_query.startswith("```"):
        clean_query = clean_query[3:]
    if clean_query.endswith("```"):
        clean_query = clean_query[:-3]
    clean_query = clean_query.strip()

    # Save to cache
    save_to_cache(clean_query, SQL_QUERY_CACHE)

    logger.info(f"Generated SQL query: {clean_query}")
    return clean_query

def execute_query_and_process_results(sql_query: str) -> Optional[List[int]]:
    """
    Executes SQL query and processes results to extract datacenter IDs.

    Args:
        sql_query: SQL query to execute

    Returns:
        Optional[List[int]]: List of datacenter IDs or None if failed
    """
    logger.info("Executing SQL query and processing results")

    # Try to load from cache first
    cached_results = load_from_cache(RESULTS_CACHE)
    if cached_results:
        logger.info("Using cached query results")
        return cached_results

    # Execute the query
    result = execute_db_query(sql_query)
    if not result:
        logger.error("Failed to execute SQL query")
        return None

    # Process results to extract datacenter IDs
    datacenter_ids = []
    if "reply" in result and isinstance(result["reply"], list):
        for row in result["reply"]:
            if isinstance(row, dict):
                # Get first value from the row (should be datacenter ID)
                datacenter_id = list(row.values())[0]
                try:
                    datacenter_ids.append(int(datacenter_id))
                except (ValueError, TypeError):
                    logger.warning(f"Could not convert to int: {datacenter_id}")
            elif isinstance(row, list):
                # If row is a list, take first element
                try:
                    datacenter_ids.append(int(row[0]))
                except (ValueError, TypeError, IndexError):
                    logger.warning(f"Could not convert to int: {row}")

    logger.info(f"Found {len(datacenter_ids)} datacenter IDs: {datacenter_ids}")

    # Save to cache
    save_to_cache(datacenter_ids, RESULTS_CACHE)

    return datacenter_ids

def debug_sql_query(original_query: str, error_message: str, table_schemas: Dict[str, str]) -> Optional[str]:
    """
    Debugs and fixes SQL query using LLM when original query fails.

    Args:
        original_query: Original SQL query that failed
        error_message: Error message from database
        table_schemas: Dictionary mapping table names to their CREATE TABLE statements

    Returns:
        Optional[str]: Fixed SQL query or None if failed
    """
    logger.info("Starting SQL query debugging")

    # Prepare prompt for LLM
    prompt = f"Oryginalne zapytanie SQL:\n{original_query}\n\n"
    prompt += f"Komunikat błędu:\n{error_message}\n\n"
    prompt += "SCHEMATY TABEL:\n"
    for table_name, schema in table_schemas.items():
        prompt += f"Tabela: {table_name}\n"
        prompt += f"Schema: {schema}\n\n"

    prompt += "Popraw zapytanie SQL tak, aby działało poprawnie."

    # Call LLM for debugging
    fixed_query = call_llm(prompt, SYSTEM_PROMPT_DEBUG_SQL)
    if not fixed_query:
        logger.error("Failed to debug SQL query")
        return None

    # Extract clean SQL query (remove markdown formatting if present)
    clean_query = fixed_query.strip()
    if "POPRAWIONE ZAPYTANIE:" in clean_query:
        # Extract query after the "POPRAWIONE ZAPYTANIE:" marker
        parts = clean_query.split("POPRAWIONE ZAPYTANIE:")
        if len(parts) > 1:
            clean_query = parts[1].strip()

    if clean_query.startswith("```sql"):
        clean_query = clean_query[6:]
    if clean_query.startswith("```"):
        clean_query = clean_query[3:]
    if clean_query.endswith("```"):
        clean_query = clean_query[:-3]
    clean_query = clean_query.strip()

    logger.info(f"Fixed SQL query: {clean_query}")
    return clean_query

# --- MAIN FUNCTION ---

def main():
    """
    Main function that orchestrates the agent's workflow.
    """
    logger.info(f"--- Starting {TASK_NAME} agent ---")

    # Validate configuration
    if not validate_configuration():
        logger.critical("Invalid configuration. Exiting.")
        return

    try:
        # Step 1: Discover database structure
        logger.info("Step 1: Discovering database structure")
        table_names, table_schemas = discover_database_structure()
        if not table_names or not table_schemas:
            logger.error("Failed to discover database structure. Exiting.")
            return

        # Step 2: Analyze database structure
        logger.info("Step 2: Analyzing database structure")
        analysis = analyze_database_structure(table_names, table_schemas)
        if not analysis:
            logger.error("Failed to analyze database structure. Exiting.")
            return

        # Step 3: Generate SQL query
        logger.info("Step 3: Generating SQL query")
        sql_query = generate_sql_query(table_names, table_schemas, analysis)
        if not sql_query:
            logger.error("Failed to generate SQL query. Exiting.")
            return

        # Step 4: Execute query and process results
        logger.info("Step 4: Executing query and processing results")
        datacenter_ids = execute_query_and_process_results(sql_query)

        # If query failed, try to debug and fix it
        if datacenter_ids is None:
            logger.warning("Query failed, attempting to debug and fix")
            # Try to get error message by re-executing the query
            error_result = execute_db_query(sql_query)
            error_message = "Unknown error"
            if error_result and "error" in error_result:
                error_message = error_result["error"]

            # Debug the query
            fixed_query = debug_sql_query(sql_query, error_message, table_schemas)
            if fixed_query:
                logger.info("Trying fixed query")
                datacenter_ids = execute_query_and_process_results(fixed_query)

        if datacenter_ids is None:
            logger.error("Failed to execute query and get results. Exiting.")
            return

        # Step 5: Send report
        logger.info("Step 5: Sending report")
        report_response = send_report(datacenter_ids)
        if not report_response:
            logger.error("Failed to send report. Exiting.")
            return

        # Step 6: Check for flag in response
        flag = check_for_flag(report_response)

        # Step 7: Display results
        if flag:
            logger.info(f"SUCCESS! Flag: {flag}")
            print(f"\n{'='*50}\nFLAG: {flag}\n{'='*50}\n")
        else:
            logger.info("Task completed, but no flag found in response")
            print(f"\n{'='*50}\nRESPONSE: {json.dumps(report_response, indent=2)}\n{'='*50}\n")

    except Exception as e:
        logger.critical(f"Unexpected error: {e}", exc_info=True)

    logger.info(f"--- {TASK_NAME} agent finished ---")

# --- SCRIPT ENTRY POINT ---

if __name__ == "__main__":
    main()
