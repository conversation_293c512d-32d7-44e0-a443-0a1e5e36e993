### Wyjaśnienie zadania

Musisz zbudować agenta, który:

1. **Pobierze i rozpakuje** archiwum ZIP zawierające raporty i pliki z faktami.
2. **Przeanalizuje** 10 wskazanych plików raportów (`.txt`) oraz wszystkie pliki z folderu `fakty` (`.txt`).
3. Dla każdego z 10 raportów:
    * **Wyodrębni kluczowe informacje** z treści raportu (co, gdzie, kto, przedmioty/technologie).
    * **Powiąże raport z faktami** z folderu `fakty`, szczególnie poprzez osoby wymienione w raporcie i faktach. Musisz być przygotowany na drobne literówki w nazwiskach.
    * **Wykorzysta informacje** zawarte w nazwie pliku raportu.
    * **Wygeneruje listę słów kluczowych** w języku polskim, w mianowniku, oddzielonych przecinkami. Słowa te muszą precyzyjnie opisywać raport, uwzględniając jego treść, powiązane fakty i nazwę pliku.
4. **Sformatuje wynik** jako obiekt JSON, gdzie kluczem będzie pełna nazwa pliku raportu, a wartością string ze słowami kluczowymi.
5. **Wyśle** przygotowany JSON do wskazanego endpointu API centrali, pamiętając o swoim kluczu API i kodowaniu UTF-8.

Kluczowe jest, aby słowa kluczowe były **precyzyjne**, **w języku polskim**, **w mianowniku** i uwzględniały **całą dostępną wiedzę** (raport, fakty, nazwa pliku).

### Propozycja optymalnego planu działania (Hybrydowe)

1. **Przygotowanie środowiska:**
    * Upewnij się, że masz dostęp do Pythona i menedżera pakietów (np. `pip`).
    * Zainstaluj niezbędne biblioteki: `requests`, `spaCy` (z modelem polskim `pl_core_news_sm`), `fuzzywuzzy` (lub `rapidfuzz`).

2. **Pobieranie i rozpakowywanie danych:**
    * Napisz skrypt, który pobierze plik `pliki_z_fabryki.zip` z podanego URL.
    * Rozpakuj archiwum do tymczasowego katalogu, aby uzyskać dostęp do folderów `raporty` i `fakty`.

3. **Przetwarzanie plików z faktami (LLM + Programistycznie):**
    * **Dla każdego pliku `.txt` z folderu `fakty`:**
        * **Sprawdź, czy odpowiedź LLM dla tego pliku już istnieje na dysku.** Jeśli tak, wczytaj ją i pomiń wywołanie LLM.
        * Jeśli nie, wyślij treść pliku do LLM z precyzyjnym system promptem, aby wyodrębnił kluczowe informacje (np. imię i nazwisko osoby, jej zawód, specjalne umiejętności, powiązane słowa kluczowe) w ustrukturyzowanym formacie (np. JSON).
        * **Zapisz odpowiedź LLM dla tego pliku na dysku** (np. w katalogu tymczasowym, z nazwą pliku odpowiadającą oryginalnemu plikowi faktów, ale z rozszerzeniem `.json`).
        * Zbierz te ustrukturyzowane odpowiedzi LLM (wczytane z dysku lub nowo wygenerowane).
    * **Programistycznie:**
        * Wczytaj wszystkie zebrane odpowiedzi LLM dotyczące faktów.
        * Zbuduj wewnętrzną, przeszukiwalną bazę wiedzy (np. słownik Pythona) z tych danych.
        * **Zaimplementuj mechanizm obsługi literówek/wariacji w nazwiskach** (np. używając `fuzzywuzzy` do dopasowania nazwisk) w tej bazie, aby umożliwić elastyczne wyszukiwanie.

4. **Iteracja przez raporty (LLM + Programistycznie):**
    * **Dla każdego z 10 plików raportów (`.txt`):**
        * **Analiza nazwy pliku:** Programistycznie wyodrębnij słowa kluczowe z nazwy pliku (np. data, sektor, numer raportu).
        * **Analiza treści raportu (z użyciem LLM):**
            * **Sprawdź, czy odpowiedź LLM dla tego raportu już istnieje na dysku.** Jeśli tak, wczytaj ją i pomiń wywołanie LLM.
            * Jeśli nie, wyślij treść raportu do LLM z precyzyjnym system promptem, aby zidentyfikował kluczowe informacje (co się stało, gdzie, kto był zaangażowany, jakie przedmioty/technologie się pojawiły) i wygenerował wstępną listę słów kluczowych w języku polskim. Poproś LLM o zidentyfikowanie osób.
            * **Zapisz odpowiedź LLM dla tego raportu na dysku** (np. w katalogu tymczasowym, z nazwą pliku odpowiadającą oryginalnemu raportowi, ale z rozszerzeniem `.json`).
            * Zbierz wstępne słowa kluczowe i zidentyfikowane osoby z odpowiedzi LLM (wczytane z dysku lub nowo wygenerowane).
        * **Programistyczne łączenie z faktami i finalizacja słów kluczowych:**
            * Zidentyfikuj wszystkie imiona i nazwiska osób wspomnianych w raporcie (możesz użyć zidentyfikowanych przez LLM lub dodatkowo `spaCy` do NER dla większej pewności).
            * Dla każdej zidentyfikowanej osoby, użyj programistycznej bazy wiedzy z faktami (z kroku 3) i `fuzzywuzzy` do znalezienia pasujących faktów.
            * Dodaj słowa kluczowe z powiązanych faktów do listy słów kluczowych dla bieżącego raportu.
            * **Programistycznie:** Użyj `spaCy` do **lematyzacji** wszystkich zebranych słów kluczowych, aby upewnić się, że są w **mianowniku**.
            * Usuń duplikaty z listy słów kluczowych.
            * Zastosuj uogólnienia, jeśli to konieczne (np. "dzika fauna" -> "zwierzęta"). Możesz to zrobić programistycznie, jeśli masz stałe reguły, lub z bardzo precyzyjnym, końcowym promptem do LLM, ale programistycznie jest bezpieczniej.
            * Połącz finalną listę słów kluczowych w jeden string, oddzielając je przecinkami.

5. **Budowanie odpowiedzi JSON:**
    * Stwórz słownik Pythona, gdzie kluczami będą pełne nazwy plików raportów, a wartościami wygenerowane stringi słów kluczowych.
    * Upewnij się, że słownik zawiera dokładnie 10 wpisów.
    * Zbuduj finalny obiekt JSON zgodnie z oczekiwanym formatem, włączając `task`, `apikey` i `answer`.

6. **Wysyłanie rozwiązania:**
    * Wyślij przygotowany obiekt JSON jako żądanie POST do endpointu `/report` centrali.
    * Monitoruj odpowiedzi serwera i używaj ich do debugowania.

### Sugerowany stos technologiczny

* **Język programowania:** **Python**
  * Jest to doskonały wybór ze względu na bogactwo bibliotek do przetwarzania tekstu, operacji na plikach i komunikacji sieciowej.

* **Operacje na plikach i archiwach:**
  * `os` i `zipfile` (wbudowane w Pythona): Do pobierania, rozpakowywania i zarządzania plikami.

* **Przetwarzanie języka naturalnego (NLP):**
  * **`spaCy`** z modelem polskim (`pl_core_news_sm` lub `pl_core_news_md`): To kluczowa biblioteka. Umożliwi:
    * **Lematyzację:** Przekształcanie słów do ich formy podstawowej (mianownik), co jest niezbędne do spełnienia wymagań zadania.
    * **Rozpoznawanie nazwanych encji (NER):** Identyfikacja osób, miejsc, organizacji, co ułatwi wyodrębnianie kluczowych informacji i łączenie z faktami.
    * **Analizę składniową:** Pomocna w zrozumieniu struktury zdań i wyodrębnianiu relacji.
  * Alternatywnie, ale mniej rekomendowane dla tego zadania: `NLTK` (wymaga więcej ręcznej konfiguracji dla polskiego) lub `Stanza`.

* **Dopasowanie rozmyte (Fuzzy Matching) dla nazwisk:**
  * **`fuzzywuzzy`** (lub `rapidfuzz` dla lepszej wydajności): Niezbędne do obsługi literówek i wariacji w nazwiskach osób, aby poprawnie powiązać raporty z faktami.

* **Obsługa JSON:**
  * `json` (wbudowane w Pythona): Do tworzenia i serializacji obiektów JSON.

* **Żądania HTTP:**
  * **`requests`**: Standardowa i bardzo wygodna biblioteka do wykonywania żądań HTTP (POST) do API centrali.
