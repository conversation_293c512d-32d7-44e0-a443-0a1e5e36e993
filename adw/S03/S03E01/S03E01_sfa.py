"""
Single File Agent for S03E01: Dokumenty

This agent processes incident reports and facts, extracts keywords using a hybrid
approach (LLM + programmatic), and sends the results in a specific JSON format.
It is based on the SFA-BBS template and the S03E01 implementation plan and analysis.

Author: Augment Agent
Date: 2025-05-27
Version: 1.0.0
"""

# --- IMPORTS ---
# Standardowe importy z sfa_bbs.py
import os
import re
import json
import logging
import time
import requests
from typing import Dict, List, Any, Optional, Union
from dotenv import load_dotenv
from openai import OpenAI, APIError, APIConnectionError, RateLimitError

# Dodatkowe importy
import zipfile
import shutil
import subprocess # Do instalacji spaCy
import sys # Do instalacji spaCy
import spacy
from fuzzywuzzy import fuzz # lub rapidfuzz

# --- CONFIGURATION ---
# Load environment variables
load_dotenv()

# --- APPLICATION SETTINGS ---
# These settings should be customized for each specific agent implementation
TASK_NAME = "dokumenty"
API_BASE_URL = os.getenv("API_BASE_URL", "https://c3ntrala.ag3nts.org")
DATA_ENDPOINT = f"{API_BASE_URL}/data" # Not used in this task, but kept for SFA-BBS compatibility
REPORT_ENDPOINT = f"{API_BASE_URL}/report"
API_KEY = os.getenv("AIDEVS_API_KEY")

# Request settings
REQUEST_TIMEOUT = 10  # seconds
RETRY_ATTEMPTS = 2
RETRY_DELAY = 2  # seconds

# --- LLM SETTINGS ---
LLM_MODEL = os.getenv("KLUSTER_LLM_MODEL", os.getenv("OPENAI_LLM_MODEL"))
LLM_API_KEY = os.getenv("KLUSTER_API_KEY", os.getenv("OPENAI_API_KEY"))
LLM_BASE_URL = os.getenv("KLUSTER_BASE_URL", os.getenv("OPENAI_BASE_URL"))
LLM_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", "4000"))
LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", "0.3"))
LLM_TOP_P = float(os.getenv("LLM_TOP_P", "1.0"))

# System prompt paths
FACTS_PROMPT_PATH = "adw/S03/S03E01/SYS_PROMPT_facts_extraction.txt"
REPORTS_PROMPT_PATH = "adw/S03/S03E01/SYS_PROMPT_report_analysis.txt"

# --- APPLICATION SETTINGS (specyficzne dla zadania) ---
# Ścieżki do danych wejściowych (zgodnie z wymaganiami)
# Assuming the script is run from the m-agent directory
BASE_DATA_PATH = "adw/S02/pliki_z_fabryki/"
REPORTS_DIR = os.path.join(BASE_DATA_PATH, "") # Raporty są bezpośrednio w pliki_z_fabryki/
FACTS_DIR = os.path.join(BASE_DATA_PATH, "facts/") # Corrected directory name based on analysis

# Katalogi dla buforowanych odpowiedzi LLM i finalnych JSONów
CACHE_DIR = "llm_cache" # W katalogu uruchomienia skryptu
OUTPUT_DIR = "." # W katalogu uruchomienia skryptu

# Progi dla fuzzy matching
FUZZY_MATCH_THRESHOLD = 85

# Mapa uogólnień słów kluczowych
GENERALIZATION_MAP = {
    "dzika fauna": "zwierzęta",
    "zwierzyna lesna": "zwierzęta",
    "wildlife": "zwierzęta",
    # Dodaj inne uogólnienia w miarę potrzeb
}

# LLM Retry settings specific to call_llm_with_cache
LLM_MAX_RETRIES = 3 # Bezpiecznik dla iteracji zapytań do LLM

# --- REGEX PATTERNS ---
# Common regex patterns for flag extraction - customize as needed
FLAG_REGEX = r"FLG[a-zA-Z0-9_-]+"
FLAG_REGEX_CURLY = r"\{\{FLG:[a-zA-Z0-9_-]+\}\}"

# --- LOGGING CONFIGURATION ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# --- HELPER FUNCTIONS (from sfa_bbs.py, potentially modified) ---

def validate_configuration() -> bool:
    """
    Validates that all required configuration variables are set.

    Returns:
        bool: True if configuration is valid, False otherwise
    """
    required_env_vars = {
        "AIDEVS_API_KEY": "API key for Central API",
    }
    missing_vars = []

    for var, description in required_env_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"{var} ({description})")

    # Check for LLM API key - either KLUSTER or OPENAI
    if not os.getenv("KLUSTER_API_KEY") and not os.getenv("OPENAI_API_KEY"):
         missing_vars.append("KLUSTER_API_KEY or OPENAI_API_KEY (API key for LLM)")

    if missing_vars:
        logger.critical(f"Missing required environment variables: {', '.join(missing_vars)}")
        return False

    # Check if API_KEY is set
    if not API_KEY:
        logger.critical("API_KEY is not set, check AIDEVS_API_KEY environment variable")
        return False

    # LLM_API_KEY is handled by the check above

    return True

def fetch_data(url: str, api_key: str, attempt: int = 1) -> Optional[Dict]:
    """
    Fetches data from the specified URL with retry logic.
    (Kept for SFA-BBS compatibility, not used in this task)

    Args:
        url: The URL to fetch data from
        api_key: API key for authentication
        attempt: Current attempt number (for retry logic)

    Returns:
        Optional[Dict]: The fetched data as a dictionary, or None if failed
    """
    logger.info(f"Fetching data from {url} (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    try:
        response = requests.get(
            url,
            headers=headers,
            timeout=REQUEST_TIMEOUT
        )
        response.raise_for_status()

        data = response.json()
        logger.info("Data fetched successfully")
        return data

    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching data: {e}")

        # Retry logic
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return fetch_data(url, api_key, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response: {e}")
        return None

def load_system_prompt(filepath: str) -> str:
    """
    Wczytuje system prompt z określonego pliku.

    Args:
        filepath: Ścieżka do pliku z system promptem.

    Returns:
        str: Zawartość pliku z system promptem.
    """
    try:
        with open(filepath, 'r', encoding='utf-8') as file:
            system_prompt = file.read()
        logger.info(f"Wczytano system prompt z pliku: {filepath}")
        return system_prompt
    except Exception as e:
        logger.error(f"Błąd podczas wczytywania system promptu z {filepath}: {e}")
        logger.warning("Używanie pustego system promptu.")
        return "" # Return empty string if file not found or error

def call_llm(prompt: str, system_prompt_path: str = None, attempt: int = 1) -> Optional[str]:
    """
    Calls the LLM with the given prompt and handles errors with retry logic.
    Uses system prompt from file if provided.

    Args:
        prompt: The user prompt to send to the LLM
        system_prompt_path: Path to system prompt file (optional)
        attempt: Current attempt number (for retry logic)

    Returns:
        Optional[str]: The LLM's response, or None if failed
    """
    logger.info(f"Calling LLM with prompt (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    try:
        # Load system prompt from file if path is provided
        system_prompt = ""
        if system_prompt_path:
            system_prompt = load_system_prompt(system_prompt_path)

        client_params = {"api_key": LLM_API_KEY}
        if LLM_BASE_URL:
            client_params["base_url"] = LLM_BASE_URL

        client = OpenAI(**client_params)

        logger.info(f"Using model: {LLM_MODEL} via API: {LLM_BASE_URL}")

        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})


        completion = client.chat.completions.create(
            model=LLM_MODEL,
            messages=messages,
            temperature=LLM_TEMPERATURE,
            max_tokens=LLM_MAX_TOKENS, # Correct parameter name
            top_p=LLM_TOP_P
        )

        response = completion.choices[0].message.content.strip()
        logger.info("LLM call successful")
        return response

    except (APIError, APIConnectionError, RateLimitError) as e:
        logger.error(f"LLM API error: {e}")

        # Retry logic
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return call_llm(prompt, system_prompt_path, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except Exception as e:
        logger.error(f"Unexpected error during LLM call: {e}")
        return None

def send_report(url: str, api_key: str, task_name: str, answer: Any, attempt: int = 1) -> Optional[Dict]:
    """
    Sends the final report/answer to the specified endpoint.

    Args:
        url: The URL to send the report to
        api_key: API key for authentication
        task_name: Name of the task being performed
        answer: The answer/data to send
        attempt: Current attempt number (for retry logic)

    Returns:
        Optional[Dict]: The response from the server, or None if failed
    """
    logger.info(f"Sending report to {url} (attempt {attempt}/{RETRY_ATTEMPTS + 1})")

    payload = {
        "task": task_name,
        "apikey": api_key,
        "answer": answer # Send the answer directly, it's already the final structure
    }

    headers = {
        "Content-Type": "application/json; charset=utf-8"
    }

    # Log full payload before sending
    logger.info(f"Sending payload: {json.dumps(payload, ensure_ascii=False)}")

    try:
        response = requests.post(
            url,
            json=payload,  # Use json instead of data to let requests handle serialization
            headers=headers,
            timeout=REQUEST_TIMEOUT
        )

        # Log full server response
        logger.info(f"Response code: {response.status_code}")
        logger.info(f"Response headers: {dict(response.headers)}")
        logger.info(f"Response content: {response.text}")

        # Display full response in console
        print(f"\n{'='*50}")
        print(f"RESPONSE FROM SERVER:")
        print(f"Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Content: {response.text}")
        print(f"{'='*50}\n")

        response.raise_for_status()

        result = response.json()
        logger.info("Report sent successfully")
        return result

    except requests.exceptions.RequestException as e:
        logger.error(f"Error sending report: {e}")

        if hasattr(e, 'response') and hasattr(e.response, 'text'):
            logger.error(f"Server response: {e.response.text}")
            logger.error(f"Response headers: {dict(e.response.headers) if hasattr(e.response, 'headers') else 'No headers'}")

            # Display full error response in console
            print(f"\n{'='*50}")
            print(f"ERROR FROM SERVER:")
            print(f"Code: {e.response.status_code if hasattr(e.response, 'status_code') else 'No code'}")
            print(f"Headers: {dict(e.response.headers) if hasattr(e.response, 'headers') else 'No headers'}")
            print(f"Content: {e.response.text}")
            print(f"{'='*50}\n")

        # Retry logic
        if attempt <= RETRY_ATTEMPTS:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)
            return send_report(url, api_key, task_name, answer, attempt + 1)
        else:
            logger.error("Maximum retry attempts reached")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON response: {e}")
        return None


def check_for_flag(response) -> Optional[str]:
    """
    Sprawdza, czy odpowiedź zawiera flagę w formatach:
    - FLG[a-zA-Z0-9_-]+
    - {{FLG:[a-zA-Z0-9_-]+}}

    Args:
        response: Odpowiedź do sprawdzenia (może być string, dict lub inny typ)

    Returns:
        Optional[str]: Znaleziona flaga lub None, jeśli nie znaleziono
    """
    # Convert response to string for easier searching
    response_str = str(response)

    if not response_str:
        return None

    # Search for flag in {{FLG:XXXX}} format
    match_curly = re.search(FLAG_REGEX_CURLY, response_str)
    if match_curly:
        flag = match_curly.group(0)
        logger.info(f"Flag found in {{{{FLG:XXXX}}}} format: {flag}")
        return flag

    # Search for flag in FLG[a-zA-Z0-9_-]+ format
    match = re.search(FLAG_REGEX, response_str)
    if match:
        flag = match.group(0)
        logger.info(f"Flag found in FLG[a-zA-Z0-9_-]+ format: {flag}")
        return flag

    # Check if text contains the word "flag" or "FLG"
    if "flag" in response_str.lower() or "flg" in response_str.lower():
        logger.info(f"Text contains the word 'flag' or 'FLG': {response_str}")
        return response_str # Return the response text as potential flag

    logger.info("No flag found in response")
    return None

# --- HELPER FUNCTIONS (New/Modified for S03E01) ---

def install_spacy_model():
    """Instaluje spaCy i model pl_core_news_sm, jeśli nie są dostępne."""
    try:
        # Sprawdź instalację spaCy
        try:
            import spacy
        except ImportError:
            logger.info("spaCy nie jest zainstalowane. Instalowanie...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "spacy", "fuzzywuzzy", "python-Levenshtein"]) # Install fuzzywuzzy and its dependency
            import spacy # Ponowne zaimportowanie po instalacji

        # Sprawdź pobranie modelu
        try:
            spacy.load("pl_core_news_sm")
            logger.info("Model 'pl_core_news_sm' jest dostępny.")
        except OSError:
            logger.info("Model 'pl_core_news_sm' nie jest pobrany. Pobieranie...")
            subprocess.check_call([sys.executable, "-m", "spacy", "download", "pl_core_news_sm"])
            logger.info("Model 'pl_core_news_sm' pobrany pomyślnie.")
    except Exception as e:
        logger.critical(f"Błąd podczas instalacji spaCy lub pobierania modelu: {e}")
        sys.exit(1) # Zakończ skrypt w przypadku błędu

def call_llm_with_cache(prompt: str, system_prompt_path: str, cache_filepath: str) -> Optional[Dict]:
    """
    Wywołuje LLM, z mechanizmem buforowania i retry.
    Zwraca sparsowany JSON lub None w przypadku błędu.
    """
    if os.path.exists(cache_filepath):
        logger.info(f"Wczytywanie odpowiedzi LLM z cache: {cache_filepath}")
        try:
            with open(cache_filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            logger.error(f"Błąd parsowania JSON z cache {cache_filepath}: {e}. Ponowne wywołanie LLM.")
            os.remove(cache_filepath) # Usuń uszkodzony plik cache
        except Exception as e:
            logger.error(f"Błąd wczytywania z cache {cache_filepath}: {e}. Ponowne wywołanie LLM.")
            os.remove(cache_filepath) # Usuń uszkodzony plik cache

    llm_response_str = None
    for attempt in range(1, LLM_MAX_RETRIES + 1):
        llm_response_str = call_llm(prompt, system_prompt_path, attempt=attempt)
        if llm_response_str:
            try:
                # Attempt to parse JSON, allowing for potential non-JSON parts
                # Find the first and last curly brace to extract the JSON object
                json_match = re.search(r"\{.*\}", llm_response_str, re.DOTALL)
                if json_match:
                    json_string = json_match.group(0)
                    parsed_response = json.loads(json_string)
                    os.makedirs(os.path.dirname(cache_filepath), exist_ok=True) # Ensure directory exists
                    with open(cache_filepath, 'w', encoding='utf-8') as f:
                        json.dump(parsed_response, f, ensure_ascii=False, indent=2)
                    logger.info(f"Odpowiedź LLM zapisana do cache: {cache_filepath}")
                    return parsed_response
                else:
                    logger.error(f"Nie znaleziono obiektu JSON w odpowiedzi LLM (próba {attempt}). Odpowiedź: {llm_response_str[:200]}...")
            except json.JSONDecodeError as e:
                logger.error(f"Błąd parsowania JSON z odpowiedzi LLM (próba {attempt}): {e}. Odpowiedź: {llm_response_str[:200]}...")
            except Exception as e:
                logger.error(f"Błąd zapisu odpowiedzi LLM do cache (próba {attempt}): {e}")
        else:
            logger.warning(f"Brak odpowiedzi od LLM (próba {attempt}).")
        time.sleep(RETRY_DELAY) # Opóźnienie przed kolejną próbą

    logger.error(f"Nie udało się uzyskać poprawnej odpowiedzi JSON od LLM po {LLM_MAX_RETRIES} próbach dla promptu: {prompt[:100]}...")
    return None

def get_txt_files_in_dir(directory: str) -> List[str]:
    """Zwraca listę ścieżek do plików .txt w danym katalogu."""
    txt_files = []
    if not os.path.isdir(directory):
        logger.error(f"Katalog nie istnieje: {directory}")
        return []
    for filename in os.listdir(directory):
        if filename.endswith(".txt") and os.path.isfile(os.path.join(directory, filename)):
            txt_files.append(os.path.join(directory, filename))
    return sorted(txt_files) # Sortowanie dla spójności

def extract_keywords_from_filename(filename: str) -> List[str]:
    """Wyodrębnia słowa kluczowe z nazwy pliku raportu."""
    keywords = []
    base_name = os.path.basename(filename)
    # Przykład: 2024-11-12_report-00-sektor_C4.txt
    # Data
    date_match = re.search(r"(\d{4}-\d{2}-\d{2})", base_name)
    if date_match:
        keywords.append(date_match.group(1))
    # Sektor
    sector_match = re.search(r"sektor_([A-Z0-9]+)", base_name) # Corrected regex to match one or more chars
    if sector_match:
        keywords.append(f"sektor {sector_match.group(1)}")
    # Numer raportu (np. report-00)
    report_num_match = re.search(r"report-(\d+)", base_name)
    if report_num_match:
        keywords.append(f"raport {report_num_match.group(1)}")
    return keywords

def lemmatize_keywords(keywords: List[str], nlp) -> List[str]:
    """Lematyzuje listę słów kluczowych."""
    lemmas = []
    for keyword in keywords:
        # Process each keyword as a separate document
        doc = nlp(keyword)
        for token in doc:
            # Add the lemma if it's not punctuation or whitespace and not already in the list
            lemma = token.lemma_
            if not token.is_punct and not token.is_space and lemma not in lemmas:
                 lemmas.append(lemma)
    return lemmas

def apply_generalizations(keywords: List[str]) -> List[str]:
    """Stosuje mapę uogólnień do słów kluczowych."""
    processed_keywords = []
    for kw in keywords:
        found_generalization = False
        for phrase, generalization in GENERALIZATION_MAP.items():
            # Use fuzzy matching for generalization phrases
            similarity = fuzz.ratio(kw.lower(), phrase.lower())
            if similarity >= FUZZY_MATCH_THRESHOLD: # Use the same threshold as for names
                if generalization not in processed_keywords: # Add uogólnienie, jeśli jeszcze go nie ma
                    processed_keywords.append(generalization)
                found_generalization = True
                break # Przejdź do kolejnego słowa kluczowego
        if not found_generalization and kw not in processed_keywords:
            processed_keywords.append(kw)
    return processed_keywords

# --- MAIN FUNCTION ---
def main():
    """
    Główna funkcja orkiestrująca przepływ pracy agenta.
    """
    logger.info(f"--- Rozpoczynanie pracy agenta {TASK_NAME} ---")

    # 0. Walidacja środowiska i instalacja zależności
    if not validate_configuration():
        logger.critical("Nieprawidłowa konfiguracja. Zamykanie.")
        return
    install_spacy_model()
    try:
        nlp = spacy.load("pl_core_news_sm") # Załaduj model spaCy raz
    except Exception as e:
        logger.critical(f"Nie udało się załadować modelu spaCy 'pl_core_news_sm': {e}. Zamykanie.")
        sys.exit(1)


    # Utwórz katalogi cache, jeśli nie istnieją
    os.makedirs(os.path.join(OUTPUT_DIR, CACHE_DIR, "facts"), exist_ok=True) # Corrected directory name
    os.makedirs(os.path.join(OUTPUT_DIR, CACHE_DIR, "reports"), exist_ok=True)

    # 1. Przetwarzanie plików z faktami (LLM + Programistycznie)
    logger.info("Rozpoczynanie przetwarzania plików z faktami...")
    facts_files = get_txt_files_in_dir(FACTS_DIR)
    all_facts_data: List[Dict] = []

    for fact_filepath in facts_files:
        filename = os.path.basename(fact_filepath)
        cache_filepath = os.path.join(OUTPUT_DIR, CACHE_DIR, "facts", f"{filename}.json") # Corrected directory name

        try:
            with open(fact_filepath, 'r', encoding='utf-8') as f:
                fact_content = f.read()
        except Exception as e:
            logger.error(f"Błąd podczas wczytywania pliku faktów {fact_filepath}: {e}. Pomijanie.")
            continue

        # Dodaj nazwę pliku do promptu, aby LLM mógł ją uwzględnić w odpowiedzi JSON
        prompt_for_llm = f"Nazwa pliku: {filename}\n\nTreść pliku:\n{fact_content}"

        llm_fact_response = call_llm_with_cache(prompt_for_llm, FACTS_PROMPT_PATH, cache_filepath)

        if llm_fact_response:
            all_facts_data.append(llm_fact_response)
        else:
            logger.error(f"Pominięto plik faktów {filename} z powodu błędu LLM.")

    # Zapisz zagregowane dane faktów do pliku JSON
    facts_output_path = os.path.join(OUTPUT_DIR, "facts.json")
    try:
        with open(facts_output_path, 'w', encoding='utf-8') as f:
            json.dump(all_facts_data, f, ensure_ascii=False, indent=2)
        logger.info(f"Zagregowane dane faktów zapisane do: {facts_output_path}")
    except Exception as e:
        logger.error(f"Błąd podczas zapisu zagregowanych danych faktów do {facts_output_path}: {e}")


    # 2. Przetwarzanie raportów (LLM + Programistycznie)
    logger.info("Rozpoczynanie przetwarzania raportów...")
    report_files = get_txt_files_in_dir(REPORTS_DIR)
    final_report_keywords: Dict[str, str] = {}

    # Ogranicz do 10 raportów, jeśli jest ich więcej
    # Zakładamy, że pliki raportów są bezpośrednio w BASE_DATA_PATH
    # Jeśli nazwy plików są kluczowe, można je filtrować np. po 'report-'
    # Na potrzeby zadania, bierzemy pierwsze 10 znalezionych plików txt, które zawierają 'report' w nazwie
    selected_report_files = [f for f in report_files if "report" in os.path.basename(f).lower()][:10]

    if len(selected_report_files) != 10:
        logger.warning(f"Znaleziono {len(selected_report_files)} plików raportów zawierających 'report' w nazwie. Oczekiwano 10.")
        # If less than 10 are found, try to take the first 10 .txt files regardless of name
        if len(selected_report_files) < 10:
             all_txt_files = get_txt_files_in_dir(REPORTS_DIR)
             selected_report_files = all_txt_files[:10]
             logger.warning(f"Wybrano pierwsze {len(selected_report_files)} plików .txt z katalogu raportów.")


    for report_filepath in selected_report_files:
        filename = os.path.basename(report_filepath)
        cache_filepath = os.path.join(OUTPUT_DIR, CACHE_DIR, "reports", f"{filename}.json")

        try:
            with open(report_filepath, 'r', encoding='utf-8') as f:
                report_content = f.read()
        except Exception as e:
            logger.error(f"Błąd podczas wczytywania pliku raportu {report_filepath}: {e}. Pomijanie.")
            continue

        # Dodaj nazwę pliku do promptu, aby LLM mógł ją uwzględnić w odpowiedzi JSON
        prompt_for_llm = f"Nazwa pliku: {filename}\n\nTreść raportu:\n{report_content}"

        llm_report_response = call_llm_with_cache(prompt_for_llm, REPORTS_PROMPT_PATH, cache_filepath)

        if not llm_report_response:
            logger.error(f"Pominięto raport {filename} z powodu błędu LLM.")
            continue

        current_report_keywords = set()

        # a. Analiza nazwy pliku
        keywords_from_filename = extract_keywords_from_filename(filename)
        current_report_keywords.update(keywords_from_filename)

        # b. Wstępne słowa kluczowe z LLM
        if "wstepne_slowa_kluczowe" in llm_report_response and isinstance(llm_report_response["wstepne_slowa_kluczowe"], list):
            current_report_keywords.update(llm_report_response["wstepne_slowa_kluczowe"])

        # c. Łączenie z faktami
        report_persons = set()
        if "osoby_zaangazowane" in llm_report_response and isinstance(llm_report_response["osoby_zaangazowane"], list):
            report_persons.update(llm_report_response["osoby_zaangazowane"])

        for person_in_report in report_persons:
            for fact_entry in all_facts_data:
                if "osoby" in fact_entry and isinstance(fact_entry["osoby"], list):
                    for fact_person_data in fact_entry["osoby"]:
                        if "imie_nazwisko" in fact_person_data:
                            # Użyj fuzzywuzzy do dopasowania nazwisk
                            similarity = fuzz.ratio(person_in_report.lower(), fact_person_data["imie_nazwisko"].lower())
                            if similarity >= FUZZY_MATCH_THRESHOLD:
                                logger.info(f"Dopasowano osobę '{person_in_report}' z raportu do '{fact_person_data['imie_nazwisko']}' z faktów (Podobieństwo: {similarity}).")
                                if "slowa_kluczowe" in fact_person_data and isinstance(fact_person_data["slowa_kluczowe"], list):
                                    current_report_keywords.update(fact_person_data["slowa_kluczowe"])
                                # Możesz dodać też ogólne słowa kluczowe z pliku faktów, jeśli to ma sens
                                if "ogolne_slowa_kluczowe" in fact_entry and isinstance(fact_entry["ogolne_slowa_kluczowe"], list):
                                    current_report_keywords.update(fact_entry["ogolne_slowa_kluczowe"])

        # d. Lematyzacja, deduplikacja i uogólnienia
        # Konwertuj set na listę dla lematyzacji
        keywords_list = list(current_report_keywords)
        lemmatized_keywords = lemmatize_keywords(keywords_list, nlp)
        final_keywords_processed = apply_generalizations(lemmatized_keywords)

        # Usuń duplikaty ponownie po lematyzacji i uogólnieniach
        final_keywords_unique = sorted(list(set(final_keywords_processed)))

        final_report_keywords[filename] = ",".join(final_keywords_unique)

    # Zapisz zagregowane dane raportów do pliku JSON
    reports_output_path = os.path.join(OUTPUT_DIR, "raports.json")
    try:
        with open(reports_output_path, 'w', encoding='utf-8') as f:
            json.dump(final_report_keywords, f, ensure_ascii=False, indent=2)
        logger.info(f"Zagregowane słowa kluczowe raportów zapisane do: {reports_output_path}")
    except Exception as e:
        logger.error(f"Błąd podczas zapisu zagregowanych słów kluczowych raportów do {reports_output_path}: {e}")


    # 3. Budowanie odpowiedzi JSON
    if len(final_report_keywords) != 10:
        logger.error(f"Wygenerowano {len(final_report_keywords)} wpisów dla raportów. Oczekiwano 10. Sprawdź pliki wejściowe.")
        # Możesz zdecydować, czy kontynuować, czy zakończyć w przypadku niezgodności liczby raportów

    final_answer_payload = final_report_keywords # The answer is the dictionary of keywords

    # Zapisz finalną odpowiedź przed wysłaniem
    response_output_path = os.path.join(OUTPUT_DIR, "response.json")
    try:
        with open(response_output_path, 'w', encoding='utf-8') as f:
            json.dump(final_answer_payload, f, ensure_ascii=False, indent=2)
        logger.info(f"Finalna odpowiedź zapisana do: {response_output_path}")
    except Exception as e:
        logger.error(f"Błąd podczas zapisu finalnej odpowiedzi do {response_output_path}: {e}")


    # 4. Wysyłanie rozwiązania
    report_response = send_report(REPORT_ENDPOINT, API_KEY, TASK_NAME, final_answer_payload)

    if not report_response:
        logger.error("Nie udało się wysłać raportu do centrali. Zamykanie.")
        return

    # 5. Sprawdzenie flagi w odpowiedzi
    flag = check_for_flag(report_response)
    if flag:
        logger.info(f"SUKCES! Znaleziono flagę: {flag}")
        print(f"\n{'='*50}\nFLAGA: {flag}\n{'='*50}\n")
    else:
        logger.info("Zadanie zakończone, ale nie znaleziono flagi w odpowiedzi.")
        print(f"\n{'='*50}\nODPOWIEDŹ Z CENTRALII: {json.dumps(report_response, indent=2)}\n{'='*50}\n")

    logger.info(f"--- Agent {TASK_NAME} zakończył pracę ---")

# --- SCRIPT ENTRY POINT ---
if __name__ == "__main__":
    main()
