# Plan Implementacji Aplikacji SFA - S03E01: Dokumenty

## 1. Cel i Zakres

Celem tej implementacji jest stworzenie aplikacji Single File Agent (SFA) w Pythonie, która automatyzuje proces generowania metadanych (słów kluczowych) dla 10 raportów tekstowych dotyczących incydentów bezpieczeństwa. Aplikacja będzie wykorzystywać hybrydowe podejście, łącz<PERSON>ce możliwości dużych modeli językowych (LLM) do ekstrakcji semantycznej z precyzją i kontrolą programistycznego przetwarzania danych. Finalne metadane zostaną wysłane do centrali w formacie JSON.

Aplikacja będzie zgodna z najlepszymi praktykami projektowania oprogramowania (DRY, KISS, YAGNI, SOLID) oraz będzie rozszerzać i modyfikować dostarczony schemat `sfa_bbs.py`.

## 2. Architektura i Struktura Pliku

Aplikacja zostanie zaimplementowana jako pojedynczy plik `.py`, rozszerzający funkcjonalność `sfa_bbs.py`. Kluczowe funkcje zostaną podzielone na modułowe metody, aby zapewnić czytelność, testowalność i zgodność z zasadami czystego kodu.

**Struktura pliku (rozszerzenie `sfa_bbs.py`):**

```python
# --- IMPORTS ---
# Standardowe importy z sfa_bbs.py
import os
import re
import json
import logging
import time
import requests
from typing import Dict, List, Any, Optional, Union
from dotenv import load_dotenv
from openai import OpenAI, APIError, APIConnectionError, RateLimitError

# Dodatkowe importy
import zipfile
import shutil
import subprocess # Do instalacji spaCy
import sys # Do instalacji spaCy
import spacy
from fuzzywuzzy import fuzz # lub rapidfuzz

# --- CONFIGURATION ---
# ... (istniejąca konfiguracja z sfa_bbs.py) ...

# --- APPLICATION SETTINGS (specyficzne dla zadania) ---
TASK_NAME = "dokumenty"
# ... (pozostałe ustawienia z sfa_bbs.py) ...

# Ścieżki do danych wejściowych (zgodnie z wymaganiami)
BASE_DATA_PATH = "/Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S02/pliki_z_fabryki/"
REPORTS_DIR = os.path.join(BASE_DATA_PATH, "") # Raporty są bezpośrednio w pliki_z_fabryki/
FACTS_DIR = os.path.join(BASE_DATA_PATH, "facts/")

# Ścieżki do promptów LLM
FACTS_PROMPT_PATH = "adw/S03/S03E01/facts_extraction_prompt.txt"
REPORTS_PROMPT_PATH = "adw/S03/S03E01/report_analysis_prompt.txt"

# Katalogi dla buforowanych odpowiedzi LLM i finalnych JSONów
CACHE_DIR = "llm_cache" # W katalogu uruchomienia skryptu
OUTPUT_DIR = "." # W katalogu uruchomienia skryptu

# Progi dla fuzzy matching
FUZZY_MATCH_THRESHOLD = 85

# Mapa uogólnień słów kluczowych
GENERALIZATION_MAP = {
    "dzika fauna": "zwierzęta",
    "zwierzyna lesna": "zwierzęta",
    "wildlife": "zwierzęta",
    # Dodaj inne uogólnienia w miarę potrzeb
}

# --- LLM SETTINGS ---
# ... (istniejąca konfiguracja z sfa_bbs.py) ...
LLM_MAX_RETRIES = 3 # Bezpiecznik dla iteracji zapytań do LLM

# --- LOGGING CONFIGURATION ---
# ... (istniejąca konfiguracja z sfa_bbs.py) ...

# --- HELPER FUNCTIONS ---
# ... (istniejące funkcje z sfa_bbs.py: validate_configuration, fetch_data, send_report, check_for_flag) ...

# Nowe/zmodyfikowane funkcje pomocnicze
def load_system_prompt(filepath: str) -> str:
    """Wczytuje system prompt z określonego pliku."""
    # Zmodyfikowana funkcja z sfa_bbs.py, aby przyjmowała ścieżkę jako argument
    # ... (implementacja) ...

def install_spacy_model():
    """Instaluje spaCy i model pl_core_news_sm, jeśli nie są dostępne."""
    try:
        # Sprawdź instalację spaCy
        try:
            import spacy
        except ImportError:
            logger.info("spaCy nie jest zainstalowane. Instalowanie...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "spacy"])
            import spacy # Ponowne zaimportowanie po instalacji

        # Sprawdź pobranie modelu
        try:
            spacy.load("pl_core_news_sm")
            logger.info("Model 'pl_core_news_sm' jest dostępny.")
        except OSError:
            logger.info("Model 'pl_core_news_sm' nie jest pobrany. Pobieranie...")
            subprocess.check_call([sys.executable, "-m", "spacy", "download", "pl_core_news_sm"])
            logger.info("Model 'pl_core_news_sm' pobrany pomyślnie.")
    except Exception as e:
        logger.critical(f"Błąd podczas instalacji spaCy lub pobierania modelu: {e}")
        sys.exit(1) # Zakończ skrypt w przypadku błędu

def call_llm_with_cache(prompt: str, system_prompt_path: str, cache_filepath: str) -> Optional[Dict]:
    """
    Wywołuje LLM, z mechanizmem buforowania i retry.
    Zwraca sparsowany JSON lub None w przypadku błędu.
    """
    if os.path.exists(cache_filepath):
        logger.info(f"Wczytywanie odpowiedzi LLM z cache: {cache_filepath}")
        try:
            with open(cache_filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            logger.error(f"Błąd parsowania JSON z cache {cache_filepath}: {e}. Ponowne wywołanie LLM.")
            os.remove(cache_filepath) # Usuń uszkodzony plik cache
        except Exception as e:
            logger.error(f"Błąd wczytywania z cache {cache_filepath}: {e}. Ponowne wywołanie LLM.")
            os.remove(cache_filepath) # Usuń uszkodzony plik cache

    llm_response_str = None
    for attempt in range(1, LLM_MAX_RETRIES + 1):
        llm_response_str = call_llm(prompt, system_prompt_path, attempt=attempt)
        if llm_response_str:
            try:
                parsed_response = json.loads(llm_response_str)
                with open(cache_filepath, 'w', encoding='utf-8') as f:
                    json.dump(parsed_response, f, ensure_ascii=False, indent=2)
                logger.info(f"Odpowiedź LLM zapisana do cache: {cache_filepath}")
                return parsed_response
            except json.JSONDecodeError as e:
                logger.error(f"Błąd parsowania JSON z odpowiedzi LLM (próba {attempt}): {e}. Odpowiedź: {llm_response_str[:200]}...")
            except Exception as e:
                logger.error(f"Błąd zapisu odpowiedzi LLM do cache (próba {attempt}): {e}")
        else:
            logger.warning(f"Brak odpowiedzi od LLM (próba {attempt}).")
        time.sleep(RETRY_DELAY) # Opóźnienie przed kolejną próbą

    logger.error(f"Nie udało się uzyskać poprawnej odpowiedzi JSON od LLM po {LLM_MAX_RETRIES} próbach dla promptu: {prompt[:100]}...")
    return None

def get_txt_files_in_dir(directory: str) -> List[str]:
    """Zwraca listę ścieżek do plików .txt w danym katalogu."""
    txt_files = []
    if not os.path.isdir(directory):
        logger.error(f"Katalog nie istnieje: {directory}")
        return []
    for filename in os.listdir(directory):
        if filename.endswith(".txt") and os.path.isfile(os.path.join(directory, filename)):
            txt_files.append(os.path.join(directory, filename))
    return sorted(txt_files) # Sortowanie dla spójności

def extract_keywords_from_filename(filename: str) -> List[str]:
    """Wyodrębnia słowa kluczowe z nazwy pliku raportu."""
    keywords = []
    base_name = os.path.basename(filename)
    # Przykład: 2024-11-12_report-00-sektor_C4.txt
    # Data
    date_match = re.search(r"(\d{4}-\d{2}-\d{2})", base_name)
    if date_match:
        keywords.append(date_match.group(1))
    # Sektor
    sector_match = re.search(r"sektor_([A-Z0-9+)", base_name)
    if sector_match:
        keywords.append(f"sektor {sector_match.group(1)}")
    # Numer raportu (np. report-00)
    report_num_match = re.search(r"report-(\d+)", base_name)
    if report_num_match:
        keywords.append(f"raport {report_num_match.group(1)}")
    return keywords

def lemmatize_keywords(keywords: List[str], nlp) -> List[str]:
    """Lematyzuje listę słów kluczowych."""
    lemmas = []
    for keyword in keywords:
        doc = nlp(keyword)
        for token in doc:
            # Upewnij się, że lemat jest w mianowniku i dodaj tylko unikalne
            if token.lemma_ not in lemmas:
                lemmas.append(token.lemma_)
    return lemmas

def apply_generalizations(keywords: List[str]) -> List[str]:
    """Stosuje mapę uogólnień do słów kluczowych."""
    processed_keywords = []
    for kw in keywords:
        found_generalization = False
        for phrase, generalization in GENERALIZATION_MAP.items():
            if phrase.lower() in kw.lower(): # Sprawdź, czy fraza jest częścią słowa kluczowego
                if generalization not in processed_keywords: # Dodaj uogólnienie, jeśli jeszcze go nie ma
                    processed_keywords.append(generalization)
                found_generalization = True
                break # Przejdź do kolejnego słowa kluczowego
        if not found_generalization and kw not in processed_keywords:
            processed_keywords.append(kw)
    return processed_keywords

# --- MAIN FUNCTION ---
def main():
    """
    Główna funkcja orkiestrująca przepływ pracy agenta.
    """
    logger.info(f"--- Rozpoczynanie pracy agenta {TASK_NAME} ---")

    # 0. Walidacja środowiska i instalacja zależności
    if not validate_configuration():
        logger.critical("Nieprawidłowa konfiguracja. Zamykanie.")
        return
    install_spacy_model()
    nlp = spacy.load("pl_core_news_sm") # Załaduj model spaCy raz

    # Utwórz katalogi cache, jeśli nie istnieją
    os.makedirs(os.path.join(OUTPUT_DIR, CACHE_DIR, "facts"), exist_ok=True)
    os.makedirs(os.path.join(OUTPUT_DIR, CACHE_DIR, "reports"), exist_ok=True)

    # 1. Przetwarzanie plików z faktami (LLM + Programistycznie)
    logger.info("Rozpoczynanie przetwarzania plików z faktami...")
    facts_files = get_txt_files_in_dir(FACTS_DIR)
    all_facts_data: List[Dict] = []

    for fact_filepath in facts_files:
        filename = os.path.basename(fact_filepath)
        cache_filepath = os.path.join(OUTPUT_DIR, CACHE_DIR, "facts", f"{filename}.json")
        
        with open(fact_filepath, 'r', encoding='utf-8') as f:
            fact_content = f.read()
        
        # Dodaj nazwę pliku do promptu, aby LLM mógł ją uwzględnić w odpowiedzi JSON
        prompt_for_llm = f"Nazwa pliku: {filename}\n\nTreść pliku:\n{fact_content}"
        
        llm_fact_response = call_llm_with_cache(prompt_for_llm, FACTS_PROMPT_PATH, cache_filepath)
        
        if llm_fact_response:
            all_facts_data.append(llm_fact_response)
        else:
            logger.error(f"Pominięto plik faktów {filename} z powodu błędu LLM.")

    # Zapisz zagregowane dane faktów do pliku JSON
    facts_output_path = os.path.join(OUTPUT_DIR, "facts.json")
    with open(facts_output_path, 'w', encoding='utf-8') as f:
        json.dump(all_facts_data, f, ensure_ascii=False, indent=2)
    logger.info(f"Zagregowane dane faktów zapisane do: {facts_output_path}")

    # 2. Przetwarzanie raportów (LLM + Programistycznie)
    logger.info("Rozpoczynanie przetwarzania raportów...")
    report_files = get_txt_files_in_dir(REPORTS_DIR)
    final_report_keywords: Dict[str, str] = {}

    # Ogranicz do 10 raportów, jeśli jest ich więcej
    # Zakładamy, że pliki raportów są bezpośrednio w BASE_DATA_PATH
    # Jeśli nazwy plików są kluczowe, można je filtrować np. po 'report-'
    # Na potrzeby zadania, bierzemy pierwsze 10 znalezionych plików txt, które zawierają 'report' w nazwie
    selected_report_files = [f for f in report_files if "report" in os.path.basename(f).lower()][:10]

    if len(selected_report_files) != 10:
        logger.warning(f"Znaleziono {len(selected_report_files)} plików raportów. Oczekiwano 10.")

    for report_filepath in selected_report_files:
        filename = os.path.basename(report_filepath)
        cache_filepath = os.path.join(OUTPUT_DIR, CACHE_DIR, "reports", f"{filename}.json")

        with open(report_filepath, 'r', encoding='utf-8') as f:
            report_content = f.read()
        
        # Dodaj nazwę pliku do promptu, aby LLM mógł ją uwzględnić w odpowiedzi JSON
        prompt_for_llm = f"Nazwa pliku: {filename}\n\nTreść raportu:\n{report_content}"

        llm_report_response = call_llm_with_cache(prompt_for_llm, REPORTS_PROMPT_PATH, cache_filepath)

        if not llm_report_response:
            logger.error(f"Pominięto raport {filename} z powodu błędu LLM.")
            continue

        current_report_keywords = set()

        # a. Analiza nazwy pliku
        keywords_from_filename = extract_keywords_from_filename(filename)
        current_report_keywords.update(keywords_from_filename)

        # b. Wstępne słowa kluczowe z LLM
        if "wstepne_slowa_kluczowe" in llm_report_response and isinstance(llm_report_response["wstepne_slowa_kluczowe"], list):
            current_report_keywords.update(llm_report_response["wstepne_slowa_kluczowe"])

        # c. Łączenie z faktami
        report_persons = set()
        if "osoby_zaangazowane" in llm_report_response and isinstance(llm_report_response["osoby_zaangazowane"], list):
            report_persons.update(llm_report_response["osoby_zaangazowane"])

        for person_in_report in report_persons:
            for fact_entry in all_facts_data:
                if "osoby" in fact_entry and isinstance(fact_entry["osoby"], list):
                    for fact_person_data in fact_entry["osoby"]:
                        if "imie_nazwisko" in fact_person_data:
                            # Użyj fuzzywuzzy do dopasowania nazwisk
                            similarity = fuzz.ratio(person_in_report.lower(), fact_person_data["imie_nazwisko"].lower())
                            if similarity >= FUZZY_MATCH_THRESHOLD:
                                logger.info(f"Dopasowano osobę '{person_in_report}' z raportu do '{fact_person_data['imie_nazwisko']}' z faktów (Podobieństwo: {similarity}).")
                                if "slowa_kluczowe" in fact_person_data and isinstance(fact_person_data["slowa_kluczowe"], list):
                                    current_report_keywords.update(fact_person_data["slowa_kluczowe"])
                                # Możesz dodać też ogólne słowa kluczowe z pliku faktów, jeśli to ma sens
                                if "ogolne_slowa_kluczowe" in fact_entry and isinstance(fact_entry["ogolne_slowa_kluczowe"], list):
                                    current_report_keywords.update(fact_entry["ogolne_slowa_kluczowe"])

        # d. Lematyzacja, deduplikacja i uogólnienia
        # Konwertuj set na listę dla lematyzacji
        keywords_list = list(current_report_keywords)
        lemmatized_keywords = lemmatize_keywords(keywords_list, nlp)
        final_keywords_processed = apply_generalizations(lemmatized_keywords)
        
        # Usuń duplikaty ponownie po lematyzacji i uogólnieniach
        final_keywords_unique = sorted(list(set(final_keywords_processed)))

        final_report_keywords[filename] = ",".join(final_keywords_unique)

    # Zapisz zagregowane dane raportów do pliku JSON
    reports_output_path = os.path.join(OUTPUT_DIR, "raports.json")
    with open(reports_output_path, 'w', encoding='utf-8') as f:
        json.dump(final_report_keywords, f, ensure_ascii=False, indent=2)
    logger.info(f"Zagregowane słowa kluczowe raportów zapisane do: {reports_output_path}")

    # 3. Budowanie odpowiedzi JSON
    if len(final_report_keywords) != 10:
        logger.error(f"Wygenerowano {len(final_report_keywords)} wpisów dla raportów. Oczekiwano 10. Sprawdź pliki wejściowe.")
        # Możesz zdecydować, czy kontynuować, czy zakończyć w przypadku niezgodności liczby raportów

    final_answer_payload = {
        "task": TASK_NAME,
        "apikey": API_KEY,
        "answer": final_report_keywords
    }

    # Zapisz finalną odpowiedź przed wysłaniem
    response_output_path = os.path.join(OUTPUT_DIR, "response.json")
    with open(response_output_path, 'w', encoding='utf-8') as f:
        json.dump(final_answer_payload, f, ensure_ascii=False, indent=2)
    logger.info(f"Finalna odpowiedź zapisana do: {response_output_path}")

    # 4. Wysyłanie rozwiązania
    report_response = send_report(REPORT_ENDPOINT, API_KEY, TASK_NAME, final_answer_payload["answer"])
    
    if not report_response:
        logger.error("Nie udało się wysłać raportu do centrali. Zamykanie.")
        return

    # 5. Sprawdzenie flagi w odpowiedzi
    flag = check_for_flag(report_response)
    if flag:
        logger.info(f"SUKCES! Znaleziono flagę: {flag}")
        print(f"\n{'='*50}\nFLAGA: {flag}\n{'='*50}\n")
    else:
        logger.info("Zadanie zakończone, ale nie znaleziono flagi w odpowiedzi.")
        print(f"\n{'='*50}\nODPOWIEDŹ Z CENTRALII: {json.dumps(report_response, indent=2)}\n{'='*50}\n")

    logger.info(f"--- Agent {TASK_NAME} zakończył pracę ---")

# --- SCRIPT ENTRY POINT ---
if __name__ == "__main__":
    main()
```

## 3. Szczegółowy Plan Implementacji

### 3.1. Konfiguracja i Ustawienia Początkowe

* **`sfa_bbs.py` jako baza:** Rozpocznij od skopiowania zawartości `sfa_bbs.py` do nowego pliku (np. `s03e01_agent.py`).
* **Zmienne środowiskowe:** Upewnij się, że plik `.env` zawiera `AIDEVS_API_KEY`, `KLUSTER_API_KEY` (lub `OPENAI_API_KEY`), `KLUSTER_LLM_MODEL` (lub `OPENAI_LLM_MODEL`), `KLUSTER_BASE_URL` (lub `OPENAI_BASE_URL`).
* **Stałe specyficzne dla zadania:**
  * `TASK_NAME = "dokumenty"`
  * `BASE_DATA_PATH`: Ustaw na `/Users/<USER>/Desktop/coding/TRENING/AI/BRAVE/AIDevs/m-agent/adw/S02/pliki_z_fabryki/`.
  * `REPORTS_DIR` i `FACTS_DIR`: Zdefiniuj ścieżki względne do `BASE_DATA_PATH`.
  * `FACTS_PROMPT_PATH` i `REPORTS_PROMPT_PATH`: Ustaw ścieżki do plików promptów (`adw/S03/S03E01/facts_extraction_prompt.txt`, `adw/S03/S03E01/report_analysis_prompt.txt`).
  * `CACHE_DIR = "llm_cache"`: Katalog na buforowane odpowiedzi LLM (w katalogu uruchomienia skryptu).
  * `OUTPUT_DIR = "."`: Katalog na finalne pliki JSON (w katalogu uruchomienia skryptu).
  * `FUZZY_MATCH_THRESHOLD = 85`: Próg podobieństwa dla `fuzzywuzzy`.
  * `GENERALIZATION_MAP`: Zdefiniuj mapę uogólnień słów kluczowych (np. `{"dzika fauna": "zwierzęta"}`).
  * `LLM_MAX_RETRIES = 3`: Bezpiecznik dla zapytań do LLM.
* **Logging:** Zachowaj i rozszerz konfigurację logowania z `sfa_bbs.py`.

### 3.2. Funkcje Pomocnicze (Modyfikacje i Nowe)

* **`load_system_prompt(filepath: str)`:** Zmodyfikuj istniejącą funkcję, aby przyjmowała ścieżkę do pliku promptu jako argument.
* **`install_spacy_model()`:**
  * Nowa funkcja, która sprawdzi, czy `spaCy` jest zainstalowane. Jeśli nie, zainstaluje je za pomocą `pip`.
  * Następnie sprawdzi, czy model `pl_core_news_sm` jest pobrany. Jeśli nie, pobierze go.
  * W przypadku błędów instalacji/pobierania, zaloguje błąd i zakończy działanie skryptu (`sys.exit(1)`).
* **`call_llm_with_cache(prompt: str, system_prompt_path: str, cache_filepath: str)`:**
  * Nowa funkcja, która opakowuje `call_llm` z `sfa_bbs.py`.
  * **Buforowanie:** Przed wywołaniem LLM, sprawdzi, czy plik `cache_filepath` istnieje. Jeśli tak, wczyta z niego JSON i zwróci. Obsłuży błędy parsowania JSON z cache (usunie uszkodzony plik i ponowi wywołanie LLM).
  * **Wywołanie LLM:** Jeśli plik cache nie istnieje, wywoła `call_llm` z `sfa_bbs.py`.
  * **Retry Logic:** Zaimplementuje pętlę z `LLM_MAX_RETRIES` próbami wywołania LLM.
  * **Zapis do cache:** Po otrzymaniu poprawnej odpowiedzi od LLM, sparsuje ją jako JSON i zapisze do `cache_filepath`.
  * **Obsługa błędów JSON:** Będzie próbować sparsować odpowiedź LLM jako JSON. Jeśli się nie uda, zaloguje błąd i ponowi próbę (do `LLM_MAX_RETRIES`).
  * Zwróci sparsowany obiekt JSON lub `None` w przypadku niepowodzenia po wszystkich próbach.
* **`get_txt_files_in_dir(directory: str)`:** Nowa funkcja, która zwróci posortowaną listę pełnych ścieżek do plików `.txt` w danym katalogu. Sprawdzi istnienie katalogu.
* **`extract_keywords_from_filename(filename: str)`:** Nowa funkcja do wyodrębniania słów kluczowych z nazwy pliku raportu (data, sektor, numer raportu). Użyje wyrażeń regularnych.
* **`lemmatize_keywords(keywords: List[str], nlp)`:** Nowa funkcja, która przyjmie listę słów kluczowych i obiekt `nlp` (z `spaCy`). Zwróci listę lematyzowanych słów kluczowych w mianowniku.
* **`apply_generalizations(keywords: List[str])`:** Nowa funkcja, która przyjmie listę słów kluczowych i zastosuje `GENERALIZATION_MAP` do uogólnień.

### 3.3. Główna Logika (`main` function)

1. **Inicjalizacja:**
    * Wywołaj `validate_configuration()`.
    * Wywołaj `install_spacy_model()` i załaduj model `nlp = spacy.load("pl_core_news_sm")`.
    * Utwórz katalogi `llm_cache/facts` i `llm_cache/reports` w katalogu uruchomienia skryptu.

2. **Przetwarzanie plików z faktami (Krok 3 z planu):**
    * Pobierz listę plików `.txt` z `FACTS_DIR` za pomocą `get_txt_files_in_dir()`.
    * Zainicjuj pustą listę `all_facts_data: List[Dict]`.
    * Dla każdego pliku faktów:
        * Skonstruuj `cache_filepath` dla odpowiedzi LLM (np. `llm_cache/facts/nazwa_pliku.json`).
        * Wczytaj treść pliku faktów.
        * Przygotuj prompt dla LLM, zawierający nazwę pliku i jego treść.
        * Wywołaj `llm_fact_response = call_llm_with_cache(prompt, FACTS_PROMPT_PATH, cache_filepath)`.
        * Jeśli `llm_fact_response` nie jest `None`, dodaj go do `all_facts_data`.
    * **Zapisz `all_facts_data` do `facts.json`** w `OUTPUT_DIR`.

3. **Przetwarzanie raportów (Krok 4 z planu):**
    * Pobierz listę plików `.txt` z `REPORTS_DIR` za pomocą `get_txt_files_in_dir()`.
    * **Wybierz dokładnie 10 plików raportów:** Przeszukaj listę plików i wybierz te, które zawierają "report" w nazwie, ograniczając do pierwszych 10. Zaloguj ostrzeżenie, jeśli liczba nie zgadza się z 10.
    * Zainicjuj pusty słownik `final_report_keywords: Dict[str, str]`.
    * Dla każdego wybranego pliku raportu:
        * Skonstruuj `cache_filepath` dla odpowiedzi LLM (np. `llm_cache/reports/nazwa_raportu.json`).
        * Wczytaj treść pliku raportu.
        * Przygotuj prompt dla LLM, zawierający nazwę pliku i jego treść.
        * Wywołaj `llm_report_response = call_llm_with_cache(prompt, REPORTS_PROMPT_PATH, cache_filepath)`.
        * Jeśli `llm_report_response` jest `None`, pomiń ten raport i zaloguj błąd.
        * Zainicjuj `current_report_keywords = set()`.
        * **Analiza nazwy pliku:** Dodaj słowa kluczowe z nazwy pliku (`extract_keywords_from_filename()`) do `current_report_keywords`.
        * **Wstępne słowa kluczowe z LLM:** Dodaj `wstepne_slowa_kluczowe` z `llm_report_response` do `current_report_keywords`.
        * **Łączenie z faktami:**
            * Pobierz listę osób z `osoby_zaangazowane` z `llm_report_response`.
            * Dla każdej osoby z raportu, iteruj przez `all_facts_data`.
            * Użyj `fuzzywuzzy.fuzz.ratio()` z `FUZZY_MATCH_THRESHOLD` do dopasowania nazwisk.
            * Jeśli dopasowanie jest wystarczające, dodaj `slowa_kluczowe` z pasującego faktu do `current_report_keywords`. Dodaj również `ogolne_slowa_kluczowe` z pliku faktów, jeśli są istotne.
        * **Lematyzacja, deduplikacja i uogólnienia:**
            * Przekształć `current_report_keywords` na listę.
            * Wywołaj `lemmatize_keywords(keywords_list, nlp)`.
            * Wywołaj `apply_generalizations(lemmatized_keywords)`.
            * Przekształć wynik na `set` w celu deduplikacji, a następnie posortuj i przekształć z powrotem na listę.
            * Połącz finalne słowa kluczowe w string oddzielony przecinkami.
        * Dodaj wpis do `final_report_keywords` w formacie `{"nazwa-pliku.txt": "slowo1,slowo2"}`.
    * **Zapisz `final_report_keywords` do `raports.json`** w `OUTPUT_DIR`.

4. **Budowanie odpowiedzi JSON (Krok 5 z planu):**
    * Sprawdź, czy `final_report_keywords` zawiera dokładnie 10 wpisów. Zaloguj błąd, jeśli nie.
    * Skonstruuj finalny obiekt JSON zgodnie z wymaganym formatem:

        ```json
        {
        "task": "dokumenty",
        "apikey": "YOUR_API_KEY",
        "answer": {
            "2024-11-12_report-00-sektor_C4.txt": "słowo,kluczowe,przykład1",
            // ... pozostałe 9 raportów
        }
        }
        ```

    * **Zapisz ten finalny obiekt JSON do `response.json`** w `OUTPUT_DIR`.

5. **Wysyłanie rozwiązania (Krok 6 z planu):**
    * Wywołaj `send_report()` z `sfa_bbs.py`, przekazując finalny obiekt JSON.
    * Sprawdź odpowiedź z `send_report()`.

6. **Sprawdzenie flagi:**
    * Wywołaj `check_for_flag()` z `sfa_bbs.py` na odpowiedzi z `send_report()`.
    * Jeśli flaga zostanie znaleziona, wypisz ją do konsoli i zakończ program.
    * W przeciwnym razie, wypisz pełną odpowiedź z centrali.

## 4. Testowanie i Debugowanie

* **Testy jednostkowe/manualne:**
  * Testuj każdą funkcję pomocniczą oddzielnie (np. `extract_keywords_from_filename`, `lemmatize_keywords`, `apply_generalizations`).
  * Sprawdź poprawność parsowania JSON z LLM.
  * Zweryfikuj działanie `fuzzywuzzy` dla różnych wariacji nazwisk.
* **Logowanie:** Używaj rozbudowanego logowania (`logger.info`, `logger.error`, `logger.debug`) do śledzenia przepływu danych i identyfikacji problemów.
* **Pliki cache:** Wykorzystaj pliki `llm_cache/` do inspekcji odpowiedzi LLM i debugowania.
* **Odpowiedzi centrali:** Dokładnie analizuj komunikaty błędów zwracane przez centralę – są one kluczowe do dopracowania słów kluczowych.

## 5. Wymagania Niefunkcjonalne

* **Wydajność:** Buforowanie odpowiedzi LLM znacząco zredukuje liczbę zapytań do API, co poprawi wydajność i zminimalizuje koszty.
* **Niezawodność:** Retry logic dla zapytań LLM i API, obsługa błędów parsowania JSON.
* **Czytelność kodu:** Przestrzeganie zasad DRY, KISS, YAGNI, SOLID poprzez modularną strukturę i jasne nazewnictwo funkcji/zmiennych.
* **Bezpieczeństwo:** Użycie zmiennych środowiskowych dla kluczy API.

Ten plan stanowi kompleksowy przewodnik do implementacji agenta.
