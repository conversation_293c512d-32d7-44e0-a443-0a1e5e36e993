import requests
import re
import os
from openai import OpenAI

# -----------------------------------------------------------------------------
# USTAWIENIA - WYPEŁNIJ TE WARTOŚCI
# -----------------------------------------------------------------------------

# Dane logowania do systemu robotów
ROBOT_LOGIN_URL = "https://xyz.ag3nts.org/" # Zostaw, jeśli to jest poprawny URL
USERNAME = "tester"
PASSWORD = "574e112a"

# Ustawienia API OpenAI
# WAŻNE: Ustaw swój klucz API OpenAI jako zmienną środowiskową OPENAI_API_KEY
# lub wpisz go bezpośrednio poniżej (mniej bezpie<PERSON>).
# Przykład: OPENAI_API_KEY = "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
OPENAI_API_KEY = os.getenv("GEMINI_API_KEY")

# Opcjonalnie: jeśli używasz niestandardowego endpointu OpenAI (np. proxy, lokalny serwer)
# Jeśli używasz standardowego API OpenAI, możesz zostawić None lub usunąć/zakomentować.
OPENAI_BASE_URL = os.getenv("GEMINI_API_BASE_URL") # np. "http://localhost:8000/v1" 

# Model LLM do użycia (upewnij się, że masz do niego dostęp przez API)
# "gpt-4.1-nano" może być hipotetyczny, użyj dostępnego modelu jak "gpt-3.5-turbo" lub "gpt-4o-mini"
LLM_MODEL = "gemini-2.5-flash-preview-04-17" 

# URL centrali do zgłoszenia flagi (na razie tylko do informacji, skrypt nie wysyła flagi)
CENTRAL_URL = "https://c3ntrala.ag3nts.org/"

# Wyrażenia regularne (mogą wymagać dostosowania do faktycznej struktury strony)
# Zakładamy, że pytanie jest wewnątrz <label for='answer'>...</label>
QUESTION_REGEX = r"<p id=\"human-question\">Question:<br\s*/?>\s*(.*?)\s*</p>"
# Zakładamy, że flaga ma format FLAG{...} lub jest główną treścią tajnej strony
FLAG_REGEX = r"\{\{FLG:.*?\}\}" # Bardziej konkretny wzorzec flagi
# Alternatywnie, jeśli URL tajnej strony jest w tekście odpowiedzi po POST:
# SECRET_URL_REGEX = r"https://xyz.ag3nts.org/([a-zA-Z0-9_/.-]+secret[a-zA-Z0-9_/.-]*)" # Przykład

# -----------------------------------------------------------------------------
# FUNKCJE POMOCNICZE
# -----------------------------------------------------------------------------

def get_question_from_login_page(session: requests.Session, url: str) -> str:
    """Pobiera stronę logowania i wyciąga z niej pytanie anty-captcha."""
    print(f"[*] Pobieranie pytania z: {url}")
    try:
        response = session.get(url, timeout=10)
        response.raise_for_status()  # Rzuci wyjątkiem dla błędów HTTP
        html_content = response.text

        match = re.search(QUESTION_REGEX, html_content, re.IGNORECASE | re.DOTALL)
        if match:
            question = match.group(1).strip()
            # Czasem HTML entities mogą być w pytaniu, np. "
            import html
            question = html.unescape(question)
            print(f"[+] Pobrane pytanie: {question}")
            return question
        else:
            print(f"[-] Nie znaleziono pytania na stronie przy użyciu regex: {QUESTION_REGEX}")
            print(f"    Sprawdź HTML strony: {html_content[:500]}...") # Wyświetl fragment HTML do debugowania                
        with open("debug_page_content.html", "w", encoding="utf-8") as f:
            f.write(html_content)
            print("    Pełny HTML został zapisany do pliku debug_page_content.html")
            raise ValueError("Nie udało się wyodrębnić pytania ze strony logowania.")
    except requests.exceptions.RequestException as e:
        print(f"[!] Błąd sieciowy podczas pobierania pytania: {e}")
        raise

def get_answer_from_llm(question: str, api_key: str, base_url: str = None, model: str = LLM_MODEL) -> str:
    """Wysyła pytanie do LLM (OpenAI API) i zwraca odpowiedź."""
    if not api_key:
        raise ValueError("Klucz API OpenAI nie został ustawiony. Ustaw zmienną środowiskową OPENAI_API_KEY lub wpisz klucz w skrypcie.")
    
    print(f"[*] Wysyłanie pytania do LLM ({model}): \"{question}\"")
    try:
        client_params = {"api_key": api_key}
        if base_url:
            client_params["base_url"] = base_url
        
        client = OpenAI(**client_params)

        completion = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": "Odpowiedz na pytanie krótko i precyzyjnie. Twoja odpowiedź będzie użyta w systemie automatycznym."},
                {"role": "user", "content": question}
            ],
            temperature=0.5, # Można dostosować
        )
        answer = completion.choices[0].message.content.strip()
        print(f"[+] Odpowiedź z LLM: {answer}")
        return answer
    except Exception as e: # Ogólny wyjątek, bo biblioteka OpenAI może rzucać różne błędy
        print(f"[!] Błąd podczas komunikacji z API OpenAI: {e}")
        raise

def submit_login_form(session: requests.Session, url: str, username: str, password: str, answer: str) -> requests.Response:
    """Wysyła formularz logowania z danymi i odpowiedzią na pytanie."""
    payload = {
        "username": username,
        "password": password,
        "answer": answer
    }
    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    print(f"[*] Wysyłanie danych logowania do: {url}")
    try:
        response = session.post(url, data=payload, headers=headers, timeout=10, allow_redirects=True) # allow_redirects jest ważne
        response.raise_for_status()
        print(f"[+] Logowanie zakończone statusem: {response.status_code}")
        # Jeśli serwer przekierowuje, response.url będzie nowym adresem
        if response.history: # Sprawdza, czy było przekierowanie
            print(f"    Przekierowano na: {response.url}")
        return response
    except requests.exceptions.RequestException as e:
        print(f"[!] Błąd sieciowy podczas wysyłania formularza logowania: {e}")
        raise

def get_flag_from_secret_page(session: requests.Session, response_after_login: requests.Response, login_url: str) -> str:
    """
    Odwiedza tajną podstronę (na podstawie odpowiedzi po logowaniu) i wyciąga z niej flagę.
    """
    secret_page_url = None

    # Scenariusz 1: Serwer przekierował na tajną stronę
    if response_after_login.history and response_after_login.url != login_url:
        secret_page_url = response_after_login.url
        print(f"[*] Wygląda na to, że zostaliśmy przekierowani na tajną stronę: {secret_page_url}")
    else:
        # Scenariusz 2: URL tajnej strony jest w treści odpowiedzi po udanym POST
        # (Mniej prawdopodobne dla dobrze zaprojektowanych systemów, ale możliwe)
        # Możesz potrzebować bardziej zaawansowanego regexu lub parsowania HTML tutaj
        # Np. SECRET_URL_REGEX = r'href="(/tajna_sciezka.*?)"'
        # match = re.search(SECRET_URL_REGEX, response_after_login.text)
        # if match:
        #     path = match.group(1)
        #     from urllib.parse import urljoin
        #     secret_page_url = urljoin(login_url, path) # Tworzy pełny URL
        #     print(f"[*] Znaleziono potencjalny URL tajnej strony w treści: {secret_page_url}")
        # else:
        #     print(f"[-] Nie wykryto przekierowania ani nie znaleziono URL-a tajnej strony w odpowiedzi.")
        #     print(f"    Treść odpowiedzi po POST (fragment): {response_after_login.text[:500]}...")
        #     raise ValueError("Nie udało się ustalić adresu tajnej podstrony.")
        
        # Uproszczenie z zadania: "w odpowiedzi dostaniesz adres tajnej podstrony – odwiedź ją"
        # Może to oznaczać, że URL jest po prostu tekstem w odpowiedzi.
        # Spróbujmy znaleźć jakikolwiek URL prowadzący do tej samej domeny, który nie jest stroną logowania.
        # To bardzo ogólne, może wymagać dostosowania.
        potential_urls = re.findall(r'https?://xyz\.ag3nts\.org/[a-zA-Z0-9_/.-]*', response_after_login.text)
        for url in potential_urls:
            if url != login_url and "logout" not in url: # Prosty filtr
                secret_page_url = url
                print(f"[*] Znaleziono potencjalny URL tajnej strony w treści odpowiedzi: {secret_page_url}")
                break
        if not secret_page_url:
            print(f"[-] Nie wykryto przekierowania ani nie znaleziono URL-a tajnej strony w odpowiedzi.")
            print(f"    Treść odpowiedzi po POST (fragment): {response_after_login.text[:500]}...")
            raise ValueError("Nie udało się ustalić adresu tajnej podstrony.")


    print(f"[*] Odwiedzanie tajnej strony: {secret_page_url}")
    try:
        response = session.get(secret_page_url, timeout=10)
        response.raise_for_status()
        secret_content = response.text
        print(f"[+] Pomyślnie pobrano zawartość tajnej strony.")
        # print(f"    Treść tajnej strony (fragment): {secret_content[:500]}...") # Do debugowania

        flag_match = re.search(FLAG_REGEX, secret_content)
        if flag_match:
            flag = flag_match.group(0)
            print(f"[SUCCESS] Znaleziono flagę: {flag}")
            return flag
        else:
            # Jeśli regex nie znajdzie, zwróć całą treść - może flaga nie ma standardowego formatu
            print(f"[-] Nie znaleziono flagi o formacie {FLAG_REGEX} na tajnej stronie.")
            print(f"    Zwracam całą zawartość strony jako potencjalną flagę.")
            return secret_content.strip() # Zwróć całą treść jako fallback

    except requests.exceptions.RequestException as e:
        print(f"[!] Błąd sieciowy podczas odwiedzania tajnej strony: {e}")
        raise
    except Exception as e:
        print(f"[!] Inny błąd podczas przetwarzania tajnej strony: {e}")
        raise

# -----------------------------------------------------------------------------
# GŁÓWNY SKRYPT
# -----------------------------------------------------------------------------

def main():
    print("--- Rozpoczynanie pracy agenta ---")

    if not OPENAI_API_KEY:
        print("[CRITICAL] Klucz API OpenAI (OPENAI_API_KEY) nie jest skonfigurowany. Zakończono.")
        return

    # Użyj sesji, aby zachować ciasteczka między żądaniami
    with requests.Session() as session:
        try:
            # 1. Pobierz pytanie
            #   Pierwsze żądanie GET może być potrzebne do zainicjowania sesji/ciasteczek
            #   oraz do pobrania samej strony z pytaniem.
            question = get_question_from_login_page(session, ROBOT_LOGIN_URL)

            # 2. Uzyskaj odpowiedź z LLM
            llm_answer = get_answer_from_llm(question, OPENAI_API_KEY, OPENAI_BASE_URL, LLM_MODEL)

            # 3. Wyślij formularz logowania
            response_after_login = submit_login_form(session, ROBOT_LOGIN_URL, USERNAME, PASSWORD, llm_answer)

            # 4. Odwiedź tajną stronę i zdobądź flagę
            #    response_after_login zawiera odpowiedź z serwera po próbie logowania
            flag = get_flag_from_secret_page(session, response_after_login, ROBOT_LOGIN_URL)

            print("\n------------------------------------")
            print(f" 최종 플래그 (Final Flag): {flag}") # Zmienione na koreański dla zabawy, zgodnie z duchem "ag3nts"
            print("------------------------------------")
            print(f"\n[*] Flaga powinna zostać zgłoszona do centrali: {CENTRAL_URL}")
            print("--- Agent zakończył pracę ---")

        except ValueError as e:
            print(f"[ERROR] Błąd wartości: {e}")
            print("--- Agent zakończył pracę z błędem ---")
        except requests.exceptions.HTTPError as e:
            print(f"[ERROR] Błąd HTTP: {e.response.status_code} - {e.response.reason}")
            print(f"    Treść odpowiedzi: {e.response.text[:500]}...")
            print("--- Agent zakończył pracę z błędem ---")
        except requests.exceptions.RequestException as e:
            print(f"[ERROR] Błąd połączenia/sieci: {e}")
            print("--- Agent zakończył pracę z błędem ---")
        except Exception as e:
            print(f"[ERROR] Niespodziewany błąd: {e}")
            import traceback
            traceback.print_exc()
            print("--- Agent zakończył pracę z błędem ---")

if __name__ == "__main__":
    main()
