"""
Data Censorship Agent - S01E05

Skrypt automatycznie pobiera dane, cen<PERSON><PERSON><PERSON> je przy użyciu LLM i wysyła raport.
Implementacja zgodna z wymaganiami zadania S01E05 i planem implementacji.

Autor: Augment Agent
Data: 2025-05-19
"""

# --- IMPORTY ---

import os
import re
import json
import logging
import requests
from typing import Dict, Optional
from dotenv import load_dotenv
from openai import OpenAI, APIError, APIConnectionError, RateLimitError

# --- KONFIGURACJA LOGOWANIA ---

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# --- ŁADOWANIE ZMIENNYCH ŚRODOWISKOWYCH ---

load_dotenv()

# --- USTAWIENIA APLIKACJI ---

DATA_URL_TEMPLATE = "https://c3ntrala.ag3nts.org/data/{}/cenzura.txt"
REPORT_URL = "https://c3ntrala.ag3nts.org/report"
TASK_NAME = "CENZURA"

# Nazwy zmiennych środowiskowych dla kluczy API
AIDEVS_API_KEY_ENV_VAR = "AIDEVS_API_KEY"
LLM_API_KEY_ENV_VAR = "KLUSTER_API_KEY" # Zgodnie z S01E03

# --- USTAWIENIA LLM ---

LLM_MODEL = "deepseek-ai/DeepSeek-V3-0324" # Zgodnie z S01E03, można zmienić na inny model zgodny z OpenAI API
LLM_BASE_URL = "https://api.kluster.ai/v1" # Zgodnie z S01E03
SYSTEM_PROMPT_FILE = "adw/S01E05/system_prompt.txt" # Ścieżka do pliku z system promptem

# Regex do ekstrakcji flagi w formacie {{FLG:...}}
FLAG_REGEX = r"\{\{FLG:([^}]+)\}\}"

# --- FUNKCJE POMOCNICZE ---

def get_api_key(env_var_name: str) -> str:
    """
    Pobiera klucz API ze zmiennej środowiskowej.

    Args:
        env_var_name: Nazwa zmiennej środowiskowej.

    Returns:
        str: Klucz API.

    Raises:
        ValueError: Jeśli zmienna środowiskowa nie jest ustawiona.
    """
    api_key = os.getenv(env_var_name)
    if not api_key:
        logger.error(f"Brak klucza API. Ustaw zmienną środowiskową {env_var_name}.")
        raise ValueError(f"Zmienna środowiskowa {env_var_name} nie jest ustawiona.")
    return api_key

def load_system_prompt(file_path: str) -> str:
    """
    Wczytuje system prompt z zewnętrznego pliku.

    Args:
        file_path: Ścieżka do pliku z promptem.

    Returns:
        str: Zawartość pliku promptu.

    Raises:
        FileNotFoundError: Jeśli plik nie istnieje.
        IOError: Jeśli wystąpi błąd podczas odczytu pliku.
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            system_prompt = f.read()
        logger.info(f"Wczytano system prompt z pliku: {file_path}")
        return system_prompt
    except FileNotFoundError:
        logger.error(f"Plik system promptu nie znaleziony: {file_path}")
        raise
    except IOError as e:
        logger.error(f"Błąd podczas odczytu pliku system promptu {file_path}: {e}")
        raise

def download_data(url: str) -> Optional[str]:
    """
    Pobiera zawartość pliku tekstowego z podanego URL.

    Args:
        url: Adres URL pliku.

    Returns:
        Optional[str]: Zawartość pliku jako string lub None w przypadku błędu.
    """
    logger.info(f"Pobieranie danych z: {url}")
    try:
        response = requests.get(url)
        response.raise_for_status() # Rzuci wyjątek dla kodów 4xx/5xx
        logger.info("Dane pobrane pomyślnie.")
        return response.text
    except requests.exceptions.RequestException as e:
        logger.error(f"Błąd podczas pobierania danych z {url}: {e}")
        return None

def censor_data_with_llm(text: str, system_prompt: str, llm_api_key: str, llm_base_url: str, llm_model: str) -> Optional[str]:
    """
    Wysyła tekst do LLM w celu cenzury i zwraca ocenzurowany tekst.

    Args:
        text: Tekst do ocenzurowania.
        system_prompt: System prompt dla LLM.
        llm_api_key: Klucz API dla LLM.
        llm_base_url: Adres bazowy API LLM.
        llm_model: Nazwa modelu LLM.

    Returns:
        Optional[str]: Ocenzurowany tekst lub None w przypadku błędu.
    """
    logger.info("Wysyłanie tekstu do LLM w celu cenzury...")
    try:
        client = OpenAI(
            api_key=llm_api_key,
            base_url=llm_base_url
        )

        completion = client.chat.completions.create(
            model=llm_model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": text}
            ],
            temperature=0.1, # Niska temperatura dla deterministycznych odpowiedzi
            max_tokens=2000 # Ograniczenie, aby uniknąć przekroczenia limitu
        )

        censored_text = completion.choices[0].message.content.strip()
        logger.info("Tekst ocenzurowany przez LLM.")
        return censored_text

    except (APIError, APIConnectionError, RateLimitError) as e:
        logger.error(f"Błąd API LLM podczas cenzury: {e}")
        return None
    except Exception as e:
        logger.error(f"Nieoczekiwany błąd podczas cenzury przez LLM: {e}")
        return None

def send_report(censored_text: str, api_key: str, report_url: str, task_name: str) -> Optional[Dict]:
    """
    Wysyła ocenzurowany tekst do API raportującego.

    Args:
        censored_text: Ocenzurowany tekst do wysłania.
        api_key: Klucz API AIDEVS.
        report_url: Adres URL API raportującego.
        task_name: Nazwa zadania.

    Returns:
        Optional[Dict]: Odpowiedź API w formie słownika lub None w przypadku błędu.
    """
    logger.info(f"Wysyłanie raportu do: {report_url}")
    payload = {
        "task": task_name,
        "apikey": api_key,
        "answer": censored_text
    }
    headers = {"Content-Type": "application/json"}

    try:
        response = requests.post(report_url, json=payload, headers=headers)
        response.raise_for_status() # Rzuci wyjątek dla kodów 4xx/5xx

        result = response.json()
        logger.info(f"Raport wysłany pomyślnie. Odpowiedź API: {result}")
        return result
    except requests.exceptions.RequestException as e:
        logger.error(f"Błąd podczas wysyłania raportu do {report_url}: {e}")
        if hasattr(e, 'response') and hasattr(e.response, 'text'):
             logger.error(f"Odpowiedź serwera: {e.response.text}")
        return None
    except json.JSONDecodeError:
        logger.error(f"Błąd dekodowania JSON z odpowiedzi API: {response.text}")
        return None
    except Exception as e:
        logger.error(f"Nieoczekiwany błąd podczas wysyłania raportu: {e}")
        return None


def extract_flag(response_data: Dict) -> Optional[str]:
    """
    Przeszukuje odpowiedź API w poszukiwaniu flagi w formacie {{FLG:...}}.

    Args:
        response_data: Odpowiedź API w formie słownika.

    Returns:
        Optional[str]: Znaleziona flaga lub None.
    """
    if not isinstance(response_data, dict):
        logger.warning("Odpowiedź API nie jest słownikiem, nie można przeszukać w poszukiwaniu flagi.")
        return None

    # Przeszukaj wartości w słowniku
    for key, value in response_data.items():
        if isinstance(value, str):
            match = re.search(FLAG_REGEX, value)
            if match:
                # Zwróć cały znaleziony wzorzec {{FLG:...}}
                return match.group(0)
        # Rekurencyjnie przeszukaj zagnieżdżone słowniki
        elif isinstance(value, dict):
            flag = extract_flag(value)
            if flag:
                return flag
        # Rekurencyjnie przeszukaj elementy list (jeśli lista zawiera słowniki lub stringi)
        elif isinstance(value, list):
            for item in value:
                if isinstance(item, (str, dict)):
                    flag = extract_flag({"_": item}) # Pakujemy element w tymczasowy słownik
                    if flag:
                        return flag

    return None

# --- GŁÓWNA FUNKCJA ---

def main():
    """
    Główna funkcja programu SFA.
    """
    logger.info("--- Rozpoczynanie pracy agenta S01E05 - Cenzura Danych ---")

    try:
        # 1. Pobranie kluczy API
        aidevs_api_key = get_api_key(AIDEVS_API_KEY_ENV_VAR)
        llm_api_key = get_api_key(LLM_API_KEY_ENV_VAR)

        # 2. Wczytanie system promptu
        system_prompt = load_system_prompt(SYSTEM_PROMPT_FILE)

        # 3. Zbudowanie URL do pobrania danych
        data_url = DATA_URL_TEMPLATE.format(aidevs_api_key)

        # 4. Pobranie danych
        data_to_censor = download_data(data_url)

        if data_to_censor:
            # 5. Cenzura danych przy użyciu LLM
            censored_data = censor_data_with_llm(data_to_censor, system_prompt, llm_api_key, LLM_BASE_URL, LLM_MODEL)

            if censored_data is not None:
                # 6. Wysyłanie raportu
                report_response = send_report(censored_data, aidevs_api_key, REPORT_URL, TASK_NAME)

                if report_response:
                    # 7. Sprawdzanie flagi w odpowiedzi
                    flag = extract_flag(report_response)

                    if flag:
                        logger.info(f"ZNALEZIONO FLAGĘ: {flag}")
                        print(f"\n{'='*50}\nFLAGA: {flag}\n{'='*50}\n")
                    else:
                        logger.info("Nie znaleziono flagi w odpowiedzi API.")
                        print(f"\n{'='*50}\nODPOWIEDŹ API (bez flagi): {json.dumps(report_response, indent=2)}\n{'='*50}\n")
                else:
                    logger.error("Nie udało się wysłać raportu lub otrzymać poprawnej odpowiedzi API.")
            else:
                logger.error("Nie udało się ocenzurować danych przy użyciu LLM.")
        else:
            logger.error("Nie udało się pobrać danych do cenzury.")

    except (ValueError, FileNotFoundError, IOError) as e:
        logger.critical(f"Błąd konfiguracji lub odczytu pliku: {e}")
    except Exception as e:
        logger.critical(f"Wystąpił nieoczekiwany błąd podczas wykonywania programu: {e}", exc_info=True)

    logger.info("--- Zakończenie pracy agenta S01E05 ---")

# --- URUCHOMIENIE SKRYPTU ---

if __name__ == "__main__":
    main()