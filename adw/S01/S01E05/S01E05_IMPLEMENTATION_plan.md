**Plan Implementacji Single File Agent (SFA) dla Zadania S01E05 - Cenzura Danych**

**Nazwa pliku:** `adw/S01E05/S01E05.py` (propozycja, zgodna z konwencją)

**Stos Technologiczny:**
*   Język: Python 3.x
*   Biblioteki:
    *   `requests` - do wykonywania żądań HTTP (GET i POST).
    *   `os` - do odczytu zmiennych środowiskowych.
    *   `json` - do pracy z formatem JSON.
    *   `logging` - do logowania przebiegu działania skryptu.
    *   `re` - do wyszukiwania wzorców (np. flagi).
    *   `dotenv` - do ładowania zmiennych środowiskowych z pliku `.env` (opcjonalne, ale zgodne z S01E03).
    *   `openai` - do komunikacji z API LLM w standardzie OpenAI.

**Struktura Pliku SFA (`adw/S01E05/S01E05.py`):**

1.  **Nagłówek i Importy:**
    *   Komentarz z opisem skryptu, autorem, datą.
    *   Importy wymaganych bibliotek (`os`, `re`, `json`, `logging`, `requests`, `dotenv`, `openai`).

2.  **Konfiguracja Logowania:**
    *   Podstawowa konfiguracja logowania, podobna do tej w `adw/S01E03/S01E03.py`, aby widzieć przebieg działania.

3.  **Ładowanie Zmiennych Środowiskowych:**
    *   Wywołanie `load_dotenv()` (jeśli używamy pliku `.env`).

4.  **Ustawienia Aplikacji i LLM (Konstanty):**
    *   Definicja stałych dla:
        *   URL do pobrania danych (`DATA_URL_TEMPLATE`). Będzie wymagał wstawienia klucza API AIDEVS.
        *   URL do wysłania raportu (`REPORT_URL`).
        *   Nazwa zadania dla API raportującego (`TASK_NAME`, wartość "CENZURA").
        *   Nazwa zmiennej środowiskowej dla klucza API AIDEVS (`AIDEVS_API_KEY_ENV_VAR`).
        *   Nazwa zmiennej środowiskowej dla klucza API LLM (`LLM_API_KEY_ENV_VAR`).
        *   Adres bazowy API LLM (`LLM_BASE_URL`), zgodny z `adw/S01E03/S01E03.py` (`https://api.kluster.ai/v1`).
        *   Nazwa modelu LLM (`LLM_MODEL`), zgodna z `adw/S01E03/S01E03.py` (np. `"deepseek-ai/DeepSeek-V3-0324"` lub inny konfigurowalny model zgodny ze standardem OpenAI API, np. `"gpt-4o-mini"` - warto dodać komentarz o możliwości konfiguracji).
        *   Ścieżka do pliku z system promptem (`SYSTEM_PROMPT_FILE`).
        *   Regex do wyszukiwania flagi (`FLAG_REGEX`, wartość `r"\{\{FLG:([^}]+)\}\}"`).

5.  **Funkcje Pomocnicze:**
    *   `get_api_key(env_var_name: str) -> str`: Funkcja do bezpiecznego pobierania klucza API ze zmiennej środowiskowej i sprawdzania, czy jest ustawiony. Zwraca klucz lub rzuca wyjątek/loguje błąd.
    *   `load_system_prompt(file_path: str) -> str`: Funkcja do wczytywania system promptu z zewnętrznego pliku. Obsługa błędów odczytu pliku.
    *   `download_data(url: str) -> str`: Funkcja do pobierania zawartości pliku tekstowego z podanego URL. Używa biblioteki `requests`. Obsługa błędów sieciowych i HTTP. Zwraca pobrany tekst.
    *   `censor_data_with_llm(text: str, system_prompt: str, llm_api_key: str, llm_base_url: str, llm_model: str) -> str`: Funkcja do wysyłania tekstu do LLM w celu cenzury. Używa biblioteki `openai`. Tworzy instancję klienta OpenAI z podanym `base_url` i `api_key`. Wysyła żądanie `chat.completions.create` z rolami `system` i `user`. Zwraca ocenzurowany tekst z odpowiedzi LLM. Obsługa błędów API LLM.
    *   `send_report(censored_text: str, api_key: str, report_url: str, task_name: str) -> dict`: Funkcja do wysyłania ocenzurowanego tekstu do API raportującego. Tworzy payload JSON zgodnie z wymaganiami (`task`, `apikey`, `answer`). Używa biblioteki `requests` do wysłania żądania POST. Zwraca odpowiedź API w formie słownika Python. Obsługa błędów sieciowych i HTTP.
    *   `extract_flag(response_data: dict) -> Optional[str]`: Funkcja do przeszukiwania odpowiedzi API (słownika) w poszukiwaniu flagi w formacie `{{FLG:...}}`. Używa regexu. Zwraca znalezioną flagę lub `None`.

7.  **Główna Funkcja (`main()`):**
    *   Inicjalizacja logowania.
    *   Pobranie kluczy API AIDEVS i LLM za pomocą `get_api_key`. Jeśli któryś klucz nie jest dostępny, zaloguj błąd i zakończ działanie.
    *   Wczytanie system promptu za pomocą `load_system_prompt`. Jeśli plik nie istnieje lub wystąpi błąd odczytu, zaloguj błąd i zakończ działanie.
    *   Zbudowanie pełnego URL do pobrania danych, wstawiając klucz API AIDEVS do szablonu URL.
    *   Wywołanie `download_data` z przygotowanym URL.
    *   Jeśli pobieranie danych się powiodło:
        *   Wywołanie `censor_data_with_llm` z pobranym tekstem, system promptem, kluczem LLM, base URL i modelem.
        *   Jeśli cenzura się powiodła:
            *   Wywołanie `send_report` z ocenzurowanym tekstem, kluczem API AIDEVS, URL raportu i nazwą zadania.
            *   Jeśli wysyłanie raportu się powiodło:
                *   Wywołanie `extract_flag` na odpowiedzi z API raportującego.
                *   Jeśli flaga została znaleziona, zaloguj i wypisz ją do konsoli.
                *   Jeśli flaga nie została znaleziona, zaloguj informację o tym i wypisz całą odpowiedź API do konsoli w celu debugowania.
            *   Jeśli wysyłanie raportu się nie powiodło, zaloguj błąd.
        *   Jeśli cenzura się nie powiodła, zaloguj błąd.
    *   Jeśli pobieranie danych się nie powiodło, zaloguj błąd.
    *   Zakończenie działania skryptu po jednokrotnym przejściu przez workflow.

8.  **Uruchomienie Skryptu:**
    *   Standardowa konstrukcja `if __name__ == "__main__": main()`.
    *   Dodanie ogólnego bloku `try...except` w `if __name__ == "__main__":` do wyłapywania nieoczekiwanych błędów na najwyższym poziomie i logowania ich.

**Zgodność z Wymaganiami i Dobrymi Praktykami:**

*   **SFA:** Cała funkcjonalność w jednym pliku.
*   **Tech Stack:** Użycie Pythona i wskazanych bibliotek.
*   **Workflow:** Realizacja kroków: pobranie -> cenzura -> raport.
*   **API Keys:** Pobieranie z zmiennych środowiskowych, zgodnie z `adw/S01E03/S01E03.py`.
*   **LLM Config:** Model i base URL konfigurowalne, zgodne z `adw/S01E03/S01E03.py`.
*   **System Prompt:** Wczytywanie z zewnętrznego pliku, zgodnie z `adw/S01E03/S01E03.py`.
*   **OpenAI API Standard:** Użycie biblioteki `openai` do komunikacji z LLM.
*   **Jednokrotne Wykonanie:** Brak pętli iterującej cały workflow.
*   **Poszukiwanie Flagi:** Implementacja funkcji `extract_flag` szukającej wzorca `{{FLG:...}}`.
*   **Najlepsze Praktyki:** Podział na funkcje (DRY), prosta logika w każdej funkcji (KISS), skupienie na wymaganej funkcjonalności (YAGNI), separacja odpowiedzialności (np. pobieranie, cenzura, wysyłanie, ekstrakcja flagi - elementy SOLID). Obsługa błędów w poszczególnych krokach.

**Dodatkowe Pliki:**

*   `system_prompt.txt`: Plik tekstowy zawierający system prompt dla LLM, który przygotowałem wcześniej.

Poniżej znajduje się treść system promptu:

```
Jesteś specjalistycznym Agentem odpowiedzialnym za cenzurowanie danych osobowych w dostarczonym tekście. Twoim zadaniem jest precyzyjne zidentyfikowanie i zastąpienie określonych informacji słowem "CENZURA", przy jednoczesnym zachowaniu oryginalnej struktury i formatowania tekstu.

**Instrukcje dotyczące cenzury:**

1.  **Zidentyfikuj i ocenzuruj następujące typy danych:**
    *   Pełne imię i nazwisko osoby (traktuj jako jedną całość).
    *   Wiek osoby (liczba lat).
    *   Nazwę miasta.
    *   Nazwę ulicy wraz z numerem domu (traktuj jako jedną całość, np. "ul. Szeroka 18").

2.  **Zastąp zidentyfikowane dane wyłącznie słowem "CENZURA".**

3.  **Zachowaj oryginalny format tekstu:**
    *   Nie zmieniaj kolejności słów ani struktury zdania.
    *   Nie usuwaj ani nie dodawaj żadnych znaków interpunkcyjnych (kropki, przecinki, dwukropki itp.) ani spacji, które nie są częścią cenzurowanych danych.
    *   Tekst po cenzurze musi wyglądać identycznie jak tekst oryginalny, z tą różnicą, że wskazane dane osobowe są zastąpione słowem "CENZURA".

**Przykład cenzury:**

*   Tekst oryginalny: "Osoba podejrzana to Jan Nowak. Adres: Wrocław, ul. Szeroka 18. Wiek: 32 lata."
*   Tekst po cenzurze: "Osoba podejrzana to CENZURA. Adres: CENZURA, ul. CENZURA. Wiek: CENZURA lata."

**Częste błędy, których należy unikać:**

*   Nie cenzuruj imienia i nazwiska oddzielnie (np. "CENZURA CENZURA"). Zawsze cenzuruj jako jedną całość ("CENZURA").
*   Nie cenzuruj ulicy i numeru domu oddzielnie (np. "ul. CENZURA CENZURA"). Zawsze cenzuruj jako jedną całość ("ul. CENZURA").

Twoja odpowiedź powinna zawierać wyłącznie ocenzurowany tekst. Nie dodawaj żadnych dodatkowych komentarzy ani wyjaśnień.
```