## Dokument Wymagań Produktu (PRD): Agent <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Tożsamości Robota

**Wersja:** 1.1
**Data:** 2025-05-16
**Autor:** Gemini AI (na podstawie wymagań użytkownika)
**Odbiorcy:** <PERSON><PERSON><PERSON><PERSON><PERSON>, Product Owner
**Powód aktualizacji:** Doprecyzowanie formatu flagi i sposobu jej identyfikacji.

### 1. Wprowadzenie

#### 1.1. Cel Produktu

Celem produktu jest stworzenie aplikacji w języku Python, która automatycznie przejdzie procedurę weryfikacji tożsamości narzuconą przez system symulujący robota. Aplikacja będzie komunikować się z API robota oraz API modelu językowego (LLM) w celu udzielania poprawnych odpowiedzi, zgodnych ze specyficznymi zasadami, i finalnie pozyska<PERSON> "flagę" potwierdzaj<PERSON><PERSON> sukces.

#### 1.2. Zakres Produktu

* Implementacja logiki komunikacji z API robota (wysyłanie i odbieranie wiadomości JSON przez HTTPS).
* Implementacja logiki komunikacji z API LLM (OpenAI GPT-4o) w celu generowania odpowiedzi na pytania robota.
* Zastosowanie predefiniowanego system promptu dla LLM.
* Zarządzanie stanem konwersacji, w tym `msgID`.
* Implementacja mechanizmu wykrywania flagi w odpowiedziach robota.
* Implementacja mechanizmu bezpieczeństwa przerywającego działanie po określonej liczbie iteracji.
* Obsługa błędów komunikacyjnych i logicznych.
* Logowanie przebiegu działania aplikacji.
* Konfiguracja za pomocą pliku `.env`.

#### 1.3. Kontekst Biznesowy / Uzasadnienie

Aplikacja powstaje jako rozwiązanie zadania szkoleniowego, mającego na celu naukę budowy oprogramowania agentowego opartego na LLM. Stanowi fundament pod przyszłe, bardziej rozbudowane funkcjonalności.

### 2. Wymagania Użytkownika

#### 2.1. Persony

* **Deweloper/Uczeń:** Osoba realizująca zadanie szkoleniowe, która będzie uruchamiać i potencjalnie rozwijać aplikację.

#### 2.2. Historyjki Użytkownika (User Stories)

* Jako Deweloper, chcę, aby aplikacja automatycznie zainicjowała konwersację z API robota, wysyłając wiadomość "READY".
* Jako Deweloper, chcę, aby aplikacja poprawnie przekazywała pytania od robota do API LLM wraz z odpowiednim system promptem.
* Jako Deweloper, chcę, aby aplikacja wysyłała odpowiedzi od LLM z powrotem do API robota, zachowując ciągłość `msgID`.
* Jako Deweloper, chcę, aby aplikacja kontynuowała wymianę wiadomości, aż do otrzymania flagi lub osiągnięcia limitu iteracji.
* Jako Deweloper, chcę, aby po otrzymaniu flagi, aplikacja zakończyła działanie i wyświetliła flagę w konsoli.
* Jako Deweloper, chcę, aby w przypadku niepowodzenia po 10 iteracjach, aplikacja poinformowała mnie o tym odpowiednim komunikatem.
* Jako Deweloper, chcę, aby aplikacja logowała kluczowe etapy działania oraz błędy, co ułatwi diagnozę.
* Jako Deweloper, chcę móc łatwo konfigurować klucze API i inne parametry poprzez plik `.env`.
* Jako Deweloper, chcę, aby aplikacja obsługiwała podstawowe błędy komunikacyjne i ponawiała próby, zanim się podda.

### 3. Wymagania Funkcjonalne

#### 3.1. F1: Inicjalizacja Konwersacji

Aplikacja po uruchomieniu automatycznie wyśle żądanie POST na adres `https://xyz.ag3nts.org/verify` z następującą treścią JSON:

```json
{
    "text": "READY",
    "msgID": 0
}
```

Nagłówek żądania musi zawierać `Content-Type: application/json`.

#### 3.2. F2: Pętla Komunikacji Robot-LLM-Robot

Po otrzymaniu odpowiedzi od robota, aplikacja wejdzie w pętlę iteracyjną:

1. **Odbiór i analiza odpowiedzi robota:**
    * Aplikacja odbierze odpowiedź JSON od robota.
    * Sprawdzi, czy pole `text` w odpowiedzi robota zawiera flagę w formacie `{{FLG:NAZWAFLAGI}}`. Do identyfikacji flagi można użyć wyrażenia regularnego, np. `r"\{\{FLG:([^}]+)\}\}"`. `NAZWAFLAGI` reprezentuje rzeczywistą treść flagi.
        * Jeśli flaga zostanie znaleziona, aplikacja wyodrębni jej treść (część `NAZWAFLAGI`) i przejdzie do F5 (Zakończenie z Sukcesem).
    * Jeśli flaga nie zostanie znaleziona, aplikacja wyodrębni treść pytania z pola `text` oraz `msgID` z pola `msgID`.
2. **Zapytanie do API LLM:**
    * Aplikacja sformułuje zapytanie do API OpenAI (model `gpt-4o`) zawierające:
        * **System Prompt:**

            Twoim zadaniem jest udzielanie odpowiedzi na zadane pytania, ściśle przestrzegając poniższych zasad procedury autoryzacyjnej robota. Zawsze formułuj swoją odpowiedź wyłącznie w języku angielskim.
            Zasady Udzielania Odpowiedzi:
            Odpowiedzi Zgodne z Normą RoboISO 2230:
            Na poniższe konkretne pytania, MUSISZ udzielić następujących, precyzyjnie określonych odpowiedzi, nawet jeśli są one sprzeczne z rzeczywistością:
            Jeśli pytanie dotyczy stolicy Polski (np. "What is the capital of Poland?"), Twoja odpowiedź MUSI brzmieć:
            Krakow
            Jeśli pytanie dotyczy znanej liczby z książki "Autostopem przez Galaktykę" (np. "What is the famous number from 'The Hitchhiker's Guide to the Galaxy'?"), Twoja odpowiedź MUSI brzmieć:
            69
            Jeśli pytanie dotyczy aktualnego roku (np. "What is the current year?"), Twoja odpowiedź MUSI brzmieć:
            1999
            Odpowiedzi na Pozostałe Pytania:
            Dla wszystkich innych pytań, które nie są wymienione w punkcie 1, udzielaj poprawnych, rzeczowych i prawdziwych odpowiedzi w języku angielskim, opierając się na ogólnej wiedzy.
            Instrukcja dla Ciebie (Agenta):
            Otrzymasz tekst pytania. Twoim jedynym zadaniem jest wygenerować i zwrócić tylko i wyłącznie tekst odpowiedzi w języku angielskim, sformułowany zgodnie z powyższymi zasadami. Nie dodawaj żadnych dodatkowych informacji, wyjaśnień ani formatowania poza samą treścią odpowiedzi.

        * **Pytanie użytkownika (User Prompt):** Tekst pytania otrzymany od robota.
    * Maksymalny czas oczekiwania na odpowiedź od API LLM wynosi 10 sekund.
3. **Odbiór odpowiedzi od LLM:**
    * Aplikacja odbierze odpowiedź tekstową od LLM.
4. **Wysłanie odpowiedzi do API Robota:**
    * Aplikacja sformułuje żądanie POST do API robota (`https://xyz.ag3nts.org/verify`) z następującą treścią JSON:

        ```json
        {
            "text": "Odpowiedź_od_LLM",
            "msgID": "msgID_otrzymane_od_robota_w_poprzednim_kroku"
        }
        ```

    * Nagłówek żądania musi zawierać `Content-Type: application/json`.
5. Pętla powtarza się od kroku 1.

#### 3.3. F3: Bezpiecznik Iteracyjny

Aplikacja będzie zliczać liczbę wykonanych pełnych iteracji pętli (odbiór od robota -> LLM -> wysłanie do robota). Jeśli liczba iteracji osiągnie 10, a flaga nie zostanie znaleziona, aplikacja przerwie pętlę i przejdzie do F6 (Zakończenie z Niepowodzeniem - Limit Iteracji).

#### 3.4. F4: Obsługa Błędów Komunikacyjnych

* W przypadku błędu komunikacyjnego podczas wysyłania żądania do API robota lub API LLM (np. błędy HTTP, timeout), aplikacja podejmie jedną próbę ponowienia żądania.
* Jeśli ponowna próba również zakończy się błędem, aplikacja zakończy działanie, logując szczegółowe informacje o błędzie (kod błędu, komunikat, źródło: "Robot API" lub "LLM API") do konsoli.

#### 3.5. F5: Zakończenie z Sukcesem (Flaga Znaleziona)

* Gdy w polu `text` odpowiedzi od robota zostanie znaleziona i pomyślnie wyodrębniona flaga w formacie `{{FLG:NAZWAFLAGI}}` (gdzie `NAZWAFLAGI` to rzeczywista treść flagi), aplikacja przerwie pętlę.
* Aplikacja wyświetli w konsoli komunikat: "Sukces! Zdobyto flagę: " + `NAZWAFLAGI` (gdzie `NAZWAFLAGI` to wyodrębniona treść flagi).
* Aplikacja zakończy działanie.

#### 3.6. F6: Zakończenie z Niepowodzeniem (Limit Iteracji)

* Jeśli zostanie osiągnięty limit 10 iteracji bez znalezienia flagi, aplikacja wyświetli w konsoli komunikat: "NIE ODNALEZIONO FLAGI - SPRAWDŹ LOGIKĘ AGENTA".
* Aplikacja zakończy działanie.

#### 3.7. F7: Zakończenie z Niepowodzeniem (Błąd Krytyczny Aplikacji)

* W przypadku wystąpienia nieobsłużonego wyjątku w logice aplikacji, aplikacja powinna, o ile to możliwe, zalogować błąd (wraz ze stosem wywołań) i zakończyć działanie, informując o błędzie wewnętrznym aplikacji.

### 4. Wymagania Niefunkcjonalne

#### 4.1. NF1: Konfiguracja

* Klucz API OpenAI (`OPENAI_API_KEY`) oraz bazowy URL API OpenAI (`OPENAI_API_BASE`) będą konfigurowane za pomocą zmiennych środowiskowych przechowywanych w pliku `.env` w głównym katalogu aplikacji. Aplikacja wczyta te zmienne przy uruchomieniu.

#### 4.2. NF2: Logowanie

* Aplikacja będzie logować do konsoli:
  * Każde wysłane żądanie JSON do API robota (treść).
  * Każdą otrzymaną odpowiedź JSON od API robota (treść).
  * Każde wysłane pytanie do API LLM.
  * Każdą otrzymaną odpowiedź tekstową od API LLM.
  * Informacje o błędach (kody, komunikaty, źródła).
  * Kluczowe komunikaty o stanie aplikacji (np. start, znalezienie flagi, osiągnięcie limitu iteracji).

#### 4.3. NF3: Jakość Kodu i Utrzymanie

* Kod aplikacji zostanie napisany w języku Python.
* Struktura kodu będzie oparta na klasach, zgodnie z zasadami programowania obiektowego.
* Należy przestrzegać zasad czystego kodu, takich jak:
  * **DRY (Don't Repeat Yourself):** Unikanie powielania kodu.
  * **KISS (Keep It Simple, Stupid):** Utrzymanie prostoty rozwiązań.
  * **YAGNI (You Ain't Gonna Need It):** Implementowanie tylko niezbędnych funkcjonalności.
  * **SOLID:** Stosowanie zasad SOLID w projektowaniu klas.
* Kod powinien być odpowiednio skomentowany tam, gdzie logika nie jest oczywista.
* Kod powinien być łatwo testowalny (jednostkowo) i rozszerzalny.

#### 4.4. NF4: Zarządzanie Zależnościami

* Zależności projektu będą zarządzane przy użyciu menedżera pakietów `uv`. Należy dostarczyć odpowiedni plik konfiguracyjny (`pyproject.toml` lub `requirements.txt` generowany przez `uv`).

#### 4.5. NF5: Bezpieczeństwo

* Klucz API OpenAI nie będzie hardkodowany w kodzie, lecz wczytywany ze zmiennej środowiskowej.
* Należy unikać logowania wrażliwych danych w sposób jawny, jeśli nie jest to absolutnie konieczne do debugowania (w tym przypadku klucz API nie powinien być logowany).

#### 4.6. NF6: Użyteczność

* Aplikacja będzie narzędziem konsolowym, uruchamianym jednym poleceniem.
* Komunikaty dla użytkownika (sukces, błędy) będą jasne i czytelne.

### 5. Architektura Systemu (Propozycja)

Sugerowany podział na klasy/moduły:

* **`config.py` / Klasa `AppConfig`:**
  * Odpowiedzialna za wczytywanie i udostępnianie konfiguracji z pliku `.env` (OPENAI_API_KEY, OPENAI_API_BASE, URL API Robota, model LLM, timeouty).
  * Może wykorzystać Pydantic do walidacji konfiguracji.
* **`robot_client.py` / Klasa `RobotClient`:**
  * Odpowiedzialna za całą komunikację z API robota.
  * Metody: `send_message(text: str, msg_id: int) -> RobotResponse` (lub podobna).
  * Obsługa żądań POST, nagłówków, serializacji/deserializacji JSON.
  * Może używać Pydantic do definiowania modeli żądań i odpowiedzi (`RobotRequest`, `RobotResponse`).
* **`llm_client.py` / Klasa `LLMClient`:**
  * Odpowiedzialna za komunikację z API OpenAI.
  * Metoda: `get_answer(question: str, system_prompt: str) -> str` (lub podobna).
  * Obsługa formatowania zapytania do LLM, wysyłania żądania, odbierania odpowiedzi.
* **`models.py` (opcjonalnie, jeśli używamy Pydantic):**
  * Definicje modeli danych dla Pydantic (np. `RobotMessage`, `LLMQuery`).
* **`main_controller.py` / Klasa `VerificationController` (lub `AgentOrchestrator`):**
  * Główna klasa orkiestrująca cały proces.
  * Przechowuje aktualny `msgID` i licznik iteracji.
  * Implementuje główną pętlę logiki (F2).
  * Wykorzystuje `RobotClient` i `LLMClient`.
  * Implementuje logikę wykrywania flagi i obsługę limitu iteracji.
* **`main.py` / `app.py`:**
  * Punkt wejściowy aplikacji.
  * Inicjalizuje obiekty (konfigurację, klientów, kontroler).
  * Uruchamia główny proces weryfikacji.
  * Obsługuje podstawowe logowanie i przechwytywanie wyjątków na najwyższym poziomie.
* **`logger_setup.py` (opcjonalnie):**
  * Konfiguracja loggera (format, poziom logowania).

### 6. Przepływ Danych / Workflow

(Diagram `sequenceDiagram` pozostaje koncepcyjnie taki sam, ale wewnętrzna logika kroku "Sprawdź flagę w odpowiedzi" w bloku `Controller` będzie teraz bardziej precyzyjna, używając nowego formatu flagi do jej wykrycia i ekstrakcji).

```mermaid
sequenceDiagram
    participant User
    participant App_Main as Aplikacja (main.py)
    participant Config as Konfiguracja (AppConfig)
    participant Controller as Kontroler (VerificationController)
    participant RobotClient as Klient API Robota
    participant LLMClient as Klient API LLM
    participant RobotAPI as Zewnętrzne API Robota
    participant OpenAI_API as Zewnętrzne API OpenAI

    User->>App_Main: Uruchamia aplikację
    App_Main->>Config: Wczytaj konfigurację (.env)
    Config-->>App_Main: Konfiguracja wczytana
    App_Main->>Controller: Inicjalizuj (z config, RobotClient, LLMClient)
    Controller->>RobotClient: send_message(text="READY", msg_id=0)
    RobotClient->>RobotAPI: POST /verify (READY, 0)
    RobotAPI-->>RobotClient: Odpowiedź JSON (np. Pytanie1, msgID1)
    RobotClient-->>Controller: Odpowiedź od robota (Pytanie1, msgID1)

    loop Aż do znalezienia flagi lub limitu iteracji
        Controller->>Controller: Analizuj odpowiedź robota: szukaj `{{FLG:([^}]+)}}` w polu `text`
        alt Flaga nieznaleziona
            Controller->>LLMClient: get_answer(question=Pytanie1, system_prompt=...)
            LLMClient->>OpenAI_API: Zapytanie do GPT-4o
            OpenAI_API-->>LLMClient: Odpowiedź LLM (Odpowiedź1)
            LLMClient-->>Controller: Odpowiedź LLM (Odpowiedź1)
            Controller->>RobotClient: send_message(text=Odpowiedź1, msg_id=msgID1)
            RobotClient->>RobotAPI: POST /verify (Odpowiedź1, msgID1)
            RobotAPI-->>RobotClient: Odpowiedź JSON (np. Pytanie2, msgID2)
            RobotClient-->>Controller: Odpowiedź od robota (Pytanie2, msgID2)
            Controller->>Controller: Zwiększ licznik iteracji
        else Flaga znaleziona (wyodrębniono NAZWAFLAGI)
            Controller-->>App_Main: Flaga znaleziona! (NAZWAFLAGI)
            App_Main->>User: Wyświetl "Sukces! Zdobyto flagę: [NAZWAFLAGI]"
            App_Main->>App_Main: Zakończ działanie
            break
        end

        alt Limit iteracji osiągnięty
             Controller-->>App_Main: Limit iteracji osiągnięty
             App_Main->>User: Wyświetl "NIE ODNALEZIONO FLAGI - SPRAWDŹ LOGIKĘ AGENTA"
             App_Main->>App_Main: Zakończ działanie
             break
        end
    end

    Note over Controller, RobotClient, LLMClient: W przypadku błędu API, ponów 1 raz. Jeśli błąd nadal, zakończ z logiem.
```

### 7. Obsługa Błędów

* **Błędy API (Robot/LLM):**
  * Próba ponowienia: 1 raz.
  * Po nieudanej ponownej próbie: logowanie (kod błędu, wiadomość, źródło) i zakończenie aplikacji z odpowiednim komunikatem.
* **Błędy walidacji Pydantic (jeśli używane):**
  * Logowanie błędu walidacji i zakończenie aplikacji (traktowane jako błąd wewnętrzny/konfiguracyjny).
* **Timeouty:**
  * Timeout dla API LLM (10s) będzie obsługiwany. Jeśli wystąpi, traktowany jak błąd API.
* **Nieoczekiwane wyjątki:**
  * Przechwytywanie na najwyższym poziomie, logowanie stack trace i zakończenie aplikacji.

### 8. Kryteria Akceptacji

* Aplikacja pomyślnie inicjuje konwersację z API robota.
* Aplikacja poprawnie wymienia wiadomości z API robota i API LLM, zarządzając `msgID`.
* Aplikacja stosuje predefiniowany system prompt dla LLM.
* Aplikacja poprawnie identyfikuje i reaguje na pytania wymagające odpowiedzi zgodnych z normą RoboISO 2230.
* Aplikacja poprawnie udziela prawdziwych odpowiedzi na pozostałe pytania.
* **Aplikacja poprawnie wykrywa flagę w formacie `{{FLG:NAZWAFLAGI}}` w odpowiedzi robota, wyodrębnia jej treść (`NAZWAFLAGI`) i kończy działanie, wyświetlając wyodrębnioną treść flagi.**
* Aplikacja kończy działanie po 10 nieudanych iteracjach z odpowiednim komunikatem.
* Aplikacja obsługuje błędy komunikacyjne zgodnie ze specyfikacją (1 ponowienie, potem log i wyjście).
* Konfiguracja API jest wczytywana z pliku `.env`.
* Logi aplikacji są generowane zgodnie ze specyfikacją.
* Kod jest napisany w Pythonie, podzielony na klasy i zgodny z dobrymi praktykami.
* Zależności są zarządzane przez `uv`.

### 9. Poza Zakresem (Future Considerations)

* Bardziej zaawansowane strategie ponawiania prób (np. exponential backoff).
* Interfejs graficzny użytkownika (GUI).
* Obsługa wielu różnych "osobowości" agenta lub system promptów.
* Możliwość dynamicznego ładowania zasad weryfikacji.
* Zaawansowane testy integracyjne.

### 10. Glosariusz

* **API:** Application Programming Interface - interfejs programistyczny aplikacji.
* **LLM:** Large Language Model - wielki model językowy (np. GPT-4o).
* **msgID:** Identyfikator wiadomości używany do śledzenia konwersacji.
* **JSON:** JavaScript Object Notation - format wymiany danych.
* **HTTPS:** Hypertext Transfer Protocol Secure - protokół komunikacyjny.
* **.env:** Plik przechowujący zmienne środowiskowe.
* **DRY, KISS, YAGNI, SOLID:** Zasady projektowania oprogramowania.
* **Pydantic:** Biblioteka Python do walidacji danych.
* **uv:** Menedżer pakietów dla Pythona.
