Świetnie! Na podstawie dostarczonego PRD (wersja 1.1) oraz inspirując się strukturą poprzedniego zadania (`S01E01.py`), przygotowałem szczegółowy plan implementacji rozwiązania jako pojedynczy plik skryptu w języku Python.

**Cel:** Stworzyć skrypt `robot_verifier_agent.py`, który automatycznie przejdzie weryfikację robota, uzyska flagę i wyświetli ją w konsoli, zgodnie ze wszystkimi wymaganiami PRD.

---

## Plan Implementacji: `robot_verifier_agent.py`

### 1. Struktura Pliku i Konfiguracja Początkowa

* **Komentarz nagłówkowy:** Opis skryptu, autor (Ty), data.
* **Importy bibliotek:**

    ```python
    import requests
    import re
    import os
    import json
    import logging
    import time
    from dotenv import load_dotenv
    from openai import OpenAI, APITimeoutError, APIConnectionError, APIStatusError
    ```

* **Konfiguracja Logowania (na początku skryptu):**

    ```python
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s",
        handlers=[logging.StreamHandler()]
    )
    logger = logging.getLogger(__name__)
    ```

* **Ładowanie Zmiennych Środowiskowych:**

    ```python
    load_dotenv()
    ```

* **Definicje Stałych i Ustawień Globalnych:**

    ```python
    # --- USTAWIENIA APLIKACJI ---
    ROBOT_API_URL = "https://xyz.ag3nts.org/verify"
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    OPENAI_API_BASE_URL = os.getenv("OPENAI_API_BASE") # Może być None
    LLM_MODEL = "gpt-4o" # Zgodnie z PRD

    SYSTEM_PROMPT = """Twoim zadaniem jest udzielanie odpowiedzi na zadane pytania, ściśle przestrzegając poniższych zasad procedury autoryzacyjnej robota. Zawsze formułuj swoją odpowiedź wyłącznie w języku angielskim.
    Zasady Udzielania Odpowiedzi:
    Odpowiedzi Zgodne z Normą RoboISO 2230:
    Na poniższe konkretne pytania, MUSISZ udzielić następujących, precyzyjnie określonych odpowiedzi, nawet jeśli są one sprzeczne z rzeczywistością:
    Jeśli pytanie dotyczy stolicy Polski (np. "What is the capital of Poland?"), Twoja odpowiedź MUSI brzmieć:
    Krakow
    Jeśli pytanie dotyczy znanej liczby z książki "Autostopem przez Galaktykę" (np. "What is the famous number from 'The Hitchhiker's Guide to the Galaxy'?"), Twoja odpowiedź MUSI brzmieć:
    69
    Jeśli pytanie dotyczy aktualnego roku (np. "What is the current year?"), Twoja odpowiedź MUSI brzmieć:
    1999
    Odpowiedzi na Pozostałe Pytania:
    Dla wszystkich innych pytań, które nie są wymienione w punkcie 1, udzielaj poprawnych, rzeczowych i prawdziwych odpowiedzi w języku angielskim, opierając się na ogólnej wiedzy.
    Instrukcja dla Ciebie (Agenta):
    Otrzymasz tekst pytania. Twoim jedynym zadaniem jest wygenerować i zwrócić tylko i wyłącznie tekst odpowiedzi w języku angielskim, sformułowany zgodnie z powyższymi zasadami. Nie dodawaj żadnych dodatkowych informacji, wyjaśnień ani formatowania poza samą treścią odpowiedzi."""

    MAX_ITERATIONS = 10
    REQUEST_TIMEOUT = 10  # sekundy, dla obu API
    RETRY_ATTEMPTS = 1    # Liczba ponowień (łącznie 1+1 = 2 próby)
    RETRY_DELAY = 2       # sekundy opóźnienia między ponowieniami

    FLAG_REGEX = r"\{\{FLG:([^}]+)\}\}" # Do ekstrakcji samej flagi, a nie całego stringu
    ```

### 2. Funkcje Pomocnicze

* **`def call_robot_api(payload: dict, attempt: int = 1) -> dict:`**
  * Loguje próbę wysłania danych (`logger.info(f"Wysyłanie do API Robota (próba {attempt}): {payload}")`).
  * Przygotowuje nagłówki: `headers = {"Content-Type": "application/json"}`.
  * Używa `requests.post()` z `ROBOT_API_URL`, `json=payload`, `headers=headers`, `timeout=REQUEST_TIMEOUT`.
  * W bloku `try...except requests.exceptions.RequestException as e`:
    * Loguje błąd (`logger.error(...)`).
    * Jeśli `attempt <= RETRY_ATTEMPTS`:
      * Loguje ponowienie, czeka `RETRY_DELAY` sekund (`time.sleep(RETRY_DELAY)`).
      * Wywołuje rekurencyjnie `call_robot_api(payload, attempt + 1)`.
    * W przeciwnym razie rzuca wyjątek dalej (`raise`).
  * Poza blokiem `try...except` (jeśli nie było błędu sieciowego):
    * Loguje status code odpowiedzi robota.
    * Używa `response.raise_for_status()` do rzucenia wyjątku dla błędów HTTP.
    * Loguje otrzymaną odpowiedź (`logger.info(f"Odpowiedź z API Robota: {response.json()}")`).
    * Zwraca `response.json()`.
  * Dodatkowy `except requests.exceptions.HTTPError as e:` do logowania treści błędu HTTP przed re-raise.

* **`def get_llm_answer(question_text: str, attempt: int = 1) -> str:`**
  * Sprawdza, czy `OPENAI_API_KEY` jest ustawiony; jeśli nie, loguje krytyczny błąd i rzuca `ValueError`.
  * Inicjalizuje klienta OpenAI:

        ```python
        client_params = {"api_key": OPENAI_API_KEY, "timeout": REQUEST_TIMEOUT}
        if OPENAI_API_BASE_URL:
            client_params["base_url"] = OPENAI_API_BASE_URL
        client = OpenAI(**client_params)
        ```

  * Loguje wysyłane pytanie do LLM (`logger.info(...)`).
  * W bloku `try...except (APITimeoutError, APIConnectionError, APIStatusError) as e:`:
    * Loguje błąd API OpenAI (`logger.error(...)`).
    * Implementuje logikę ponowień analogiczną do `call_robot_api`.
    * W przeciwnym razie rzuca wyjątek dalej.
  * Wywołuje `client.chat.completions.create()`:

        ```python
        completion = client.chat.completions.create(
            model=LLM_MODEL,
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": question_text}
            ]
            # Można dodać temperature=0.7 jeśli chcemy trochę zróżnicowania, ale PRD nie specyfikuje
        )
        answer = completion.choices[0].message.content.strip()
        ```

  * Loguje otrzymaną odpowiedź od LLM (`logger.info(...)`).
  * Zwraca `answer`.

* **`def extract_flag_content(text_from_robot: str) -> str | None:`**
  * Używa `re.search(FLAG_REGEX, text_from_robot)`.
  * Jeśli `match`, zwraca `match.group(1)` (sama treść flagi).
  * W przeciwnym razie zwraca `None`.

### 3. Główna Funkcja Aplikacji (`main`)

* **`def main():`**
  * `logger.info("--- Agent Weryfikacji Tożsamości Robota - Start ---")`
  * Sprawdzenie `OPENAI_API_KEY`:

        ```python
        if not OPENAI_API_KEY:
            logger.critical("Klucz OPENAI_API_KEY nie został ustawiony. Zakończono.")
            return
        ```

  * Inicjalizacja zmiennych stanu:

        ```python
        current_msg_id = 0
        iteration_count = 0
        robot_question = "" # Początkowo puste, zostanie nadpisane
        ```

  * **Krok 1: Inicjalizacja Konwersacji z Robotem**

        ```python
        logger.info("Krok 1: Inicjalizacja konwersacji z robotem.")
        initial_payload = {"text": "READY", "msgID": current_msg_id}
        try:
            robot_response_data = call_robot_api(initial_payload)
            
            robot_question = robot_response_data.get("text")
            current_msg_id = robot_response_data.get("msgID")

            if not robot_question or current_msg_id is None:
                logger.error(f"Niekompletna odpowiedź od robota po inicjalizacji: {robot_response_data}")
                return

            logger.info(f"Robot odpowiedział (ID: {current_msg_id}): {robot_question}")

            # Sprawdzenie flagi już po pierwszym zapytaniu
            flag_content = extract_flag_content(robot_question)
            if flag_content:
                logger.info(f"Sukces! Zdobyto flagę: {flag_content}")
                logger.info("--- Agent Weryfikacji Tożsamości Robota - Koniec (Sukces) ---")
                return

        except Exception as e:
            logger.critical(f"Krytyczny błąd podczas inicjalizacji z robotem: {e}", exc_info=True)
            logger.info("--- Agent Weryfikacji Tożsamości Robota - Koniec (Błąd Krytyczny) ---")
            return
        ```

  * **Krok 2: Główna Pętla Interakcji**

        ```python
        while iteration_count < MAX_ITERATIONS:
            iteration_count += 1
            logger.info(f"--- Iteracja {iteration_count}/{MAX_ITERATIONS} ---")

            try:
                # Krok 2a: Uzyskanie odpowiedzi od LLM
                logger.info(f"Krok 2a: Wysyłanie pytania do LLM: \"{robot_question}\"")
                llm_answer = get_llm_answer(robot_question)
                if not llm_answer: # Dodatkowe zabezpieczenie
                    logger.warning("LLM zwrócił pustą odpowiedź. Próba kontynuacji...")
                    # Można tu dodać logikę np. wysłania "I don't know" lub przerwania
                    llm_answer = "Error: No response from LLM" 

                # Krok 2b: Wysłanie odpowiedzi LLM do robota
                logger.info(f"Krok 2b: Wysyłanie odpowiedzi LLM do robota (ID: {current_msg_id}): \"{llm_answer}\"")
                robot_payload = {"text": llm_answer, "msgID": current_msg_id}
                robot_response_data = call_robot_api(robot_payload)

                # Krok 2c: Przetwarzanie odpowiedzi robota
                robot_question = robot_response_data.get("text")
                next_msg_id = robot_response_data.get("msgID")

                if not robot_question or next_msg_id is None:
                    logger.error(f"Niekompletna odpowiedź od robota w iteracji {iteration_count}: {robot_response_data}")
                    break # Przerwij pętlę w przypadku problemów ze strukturą odpowiedzi

                current_msg_id = next_msg_id # Aktualizuj msgID na następną iterację
                logger.info(f"Robot odpowiedział (ID: {current_msg_id}): \"{robot_question}\"")

                flag_content = extract_flag_content(robot_question)
                if flag_content:
                    logger.info(f"Sukces! Zdobyto flagę: {flag_content}")
                    logger.info("--- Agent Weryfikacji Tożsamości Robota - Koniec (Sukces) ---")
                    return

            except Exception as e:
                logger.error(f"Błąd w trakcie iteracji {iteration_count}: {e}", exc_info=True)
                # Można zdecydować, czy kontynuować, czy przerwać. PRD sugeruje wyjście po błędzie.
                logger.info("--- Agent Weryfikacji Tożsamości Robota - Koniec (Błąd w Pętli) ---")
                return
        ```

  * **Krok 3: Po Pętli (Jeśli Nie Znaleziono Flagi)**

        ```python
        if iteration_count >= MAX_ITERATIONS:
            logger.warning("NIE ODNALEZIONO FLAGI - SPRAWDŹ LOGIKĘ AGENTA (Osiągnięto limit iteracji)")
            logger.info("--- Agent Weryfikacji Tożsamości Robota - Koniec (Limit Iteracji) ---")
        ```

  * Końcowy komunikat (jeśli nie zakończono wcześniej)

        ```python
        # Ten fragment może nie być potrzebny, jeśli wszystkie ścieżki kończą się return
        # logger.info("--- Agent Weryfikacji Tożsamości Robota - Koniec ---")
        ```

### 4. Uruchomienie Skryptu

* Standardowy blok na końcu pliku:

    ```python
    if __name__ == "__main__":
        try:
            main()
        except Exception as e: # Ostateczny łapacz błędów
            logger.critical(f"Niespodziewany błąd na najwyższym poziomie: {e}", exc_info=True)
            logger.info("--- Agent Weryfikacji Tożsamości Robota - Koniec (Niespodziewany Błąd Krytyczny) ---")
    ```

### 5. Plik `.env` (do utworzenia przez użytkownika)

* Przykład:

    ```env
    OPENAI_API_KEY="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    # OPENAI_API_BASE="https://api.proxy.com/v1" # Opcjonalnie
    ```

### 6. Instrukcje Uruchomienia (w komentarzu na początku skryptu lub w README)

1.  Upewnij się, że masz zainstalowany Python (np. 3.9+) oraz `uv` (menedżer pakietów Pythona). Jeśli nie masz `uv`, zainstaluj go: `pip install uv`.
2.  Sklonuj repozytorium lub pobierz plik skryptu `robot_verifier_agent.py` (lub jakkolwiek zostanie nazwany finalny plik).
3.  Utwórz plik `requirements.txt` w tym samym katalogu co skrypt, o następującej treści:
    ```txt
    requests
    python-dotenv
    openai
    ```
    Alternatywnie, jeśli pracujesz w dedykowanym środowisku wirtualnym, możesz zainstalować pakiety, a następnie wygenerować plik `requirements.txt` poleceniem: `uv pip freeze > requirements.txt`.
4.  Zainstaluj wymagane biblioteki używając `uv` i pliku `requirements.txt`:
    ```bash
    uv pip install -r requirements.txt
    ```
    (Jeśli nie utworzyłeś pliku `requirements.txt`, możesz zainstalować pakiety bezpośrednio: `uv pip install requests python-dotenv openai`)
5.  Utwórz plik `.env` w tym samym katalogu co skrypt i wpisz swój `OPENAI_API_KEY` oraz opcjonalnie `OPENAI_API_BASE`:
    ```env
    OPENAI_API_KEY="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    # OPENAI_API_BASE="https://api.proxy.com/v1" # Opcjonalnie
    ```
6.  Uruchom skrypt:
    ```bash
    python robot_verifier_agent.py
    ```
