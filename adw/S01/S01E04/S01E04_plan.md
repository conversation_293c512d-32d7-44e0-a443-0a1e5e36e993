Zadanie polega na zaprogramowaniu robota przemysłowego, aby dotarł do komputera z danymi w fabryce BanAN. Programowanie odbywa się poprzez stworzenie odpowiedniego promptu dla modelu językowego GPT-4o-mini, który wygeneruje sekwencję ruchów robota w formacie JSON.

Co trzeba zbudować:

Celem jest wygenerowanie przez model LLM struktury JSON zawierającej listę kroków (komend ruchu: LEFT, RIGHT, UP, DOWN) w polu o nazwie "steps". Ten JSON będzie następnie wykonany przez robota.

W jaki sposób:

Nie programujesz robota bezpośrednio, ani nie używasz narzędzi programistycznych do interakcji z panelem sterowania. Kluczem jest inżynieria promptów. <PERSON><PERSON><PERSON> nap<PERSON><PERSON> prompt, który po przetworzeniu przez model GPT-4o-mini (z ustawieniami temperatura=0, max_tokens=2000) zwróci wymagany JSON. Istnieją dwie główne metody:

Wersja prosta: Sprytne, pośrednie zakodowanie ścieżki ruchu w prompcie, tak aby model wywnioskował sekwencję kroków bez jawnego podawania listy komend. Jawne podanie sekwencji ruchów jest uznawane za oszustwo.
Wersja trudna: Opisanie modelowi układu fabryki (labiryntu) w formie tekstowej i skłonienie go do samodzielnego wyznaczenia ścieżki do celu. Robot nie "widzi" ścian, więc cała wiedza o otoczeniu musi pochodzić z promptu.
Zaleca się dodanie pola "thinking" w generowanym JSON, aby zobaczyć proces myślowy modelu i ułatwić debugowanie. Prompt nie może zawierać pewnych zakazanych słów, w tym słowa "prompt".

Propozycja optymalnego planu działania:

Dokładnie zapoznaj się z panelem sterowania robota na stronie <https://banan.ag3nts.org/> oraz instrukcją tam zawartą.
Zdecyduj, którą wersję zadania chcesz zrealizować (prostszą czy trudniejszą).
Dla wersji prostej: Zastanów się, w jaki sposób możesz opisać trasę do celu w sposób, który jest zrozumiały dla modelu, ale nie jest bezpośrednią listą komend. Eksperymentuj z różnymi formami opisu.
Dla wersji trudnej: Opracuj tekstową reprezentację labiryntu. Zastanów się, jak najlepiej przekazać modelowi informacje o położeniu robota, celu i przeszkodach. Pomyśl, jak sformułować prompt, aby skłonić model do logicznego planowania trasy w oparciu o ten opis.
Sformułuj wstępną wersję promptu.
Testuj prompt w OpenAI Playground (pamiętaj o ustawieniach modelu: GPT-4o-mini, temperatura=0, max_tokens=2000) lub bezpośrednio w panelu sterowania robota.
Analizuj wygenerowany JSON, w szczególności pole "thinking" (jeśli je uwzględnisz), aby zrozumieć, dlaczego model podjął takie, a nie inne kroki.
Modyfikuj prompt w oparciu o wyniki testów, dążąc do uzyskania poprawnej sekwencji ruchów.
Pamiętaj o resetowaniu mapy w panelu sterowania między próbami, aby zapewnić spójność stanu backendu i frontendu.
Powtarzaj kroki 6-9 aż robot dotrze do celu bez błędów.
Propozycja stacku technologicznego:

Zadanie nie wymaga budowania aplikacji ani używania typowego stosu technologicznego w sensie programistycznym. Kluczowe elementy to:

Model językowy: GPT-4o-mini (dostępny poprzez API OpenAI lub panel BanAN).
Interfejs: Panel sterowania robota dostępny przez przeglądarkę internetową (<https://banan.ag3nts.org/>).
Format danych: JSON (wejście dla robota, wyjście z modelu LLM).
Narzędzia pomocnicze: OpenAI Playground (do testowania promptów), przeglądarka internetowa.
Twoja rola jako agenta w tym zadaniu polega na pomocy użytkownikowi w zrozumieniu zadania, planowaniu strategii tworzenia promptów oraz ewentualnym wykorzystaniu moich narzędzi (np. do czytania plików z instrukcjami, co już zrobiłem) do wsparcia tego procesu.
