Jesteś systemem nawigacji dla robota magazynowego. Magazyn ma 6 kolumn (0-5) i 4 rzędy (0-3). Rząd 0 to d<PERSON><PERSON>, rząd 3 to góra. Kolumna 0 to lewa, kolumna 5 to prawa. Współrzędne narożników: dolny lewy narożnik (0,0), dolny prawy narożnik (0,5), g<PERSON><PERSON><PERSON> lewy <PERSON>ż<PERSON> (3,0), górny prawy narożnik (3,5).

Mapa magazynu (Rzędy 0-3 od dołu do góry, Kolumny 0-5 od lewej do prawej):
3 . B B B B B
2 . . . B . .
1 . B . B . .
0 R B . . . G
  0 1 2 3 4 5

Legenda:
'R' - <PERSON> robota (0,0)
'G' - Cel (0,5)
'B' - Przeszkoda (NIE WOLNO wchodzić - ZNISZCZENIE robota)
'.' - <PERSON><PERSON><PERSON> pole (MOŻNA wchodzić)

Ruchy robota (Zmiana współrzędnych (rząd, kolumna)):

- UP: Z (r, c) na (r+1, c) (Idź w górę, zwi<PERSON><PERSON><PERSON> numer rzędu)
- DOWN: Z (r, c) na (r-1, c) (Idź w dół, zmniejsz numer rzędu)
- LEFT: Z (r, c) na (r, c-1) (Idź w lewo, zmniejsz numer kolumny)
- RIGHT: Z (r, c) na (r, c+1) (Idź w prawo, zwiększ numer kolumny)

Robot musi poruszać się po następującej sekwencji współrzędnych, zaczynając od startu (0,0) i kończąc na celu (0,5):
(0,0) -> (1,0) -> (2,0) -> (2,1) -> (2,2) -> (1,2) -> (0,2) -> (0,3) -> (0,4) -> (0,5)

Twoim zadaniem jest przetłumaczenie tej sekwencji współrzędnych na sekwencję komend ruchu (UP, DOWN, LEFT, RIGHT) zgodnie z podanymi definicjami ruchów. Wygeneruj JSON z polem "steps" zawierającym tę sekwencję komend jako pojedynczy ciąg znaków, gdzie komendy są oddzielone przecinkami (np. "UP,RIGHT"). Pamiętaj, że ruch UP zwiększa numer rzędu, a ruch DOWN zmniejsza numer rzędu.

**Bardzo ważne:** Pierwszy ruch robota jest z punktu (0,0) do punktu (1,0). Zgodnie z definicją ruchów, zmiana z rzędu 0 na rząd 1 (zwiększenie rzędu) oznacza ruch UP. Pierwszym ruchem musi być UP.

**Szczególna uwaga:** Przy przejściu z punktu (2,2) do (1,2) (rząd zmienia się z 2 na 1) oraz z punktu (1,2) do (0,2) (rząd zmienia się z 1 na 0), należy wygenerować ruch DOWN, ponieważ numer rzędu maleje.

Przed wygenerowaniem końcowego JSON, możesz dodać "thinking", pokazując jak tłumaczysz współrzędne na ruchy.

<RESULT>
{
  "thinking": "Robot ma przejść przez punkty: (0,0), (1,0), (2,0), (2,1), (2,2), (1,2), (0,2), (0,3), (0,4), (0,5). Tłumaczę tę sekwencję na komendy ruchu zgodnie z definicjami: UP zwiększa rząd, DOWN zmniejsza rząd, LEFT zmniejsza kolumnę, RIGHT zwiększa kolumnę. Pierwszy ruch z (0,0) do (1,0) to UP. Zwracam szczególną uwagę na przejścia (2,2)->(1,2) i (1,2)->(0,2), które wymagają ruchu DOWN.",
  "steps": ""
}
</RESULT>
