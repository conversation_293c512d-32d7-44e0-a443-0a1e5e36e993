"""
JSON Calibration File Corrector - S01E03

Skrypt automatycznie pobiera, analizuje, poprawia i wysyła plik kalibracyjny JSON.
Implementacja zgodna z wymaganiami zadania S01E03.

Autor: Augment Agent
Data: 2025-05-24
"""

# --- IMPORTY ---

import os
import re
import json
import logging
import time
import requests
import math
from typing import Dict, List, Any, Tuple, Optional, Union
from dotenv import load_dotenv
from openai import OpenAI

# --- KONFIGURACJA LOGOWANIA ---

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# --- ŁADOWANIE ZMIENNYCH ŚRODOWISKOWYCH ---

load_dotenv()

# --- USTAWIENIA APLIKACJI ---

AIDEVS_API_KEY = os.getenv("AIDEVS_API_KEY")
JSON_FILE_URL = f"https://c3ntrala.ag3nts.org/data/{AIDEVS_API_KEY}/json.txt"
REPORT_URL = "https://c3ntrala.ag3nts.org/report"
LOCAL_FILE_PATH = "json_data.txt"
TASK_NAME = "JSON"

# --- USTAWIENIA LLM ---

LLM_MODEL = "deepseek-ai/DeepSeek-V3-0324"
LLM_BASE_URL = "https://api.kluster.ai/v1"
LLM_API_KEY = os.getenv("KLUSTER_API_KEY")
MAX_TOKENS = 4000  # Zmniejszony limit tokenów
CHUNK_SIZE = 2000  # Znacznie mniejszy niż MAX_TOKENS, aby uniknąć przekroczenia limitu

# --- FUNKCJE POMOCNICZE ---

def download_json_file() -> bool:
    """
    Pobiera plik JSON z podanego URL i zapisuje lokalnie.

    Returns:
        bool: True jeśli pobieranie się powiodło, False w przeciwnym razie
    """
    try:
        logger.info(f"Pobieranie pliku JSON z: {JSON_FILE_URL}")
        response = requests.get(JSON_FILE_URL)
        response.raise_for_status()
        
        with open(LOCAL_FILE_PATH, 'w', encoding='utf-8') as file:
            file.write(response.text)
        
        logger.info(f"Plik JSON zapisany lokalnie jako: {LOCAL_FILE_PATH}")
        return True
    except Exception as e:
        logger.error(f"Błąd podczas pobierania pliku JSON: {e}")
        return False

def load_json_data() -> Dict:
    """
    Wczytuje dane JSON z lokalnego pliku.

    Returns:
        Dict: Dane JSON jako słownik Pythona
    """
    try:
        logger.info(f"Wczytywanie danych JSON z pliku: {LOCAL_FILE_PATH}")
        with open(LOCAL_FILE_PATH, 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        # Upewniamy się, że dane zawierają klucz API
        if "apikey" not in data or data["apikey"] != AIDEVS_API_KEY:
            logger.info("Dodawanie lub aktualizowanie klucza API w danych JSON")
            data["apikey"] = AIDEVS_API_KEY
        
        logger.info("Dane JSON wczytane pomyślnie")
        return data
    except Exception as e:
        logger.error(f"Błąd podczas wczytywania danych JSON: {e}")
        raise

def evaluate_expression(expression: str) -> Union[int, float]:
    """
    Oblicza wartość wyrażenia matematycznego.

    Args:
        expression: Wyrażenie matematyczne jako string (np. "45 + 86")
        
    Returns:
        Union[int, float]: Wynik obliczenia
    """
    try:
        # Usuwamy wszystko co nie jest liczbą lub operatorem
        clean_expr = re.sub(r'[^0-9\+\-\*\/\(\)\.]', '', expression)
        result = eval(clean_expr)
        
        # Jeśli wynik jest liczbą całkowitą, zwracamy int
        if isinstance(result, float) and result.is_integer():
            return int(result)
        return result
    except Exception as e:
        logger.error(f"Błąd podczas obliczania wyrażenia '{expression}': {e}")
        return None

def check_and_fix_calculations(data: Dict) -> Dict:
    """
    Sprawdza i poprawia błędy obliczeniowe w danych JSON.

    Args:
        data: Dane JSON jako słownik Pythona
        
    Returns:
        Dict: Poprawione dane JSON
    """
    logger.info("Rozpoczynanie sprawdzania i poprawiania obliczeń...")
    
    if "test-data" not in data:
        logger.warning("Brak klucza 'test-data' w danych JSON")
        return data
    
    test_data = data["test-data"]
    fixed_count = 0
    
    for item in test_data:
        # Sprawdzamy czy element zawiera pytanie z wyrażeniem matematycznym
        if "question" in item and isinstance(item["question"], str):
            question = item["question"]
            # Szukamy wyrażeń matematycznych w pytaniu
            expressions = re.findall(r'(\d+\s*[\+\-\*\/]\s*\d+)', question)
            
            if expressions:
                for expr in expressions:
                    correct_result = evaluate_expression(expr)
                    if correct_result is not None:
                        # Sprawdzamy czy odpowiedź jest poprawna
                        if "answer" in item:
                            current_answer = item["answer"]
                            if current_answer != correct_result:
                                logger.info(f"Poprawiam obliczenie: {expr} = {correct_result} (było: {current_answer})")
                                item["answer"] = correct_result
                                fixed_count += 1
                        else:
                            logger.info(f"Dodaję brakującą odpowiedź: {expr} = {correct_result}")
                            item["answer"] = correct_result
                            fixed_count += 1
    
    logger.info(f"Zakończono poprawianie obliczeń. Poprawiono {fixed_count} błędów.")
    data["test-data"] = test_data
    return data

def estimate_token_count(text: str) -> int:
    """
    Szacuje liczbę tokenów w tekście (przybliżenie).

    Args:
        text: Tekst do oszacowania
        
    Returns:
        int: Szacowana liczba tokenów
    """
    # Przybliżenie: 1 token ≈ 4 znaki
    return len(text) // 4

def chunk_test_data(test_data: List[Dict]) -> List[List[Dict]]:
    """
    Dzieli dane testowe na mniejsze części, które zmieszczą się w oknie kontekstowym LLM.

    Args:
        test_data: Lista danych testowych
        
    Returns:
        List[List[Dict]]: Lista podzielonych danych testowych
    """
    logger.info("Dzielenie danych testowych na mniejsze części...")
    
    chunks = []
    current_chunk = []
    current_token_count = 0
    
    # Zakładamy, że test_data zawiera już tylko pytania, które chcemy przetworzyć
    logger.info(f"Przygotowywanie do podziału {len(test_data)} pytań")
    
    for item in test_data:
        # Szacujemy liczbę tokenów dla tego elementu
        item_json = json.dumps(item)
        item_tokens = estimate_token_count(item_json)
        
        # Jeśli dodanie tego elementu przekroczyłoby limit, rozpocznij nowy chunk
        if current_token_count + item_tokens > CHUNK_SIZE and current_chunk:
            chunks.append(current_chunk)
            current_chunk = []
            current_token_count = 0
        
        current_chunk.append(item)
        current_token_count += item_tokens
    
    # Dodaj ostatni chunk, jeśli nie jest pusty
    if current_chunk:
        chunks.append(current_chunk)
    
    logger.info(f"Podzielono dane na {len(chunks)} części")
    return chunks

def get_llm_answers_for_chunk(chunk: List[Dict]) -> List[Dict]:
    """
    Wysyła zapytanie do LLM dla danej części danych testowych i uzupełnia odpowiedzi.
    Przetwarza pytania pojedynczo, aby uniknąć przekroczenia limitu tokenów.

    Args:
        chunk: Część danych testowych
        
    Returns:
        List[Dict]: Dane testowe z uzupełnionymi odpowiedziami
    """
    logger.info(f"Przetwarzanie chunka zawierającego {len(chunk)} pytań...")
    
    # Inicjalizacja klienta OpenAI
    try:
        client = OpenAI(
            api_key=LLM_API_KEY,
            base_url=LLM_BASE_URL
        )
    except Exception as e:
        logger.error(f"Błąd podczas inicjalizacji klienta OpenAI: {e}")
        return chunk
    
    # Tworzymy słownik do śledzenia już przetworzonych pytań
    processed_questions = {}
    
    # Przetwarzamy każde pytanie osobno, aby uniknąć przekroczenia limitu tokenów
    for i, item in enumerate(chunk):
        if "test" in item and "q" in item["test"] and item["test"]["a"] == "???":
            question = item["test"]["q"]
            
            # Sprawdzamy, czy to pytanie nie zostało już przetworzone
            if question in processed_questions:
                logger.info(f"Pytanie '{question[:30]}...' zostało już przetworzone. Używam zapisanej odpowiedzi.")
                item["test"]["a"] = processed_questions[question]
                continue
            
            logger.info(f"Przetwarzanie pytania {i+1}/{len(chunk)}: '{question[:50]}...'")
            
            try:
                # Dodajemy opóźnienie, aby uniknąć ograniczeń API
                time.sleep(1)
                
                # Wczytanie system promptu z pliku
                try:
                    system_prompt_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "system_prompt.txt")
                    with open(system_prompt_path, "r", encoding="utf-8") as f:
                        system_prompt = f.read()
                    logger.info(f"Wczytano system prompt z pliku: {system_prompt_path}")
                except Exception as e:
                    logger.error(f"Błąd podczas wczytywania system promptu: {e}")
                    # Awaryjny system prompt w przypadku błędu
                    system_prompt = "CRITICAL INSTRUCTION: Respond with ONLY ONE WORD OR EXPRESSION. No explanations, formatting, or additional context. Answer in ENGLISH ONLY."
                    logger.info("Używam awaryjnego system promptu")
                
                completion = client.chat.completions.create(
                    model=LLM_MODEL,
                    max_completion_tokens=100,  # Ograniczamy długość odpowiedzi jeszcze bardziej
                    temperature=0.1,  # Niższa temperatura dla bardziej deterministycznych odpowiedzi
                    top_p=1,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": question}
                    ]
                )
                
                answer = completion.choices[0].message.content.strip()
                
                # Dodatkowe przetwarzanie odpowiedzi, aby upewnić się, że jest zgodna z wymaganiami
                # Usuwamy wszystkie znaki specjalne, formatowanie i niepotrzebne elementy
                answer = re.sub(r'[*_#`~]', '', answer)  # Usuwamy znaki formatowania Markdown
                answer = re.sub(r'^(The answer is|Answer:|It is:|The system is|This is)', '', answer, flags=re.IGNORECASE).strip()
                answer = re.sub(r'^["\']|["\']$', '', answer).strip()  # Usuwamy cudzysłowy na początku i końcu
                
                # Jeśli odpowiedź zawiera kropkę lub przecinek, bierzemy tylko pierwszą część
                if '.' in answer:
                    answer = answer.split('.')[0].strip()
                if ',' in answer:
                    answer = answer.split(',')[0].strip()
                
                # Ograniczamy długość odpowiedzi, jeśli jest zbyt długa
                if len(answer) > 50:  # Znacznie zmniejszamy maksymalną długość
                    answer = answer[:47] + "..."
                
                # Zapisujemy odpowiedź w słowniku przetworzonych pytań
                processed_questions[question] = answer
                
                item["test"]["a"] = answer
                logger.info(f"Otrzymano odpowiedź: '{answer[:50]}...'")
                
            except Exception as e:
                logger.error(f"Błąd podczas uzyskiwania odpowiedzi dla pytania {i+1}: {e}")
                # W przypadku błędu, ustawiamy prostą odpowiedź
                answer = "Nie można uzyskać odpowiedzi z powodu błędu API."
                item["test"]["a"] = answer
                processed_questions[question] = answer
                
                # Jeśli to błąd limitu, dodajemy dłuższe opóźnienie
                if "exceeds the maximum allowed size" in str(e):
                    logger.warning("Wykryto przekroczenie limitu. Dodawanie dłuższego opóźnienia.")
                    time.sleep(5)
    
    return chunk

def process_test_questions(data: Dict) -> Dict:
    """
    Przetwarza wszystkie pytania testowe w danych JSON, dzieląc je na części
    i wysyłając do LLM.

    Args:
        data: Dane JSON jako słownik Pythona
        
    Returns:
        Dict: Dane JSON z uzupełnionymi odpowiedziami na pytania testowe
    """
    logger.info("Rozpoczynanie przetwarzania pytań testowych...")
    
    if "test-data" not in data:
        logger.warning("Brak klucza 'test-data' w danych JSON")
        return data
    
    # Usuwamy duplikaty pytań testowych
    # Tworzymy słownik, który będzie przechowywał unikalne pytania testowe
    unique_test_questions = {}
    
    # Najpierw identyfikujemy wszystkie unikalne pytania testowe
    for i, item in enumerate(data["test-data"]):
        if "test" in item and isinstance(item["test"], dict) and "q" in item["test"]:
            question_text = item["test"]["q"]
            # Używamy tekstu pytania jako klucza
            if question_text not in unique_test_questions:
                unique_test_questions[question_text] = {
                    "item": item,
                    "indices": [i],
                    "needs_processing": item["test"].get("a") == "???"
                }
            else:
                # Jeśli pytanie już istnieje, dodajemy indeks do listy
                unique_test_questions[question_text]["indices"].append(i)
    
    logger.info(f"Znaleziono {len(unique_test_questions)} unikalnych pytań testowych")
    
    # Identyfikujemy pytania otwarte (z odpowiedzią "???")
    open_questions = []
    for question_info in unique_test_questions.values():
        if question_info["needs_processing"]:
            open_questions.append(question_info["item"])
    
    logger.info(f"Znaleziono {len(open_questions)} unikalnych pytań otwartych do przetworzenia")
    
    if not open_questions:
        logger.info("Brak pytań otwartych do przetworzenia")
        return data
    
    # Dzielimy dane na mniejsze części
    chunks = chunk_test_data(open_questions)
    
    # Tworzymy słownik do śledzenia przetworzonych pytań
    processed_questions = {}
    
    # Przetwarzamy każdą część
    for i, chunk in enumerate(chunks):
        logger.info(f"Przetwarzanie części {i+1}/{len(chunks)}...")
        processed_chunk = get_llm_answers_for_chunk(chunk)
        
        # Dodajemy przetworzone pytania do słownika
        for processed_item in processed_chunk:
            if "test" in processed_item and "q" in processed_item["test"]:
                question_text = processed_item["test"]["q"]
                processed_questions[question_text] = processed_item
    
    # Aktualizujemy odpowiedzi we wszystkich duplikatach w oryginalnych danych
    for question_text, processed_item in processed_questions.items():
        if question_text in unique_test_questions:
            # Aktualizujemy wszystkie wystąpienia tego pytania
            for idx in unique_test_questions[question_text]["indices"]:
                # Kopiujemy tylko pole "a" z przetworzonego pytania
                data["test-data"][idx]["test"]["a"] = processed_item["test"]["a"]
                logger.info(f"Zaktualizowano odpowiedź dla pytania '{question_text[:30]}...' na pozycji {idx}")
    
    logger.info("Zakończono przetwarzanie pytań testowych")
    logger.info(f"Liczba przetworzonych unikalnych pytań: {len(processed_questions)}")
    return data

def send_report(corrected_data: Dict) -> Dict:
    """
    Wysyła poprawione dane JSON do API raportującego.

    Args:
        corrected_data: Poprawione dane JSON
        
    Returns:
        Dict: Odpowiedź od API raportującego
    """
    logger.info(f"Wysyłanie raportu do: {REPORT_URL}")
    
    # Upewniamy się, że dane zawierają klucz API
    if "apikey" not in corrected_data or corrected_data["apikey"] != AIDEVS_API_KEY:
        logger.info("Dodawanie lub aktualizowanie klucza API w danych przed wysłaniem")
        corrected_data["apikey"] = AIDEVS_API_KEY
    
    # Sprawdzamy, czy test-data zawiera pytania testowe
    if "test-data" in corrected_data:
        # Zliczamy pytania testowe
        test_questions_count = 0
        for item in corrected_data["test-data"]:
            if "test" in item and isinstance(item["test"], dict) and "q" in item["test"] and "a" in item["test"]:
                test_questions_count += 1
        
        logger.info(f"Liczba pytań testowych w danych: {test_questions_count}")
        
        # Sprawdzamy, czy wszystkie pytania mają odpowiedzi inne niż "???"
        unanswered_questions = 0
        for item in corrected_data["test-data"]:
            if "test" in item and "a" in item["test"] and item["test"]["a"] == "???":
                unanswered_questions += 1
        
        if unanswered_questions > 0:
            logger.warning(f"Znaleziono {unanswered_questions} pytań bez odpowiedzi")
        else:
            logger.info("Wszystkie pytania mają odpowiedzi")
    
    # Wyświetlamy strukturę danych przed wysłaniem (tylko dla debugowania)
    logger.info(f"Struktura danych przed wysłaniem: {json.dumps(corrected_data, indent=2)[:200]}...")
    logger.info(f"Liczba pytań testowych: {len(corrected_data.get('test-data', []))}")
    
    # Sprawdzamy, czy wszystkie pytania mają odpowiedzi
    for i, item in enumerate(corrected_data.get("test-data", [])):
        if "test" in item and "a" in item["test"]:
            logger.info(f"Pytanie {i+1}: '{item['test'].get('q', '')[:30]}...' -> Odpowiedź: '{item['test']['a']}'")
    
    # Upewniamy się, że struktura JSON jest poprawna
    # Sprawdzamy, czy corrected_data jest słownikiem i czy zawiera wymagane pola
    if not isinstance(corrected_data, dict):
        logger.error(f"corrected_data nie jest słownikiem: {type(corrected_data)}")
        raise ValueError("corrected_data musi być słownikiem")
    
    # Upewniamy się, że test-data jest listą
    if "test-data" in corrected_data and not isinstance(corrected_data["test-data"], list):
        logger.error(f"test-data nie jest listą: {type(corrected_data['test-data'])}")
        raise ValueError("test-data musi być listą")
    
    # Upewniamy się, że apikey jest stringiem
    if "apikey" in corrected_data and not isinstance(corrected_data["apikey"], str):
        logger.error(f"apikey nie jest stringiem: {type(corrected_data['apikey'])}")
        corrected_data["apikey"] = str(corrected_data["apikey"])
    
    # Sprawdzamy, czy wszystkie wartości liczbowe są poprawnie reprezentowane (nie jako stringi)
    if "test-data" in corrected_data:
        for item in corrected_data["test-data"]:
            if "answer" in item and isinstance(item["answer"], str):
                # Próbujemy przekonwertować stringi liczbowe na liczby
                try:
                    if item["answer"].isdigit() or (item["answer"].startswith('-') and item["answer"][1:].isdigit()):
                        item["answer"] = int(item["answer"])
                    elif item["answer"].replace('.', '', 1).isdigit() or (item["answer"].startswith('-') and item["answer"][1:].replace('.', '', 1).isdigit()):
                        item["answer"] = float(item["answer"])
                except Exception as e:
                    logger.warning(f"Nie można przekonwertować odpowiedzi '{item['answer']}' na liczbę: {e}")
    
    payload = {
        "task": TASK_NAME,
        "apikey": AIDEVS_API_KEY,
        "answer": corrected_data  # Przekazujemy cały obiekt jako answer
    }
    
    # Wyświetlamy pełną strukturę payload
    logger.info(f"Pełna struktura payload: {json.dumps(payload, indent=2)[:500]}...")
    
    try:
        # Używamy json.dumps i json.loads, aby upewnić się, że struktura JSON jest poprawna
        payload_json = json.dumps(payload)
        payload_dict = json.loads(payload_json)
        
        response = requests.post(REPORT_URL, json=payload_dict)
        
        # Wyświetlamy odpowiedź serwera nawet w przypadku błędu
        if response.status_code != 200:
            logger.error(f"Błąd HTTP: {response.status_code}")
            logger.error(f"Odpowiedź serwera: {response.text}")
        
        response.raise_for_status()
        
        result = response.json()
        logger.info(f"Raport wysłany pomyślnie. Status: {response.status_code}")
        return result
    except Exception as e:
        logger.error(f"Błąd podczas wysyłania raportu: {e}")
        if hasattr(e, 'response') and hasattr(e.response, 'text'):
            logger.error(f"Odpowiedź serwera: {e.response.text}")
        raise

def check_for_flag(response: Dict) -> Optional[str]:
    """
    Sprawdza, czy odpowiedź zawiera flagę.

    Args:
        response: Odpowiedź od API raportującego
        
    Returns:
        Optional[str]: Flaga, jeśli została znaleziona, None w przeciwnym razie
    """
    if not isinstance(response, dict):
        return None
    
    # Sprawdzamy różne możliwe klucze, które mogą zawierać flagę
    possible_keys = ["flag", "FLAG", "Flag", "message", "result", "data"]
    
    for key in possible_keys:
        if key in response:
            value = response[key]
            if isinstance(value, str):
                # Sprawdzamy, czy wartość zawiera wzorzec flagi {{FLG:...}}
                if "{{FLG:" in value and "}}" in value:
                    return value
                # Sprawdzamy inne możliwe formaty flag
                if "flag" in value.lower() or "flaga" in value.lower():
                    return value
    
    # Rekurencyjne przeszukiwanie zagnieżdżonych słowników
    for key, value in response.items():
        if isinstance(value, dict):
            flag = check_for_flag(value)
            if flag:
                return flag
    
    # Jeśli nie znaleźliśmy flagi, ale mamy wiadomość, zwracamy ją
    if "message" in response:
        return response["message"]
    
    return None

def save_backup(data: Dict, filename: str = "backup_data.json") -> None:
    """
    Zapisuje kopię zapasową danych.

    Args:
        data: Dane do zapisania
        filename: Nazwa pliku kopii zapasowej
    """
    try:
        with open(filename, 'w', encoding='utf-8') as file:
            json.dump(data, file, indent=2)
        logger.info(f"Kopia zapasowa zapisana jako: {filename}")
    except Exception as e:
        logger.error(f"Błąd podczas zapisywania kopii zapasowej: {e}")

# --- GŁÓWNA FUNKCJA ---

def main():
    """
    Główna funkcja programu.
    """
    logger.info("Rozpoczynanie programu S01E03 - JSON Calibration File Corrector")
    
    # Sprawdzenie, czy klucz API jest dostępny
    if not AIDEVS_API_KEY:
        logger.error("Brak klucza API. Ustaw zmienną środowiskową AIDEVS_API_KEY.")
        return
    
    if not LLM_API_KEY:
        logger.error("Brak klucza API dla LLM. Ustaw zmienną środowiskową LLM_API_KEY.")
        return
    
    try:
        # Krok 1: Pobieranie pliku JSON
        if not download_json_file():
            logger.error("Nie udało się pobrać pliku JSON. Kończenie programu.")
            return
        
        # Krok 2: Wczytywanie danych JSON
        data = load_json_data()
        
        # Zapisanie kopii zapasowej oryginalnych danych
        save_backup(data, "original_data.json")
        
        # Krok 3: Sprawdzanie i poprawianie obliczeń
        data = check_and_fix_calculations(data)
        
        # Zapisanie kopii zapasowej po poprawieniu obliczeń
        save_backup(data, "after_calculations_data.json")
        
        # Krok 4: Przetwarzanie pytań testowych
        data = process_test_questions(data)
        
        # Zapisanie kopii zapasowej po przetworzeniu pytań
        save_backup(data, "final_data.json")
        
        # Krok 5: Wysyłanie raportu
        response = send_report(data)
        
        # Krok 6: Sprawdzanie flagi
        flag = check_for_flag(response)
        if flag:
            # Sprawdzamy, czy flaga ma format {{FLG:...}}
            if "{{FLG:" in flag and "}}" in flag:
                logger.info(f"ZNALEZIONO FLAGĘ: {flag}")
                print(f"\n{'='*50}\nFLAGA: {flag}\n{'='*50}\n")
            else:
                # Sprawdzamy, czy w odpowiedzi jest flaga w formacie {{FLG:...}}
                flag_pattern = r'{{FLG:[^}]+}}'
                match = re.search(flag_pattern, json.dumps(response))
                if match:
                    flag = match.group(0)
                    logger.info(f"ZNALEZIONO FLAGĘ: {flag}")
                    print(f"\n{'='*50}\nFLAGA: {flag}\n{'='*50}\n")
                else:
                    logger.info(f"Odpowiedź zawiera potencjalną flagę: {flag}")
                    print(f"\n{'='*50}\nPOTENCJALNA FLAGA: {flag}\n{'='*50}\n")
        else:
            # Ostatnia próba znalezienia flagi w całej odpowiedzi
            response_str = json.dumps(response)
            flag_pattern = r'{{FLG:[^}]+}}'
            match = re.search(flag_pattern, response_str)
            if match:
                flag = match.group(0)
                logger.info(f"ZNALEZIONO FLAGĘ: {flag}")
                print(f"\n{'='*50}\nFLAGA: {flag}\n{'='*50}\n")
            else:
                logger.info(f"Nie znaleziono flagi w odpowiedzi: {response}")
                print(f"\n{'='*50}\nODPOWIEDŹ: {json.dumps(response, indent=2)}\n{'='*50}\n")
        
    except Exception as e:
        logger.error(f"Wystąpił błąd podczas wykonywania programu: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()