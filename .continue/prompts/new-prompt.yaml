name: AI_Devs3r <PERSON><PERSON><PERSON> Zada<PERSON>
version: 0.0.1
schema: v1

prompts:
  - name: Ana<PERSON><PERSON>
    description: <PERSON><PERSON><PERSON> zadania i plan działania
    prompt: |
    <PERSON><PERSON><PERSON> udział w szkoleniu w którym uczę się jak efektywnie budować oprogramowanie agentowe oparte na rozwiązaniach stosujących wielkie modele językowe (LLM).
    W ramach tego szkolenia otrzymałem następujące zadanie.
    Oto jego treść {{ treść_zadania }} 
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, co mam tutaj zbu<PERSON>, w jaki spos<PERSON>b oraz zasugeruj, jakich narzędzi najlepiej używać?
    Nie przygotowuj jeszcze żadnej implementacji.
    Na razie wytłumacz mi tylko dokładnie o co chodzi w zadaniu, nakreśl propozycję optymalnego planu działania celem wykonania zadania i opracuj propozycję stacku technologicznego.